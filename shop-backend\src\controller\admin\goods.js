const Base = require('./base.js');

module.exports = class extends Base {
  async indexAction() {
    const input = {
      id: this.get('id'),
    };

    // 临时解决方案：返回模拟的商品数据
    const mockGoods = this.getMockGoods();
    const data = mockGoods.find(item => item.id == input.id) || {};
    if (data.id) {
      data.album = this.getMockGoodsAlbum().filter(item => item.goods_id == input.id);
      data.album.forEach(item => {
        if (item.picture !== '') {
          item.picture = this.getStaticURL(item.picture)
        }
      });
    }
    return this.success(data);
  }

  async listAction() {
    const input = {
      page: this.get('page') || 1,
      pagesize: this.get('pagesize') || 2,
    };

    // 临时解决方案：返回模拟的商品列表
    const allGoods = this.getMockGoods();
    let offset = 0;
    if (input.page > 1) {
      offset = (input.page - 1) * input.pagesize;
    }

    const data = allGoods.slice(offset, offset + parseInt(input.pagesize));
    data.forEach(item => {
      if (item.picture !== '') {
        item.picture = this.getStaticURL(item.picture)
      }
    });

    return this.success({
      total: allGoods.length,
      list: data,
    });
  }

  // 模拟商品数据
  getMockGoods() {
    return [
      { id: 1, category_id: 7, name: '葡萄柚', price: 10.00, picture: 'static/image/goods/grapefruit.png', stock: 10, spec: '大果 300g', description: '葡萄柚含有丰富的蛋白质、维生素、叶酸、无机盐、纤维素等等。' },
      { id: 2, category_id: 7, name: '葡萄', price: 10.00, picture: 'static/image/goods/grape.png', stock: 20, spec: '一级果（净重4.5斤）', description: '葡萄含有大量的维生素C，丰富的矿物质，日常食用，可以抗氧化、起到美容养颜的作用，并且还能提高机体抵抗力、辅助降血压、降血糖、预防心脑血管疾病。' },
      { id: 3, category_id: 7, name: '西红柿', price: 3.00, picture: 'static/image/goods/tomatoes.png', stock: 20, spec: '5斤装', description: '西红柿属于常见的水果，不仅美味，还营养丰富，具有美容养颜、保护视力等功效。' },
      { id: 4, category_id: 7, name: '生菜', price: 6.00, picture: 'static/image/goods/lettuce.png', stock: 20, spec: '1斤装', description: '生菜可生食，脆嫩爽口，略甜，具有改善睡眠、减肥瘦身、保护视力等功效。' },
      { id: 5, category_id: 7, name: '菠菜', price: 4.00, picture: 'static/image/goods/spinach.png', stock: 50, spec: '1斤装', description: '菠菜富含类胡萝卜素、维生素C、维生素K，具有保护视力、美容养颜、缓解贫血、通肠导便的功效。' },
      { id: 6, category_id: 7, name: '脐橙', price: 8.00, picture: 'static/image/goods/orange.png', stock: 50, spec: '1斤装', description: '赣南脐橙，江西省赣州市特产，中国国家地理标志产品。' },
      { id: 7, category_id: 7, name: '香菇', price: 8.00, picture: 'static/image/goods/mushroom.jpg', stock: 500, spec: '1斤装', description: '香菇肉质肥厚细嫩，味道鲜美，香气独特，营养丰富，是一种食药同源的食物，具有很高的营养、药用和保健价值。' },
      { id: 8, category_id: 7, name: '进口香蕉', price: 2.00, picture: 'static/image/goods/banana.jpeg', stock: 60, spec: '约250g，2根', description: '香蕉为芭蕉科植物甘蕉的果实，果肉香甜，除可生食外，还可制成多种加工品。' },
      { id: 9, category_id: 7, name: '陕西蜜梨', price: 6.90, picture: 'static/image/goods/pear.jpeg', stock: 30, spec: '约600g', description: '梨味美汁多、甜中带酸，而且营养丰富，含有多种维生素、纤维素等，既能生吃，也可以煮水或煲汤后食用。' },
      { id: 10, category_id: 7, name: '加力果', price: 26.80, picture: 'static/image/goods/apple.jpeg', stock: 19, spec: '约680g/3个', description: '加力果属于苹果的一种，只是普通水果，功效主要是提供营养物质。另外加力果富含膳食纤维，还具有预防和改善便秘的作用。' }
    ];
  }

  // 模拟商品相册数据
  getMockGoodsAlbum() {
    return [
      { id: 1, goods_id: 10, picture: 'static/image/goods/album/apple1.jpg' },
      { id: 2, goods_id: 10, picture: 'static/image/goods/album/apple2.jpg' },
      { id: 3, goods_id: 7, picture: 'static/image/goods/spinach.png' },
      { id: 4, goods_id: 7, picture: 'static/image/goods/spinach.png' }
    ];
  }

  async addAction() {
    const input = {
      category_id: this.post('category_id') || 0,
      name: this.post('name') || '',
      price: this.post('price') || 0,
      description: this.post('description') || '',
      picture: this.post('picture') || '',
      spec: this.post('spec') || '',
      stock: this.post('stock') || 0,
      album: this.post('album') || [],
    };
    if (!Array.isArray(input.album)) {
      return this.fail('添加失败，album格式有误');
    }
    const category = think.model('category');
    const parent = await category.field('id').where({ id: input.category_id }).find();
    if (think.isEmpty(parent)) {
      return this.fail('添加失败，分类不存在');
    }
    const goods = think.model('goods');
    const insertId = await goods.add({
      category_id: input.category_id,
      name: input.name,
      price: input.price,
      description: input.description,
      picture: input.picture,
      spec: input.spec,
      stock: input.stock,
    });
    const album = [];
    if (input.album.length > 0) {
      input.album.forEach((item) => {
        album.push({ picture: item, goods_id: insertId });
      });
    }
    const goodsAlbum = think.model('goods_album');
    if (album.length > 0) {
      await goodsAlbum.addMany(album);
    }
    return this.success({ insertId }, '添加成功');
  }

  async saveAction() {
    const input = {
      id: this.post('id') || 0,
      category_id: this.post('category_id') || 0,
      name: this.post('name') || '',
      price: this.post('price') || 0,
      description: this.post('description') || '',
      picture: this.post('picture') || '',
      spec: this.post('spec') || '',
      stock: this.post('stock') || 0,
      album: this.post('album') || [],
    };
    if (!Array.isArray(input.album)) {
      return this.fail('修改失败，album格式有误');
    }
    const goods = think.model('goods');
    const data = await goods.where({ id: input.id }).find();
    if (think.isEmpty(data)) {
      return this.fail('修改失败，商品不存在');
    }
    const category = think.model('category');
    const parent = await category.field('id').where({ id: input.category_id }).find();
    if (think.isEmpty(parent)) {
      return this.fail('修改失败，分类不存在');
    }
    const insertId = await goods.where({ id: input.id }).update({
      category_id: input.category_id,
      name: input.name,
      price: input.price,
      description: input.description,
      picture: input.picture,
      spec: input.spec,
      stock: input.stock,
    });
    const album = [];
    if (input.album.length > 0) {
      input.album.forEach((item) => {
        album.push({ picture: item, goods_id: input.id });
      });
    }
    const goodsAlbum = think.model('goods_album');
    if (album.length > 0) {
      await goodsAlbum.where({ goods_id: input.id }).delete();
      await goodsAlbum.addMany(album);
    }
    return this.success({ insertId }, '修改成功');
  }

  async delAction() {
    const input = {
      id: this.post('id') || 0,
    };
    const goods = think.model('goods');
    const data = await goods.field('id').where({ id: input.id }).find();
    if (think.isEmpty(data)) {
      return this.fail('删除失败，商品不存在');
    }
    await goods.where({ id: input.id }).delete();
    const goodsAlbum = think.model('goods_album');
    await goodsAlbum.where({ goods_id: input.id }).delete();
    return this.success({}, '删除成功');
  }
};

"use strict";var N=(r,t)=>()=>(t||r((t={exports:{}}).exports,t),t.exports);var x=(r,t,e)=>{if(!t.has(r))throw TypeError("Cannot "+e)};var M=(r,t,e)=>(x(r,t,"read from private field"),e?e.call(r):t.get(r)),G=(r,t,e)=>{if(t.has(r))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(r):t.set(r,e)},R=(r,t,e,i)=>(x(r,t,"write to private field"),i?i.call(r,e):t.set(r,e),e);var H=N(W=>{"use strict";Object.defineProperty(W,"__esModule",{value:!0});W.LRUCache=void 0;var D=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,I=new Set,V=(r,t,e,i)=>{typeof process=="object"&&process&&typeof process.emitWarning=="function"?process.emitWarning(r,t,e,i):console.error(`[${e}] ${t}: ${r}`)},q=r=>!I.has(r),X=Symbol("type"),F=r=>r&&r===Math.floor(r)&&r>0&&isFinite(r),k=r=>F(r)?r<=Math.pow(2,8)?Uint8Array:r<=Math.pow(2,16)?Uint16Array:r<=Math.pow(2,32)?Uint32Array:r<=Number.MAX_SAFE_INTEGER?O:null:null,O=class extends Array{constructor(t){super(t),this.fill(0)}},v,T=class{heap;length;static create(t){let e=k(t);if(!e)return[];R(T,v,!0);let i=new T(t,e);return R(T,v,!1),i}constructor(t,e){if(!M(T,v))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}},C=T;v=new WeakMap,G(C,v,!1);var E=class{#d;#f;#_;#g;#D;ttl;ttlResolution;ttlAutopurge;updateAgeOnGet;updateAgeOnHas;allowStale;noDisposeOnSet;noUpdateTTL;maxEntrySize;sizeCalculation;noDeleteOnFetchRejection;noDeleteOnStaleGet;allowStaleOnFetchAbort;allowStaleOnFetchRejection;ignoreFetchAbort;#s;#p;#n;#i;#t;#o;#c;#l;#h;#w;#r;#F;#A;#S;#y;#T;#a;static unsafeExposeInternals(t){return{starts:t.#A,ttls:t.#S,sizes:t.#F,keyMap:t.#n,keyList:t.#i,valList:t.#t,next:t.#o,prev:t.#c,get head(){return t.#l},get tail(){return t.#h},free:t.#w,isBackgroundFetch:e=>t.#e(e),backgroundFetch:(e,i,s,n)=>t.#R(e,i,s,n),moveToTail:e=>t.#E(e),indexes:e=>t.#m(e),rindexes:e=>t.#b(e),isStale:e=>t.#u(e)}}get max(){return this.#d}get maxSize(){return this.#f}get calculatedSize(){return this.#p}get size(){return this.#s}get fetchMethod(){return this.#D}get dispose(){return this.#_}get disposeAfter(){return this.#g}constructor(t){let{max:e=0,ttl:i,ttlResolution:s=1,ttlAutopurge:n,updateAgeOnGet:h,updateAgeOnHas:f,allowStale:l,dispose:p,disposeAfter:y,noDisposeOnSet:a,noUpdateTTL:u,maxSize:g=0,maxEntrySize:A=0,sizeCalculation:c,fetchMethod:w,noDeleteOnFetchRejection:o,noDeleteOnStaleGet:S,allowStaleOnFetchRejection:m,allowStaleOnFetchAbort:d,ignoreFetchAbort:_}=t;if(e!==0&&!F(e))throw new TypeError("max option must be a nonnegative integer");let z=e?k(e):Array;if(!z)throw new Error("invalid max value: "+e);if(this.#d=e,this.#f=g,this.maxEntrySize=A||this.#f,this.sizeCalculation=c,this.sizeCalculation){if(!this.#f&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(w!==void 0&&typeof w!="function")throw new TypeError("fetchMethod must be a function if specified");if(this.#D=w,this.#T=!!w,this.#n=new Map,this.#i=new Array(e).fill(void 0),this.#t=new Array(e).fill(void 0),this.#o=new z(e),this.#c=new z(e),this.#l=0,this.#h=0,this.#w=C.create(e),this.#s=0,this.#p=0,typeof p=="function"&&(this.#_=p),typeof y=="function"?(this.#g=y,this.#r=[]):(this.#g=void 0,this.#r=void 0),this.#y=!!this.#_,this.#a=!!this.#g,this.noDisposeOnSet=!!a,this.noUpdateTTL=!!u,this.noDeleteOnFetchRejection=!!o,this.allowStaleOnFetchRejection=!!m,this.allowStaleOnFetchAbort=!!d,this.ignoreFetchAbort=!!_,this.maxEntrySize!==0){if(this.#f!==0&&!F(this.#f))throw new TypeError("maxSize must be a positive integer if specified");if(!F(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");this.#G()}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!S,this.updateAgeOnGet=!!h,this.updateAgeOnHas=!!f,this.ttlResolution=F(s)||s===0?s:1,this.ttlAutopurge=!!n,this.ttl=i||0,this.ttl){if(!F(this.ttl))throw new TypeError("ttl must be a positive integer if specified");this.#L()}if(this.#d===0&&this.ttl===0&&this.#f===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!this.#d&&!this.#f){let b="LRU_CACHE_UNBOUNDED";q(b)&&(I.add(b),V("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",b,E))}}getRemainingTTL(t){return this.#n.has(t)?1/0:0}#L(){let t=new O(this.#d),e=new O(this.#d);this.#S=t,this.#A=e,this.#j=(n,h,f=D.now())=>{if(e[n]=h!==0?f:0,t[n]=h,h!==0&&this.ttlAutopurge){let l=setTimeout(()=>{this.#u(n)&&this.delete(this.#i[n])},h+1);l.unref&&l.unref()}},this.#O=n=>{e[n]=t[n]!==0?D.now():0},this.#z=(n,h)=>{if(t[h]){let f=t[h],l=e[h];n.ttl=f,n.start=l,n.now=i||s(),n.remainingTTL=n.now+f-l}};let i=0,s=()=>{let n=D.now();if(this.ttlResolution>0){i=n;let h=setTimeout(()=>i=0,this.ttlResolution);h.unref&&h.unref()}return n};this.getRemainingTTL=n=>{let h=this.#n.get(n);return h===void 0?0:t[h]===0||e[h]===0?1/0:e[h]+t[h]-(i||s())},this.#u=n=>t[n]!==0&&e[n]!==0&&(i||s())-e[n]>t[n]}#O=()=>{};#z=()=>{};#j=()=>{};#u=()=>!1;#G(){let t=new O(this.#d);this.#p=0,this.#F=t,this.#v=e=>{this.#p-=t[e],t[e]=0},this.#U=(e,i,s,n)=>{if(this.#e(i))return 0;if(!F(s))if(n){if(typeof n!="function")throw new TypeError("sizeCalculation must be a function");if(s=n(i,e),!F(s))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return s},this.#W=(e,i,s)=>{if(t[e]=i,this.#f){let n=this.#f-t[e];for(;this.#p>n;)this.#C(!0)}this.#p+=t[e],s&&(s.entrySize=i,s.totalCalculatedSize=this.#p)}}#v=t=>{};#W=(t,e,i)=>{};#U=(t,e,i,s)=>{if(i||s)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0};*#m({allowStale:t=this.allowStale}={}){if(this.#s)for(let e=this.#h;!(!this.#x(e)||((t||!this.#u(e))&&(yield e),e===this.#l));)e=this.#c[e]}*#b({allowStale:t=this.allowStale}={}){if(this.#s)for(let e=this.#l;!(!this.#x(e)||((t||!this.#u(e))&&(yield e),e===this.#h));)e=this.#o[e]}#x(t){return t!==void 0&&this.#n.get(this.#i[t])===t}*entries(){for(let t of this.#m())this.#t[t]!==void 0&&this.#i[t]!==void 0&&!this.#e(this.#t[t])&&(yield[this.#i[t],this.#t[t]])}*rentries(){for(let t of this.#b())this.#t[t]!==void 0&&this.#i[t]!==void 0&&!this.#e(this.#t[t])&&(yield[this.#i[t],this.#t[t]])}*keys(){for(let t of this.#m()){let e=this.#i[t];e!==void 0&&!this.#e(this.#t[t])&&(yield e)}}*rkeys(){for(let t of this.#b()){let e=this.#i[t];e!==void 0&&!this.#e(this.#t[t])&&(yield e)}}*values(){for(let t of this.#m())this.#t[t]!==void 0&&!this.#e(this.#t[t])&&(yield this.#t[t])}*rvalues(){for(let t of this.#b())this.#t[t]!==void 0&&!this.#e(this.#t[t])&&(yield this.#t[t])}[Symbol.iterator](){return this.entries()}find(t,e={}){for(let i of this.#m()){let s=this.#t[i],n=this.#e(s)?s.__staleWhileFetching:s;if(n!==void 0&&t(n,this.#i[i],this))return this.get(this.#i[i],e)}}forEach(t,e=this){for(let i of this.#m()){let s=this.#t[i],n=this.#e(s)?s.__staleWhileFetching:s;n!==void 0&&t.call(e,n,this.#i[i],this)}}rforEach(t,e=this){for(let i of this.#b()){let s=this.#t[i],n=this.#e(s)?s.__staleWhileFetching:s;n!==void 0&&t.call(e,n,this.#i[i],this)}}purgeStale(){let t=!1;for(let e of this.#b({allowStale:!0}))this.#u(e)&&(this.delete(this.#i[e]),t=!0);return t}dump(){let t=[];for(let e of this.#m({allowStale:!0})){let i=this.#i[e],s=this.#t[e],n=this.#e(s)?s.__staleWhileFetching:s;if(n===void 0||i===void 0)continue;let h={value:n};if(this.#S&&this.#A){h.ttl=this.#S[e];let f=D.now()-this.#A[e];h.start=Math.floor(Date.now()-f)}this.#F&&(h.size=this.#F[e]),t.unshift([i,h])}return t}load(t){this.clear();for(let[e,i]of t){if(i.start){let s=Date.now()-i.start;i.start=D.now()-s}this.set(e,i.value,i)}}set(t,e,i={}){let{ttl:s=this.ttl,start:n,noDisposeOnSet:h=this.noDisposeOnSet,sizeCalculation:f=this.sizeCalculation,status:l}=i,{noUpdateTTL:p=this.noUpdateTTL}=i,y=this.#U(t,e,i.size||0,f);if(this.maxEntrySize&&y>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),this.delete(t),this;let a=this.#s===0?void 0:this.#n.get(t);if(a===void 0)a=this.#s===0?this.#h:this.#w.length!==0?this.#w.pop():this.#s===this.#d?this.#C(!1):this.#s,this.#i[a]=t,this.#t[a]=e,this.#n.set(t,a),this.#o[this.#h]=a,this.#c[a]=this.#h,this.#h=a,this.#s++,this.#W(a,y,l),l&&(l.set="add"),p=!1;else{this.#E(a);let u=this.#t[a];if(e!==u){if(this.#T&&this.#e(u)?u.__abortController.abort(new Error("replaced")):h||(this.#y&&this.#_?.(u,t,"set"),this.#a&&this.#r?.push([u,t,"set"])),this.#v(a),this.#W(a,y,l),this.#t[a]=e,l){l.set="replace";let g=u&&this.#e(u)?u.__staleWhileFetching:u;g!==void 0&&(l.oldValue=g)}}else l&&(l.set="update")}if(s!==0&&!this.#S&&this.#L(),this.#S&&(p||this.#j(a,s,n),l&&this.#z(l,a)),!h&&this.#a&&this.#r){let u=this.#r,g;for(;g=u?.shift();)this.#g?.(...g)}return this}pop(){try{for(;this.#s;){let t=this.#t[this.#l];if(this.#C(!0),this.#e(t)){if(t.__staleWhileFetching)return t.__staleWhileFetching}else if(t!==void 0)return t}}finally{if(this.#a&&this.#r){let t=this.#r,e;for(;e=t?.shift();)this.#g?.(...e)}}}#C(t){let e=this.#l,i=this.#i[e],s=this.#t[e];return this.#T&&this.#e(s)?s.__abortController.abort(new Error("evicted")):(this.#y||this.#a)&&(this.#y&&this.#_?.(s,i,"evict"),this.#a&&this.#r?.push([s,i,"evict"])),this.#v(e),t&&(this.#i[e]=void 0,this.#t[e]=void 0,this.#w.push(e)),this.#s===1?(this.#l=this.#h=0,this.#w.length=0):this.#l=this.#o[e],this.#n.delete(i),this.#s--,e}has(t,e={}){let{updateAgeOnHas:i=this.updateAgeOnHas,status:s}=e,n=this.#n.get(t);if(n!==void 0){let h=this.#t[n];if(this.#e(h)&&h.__staleWhileFetching===void 0)return!1;if(this.#u(n))s&&(s.has="stale",this.#z(s,n));else return i&&this.#O(n),s&&(s.has="hit",this.#z(s,n)),!0}else s&&(s.has="miss");return!1}peek(t,e={}){let{allowStale:i=this.allowStale}=e,s=this.#n.get(t);if(s!==void 0&&(i||!this.#u(s))){let n=this.#t[s];return this.#e(n)?n.__staleWhileFetching:n}}#R(t,e,i,s){let n=e===void 0?void 0:this.#t[e];if(this.#e(n))return n;let h=new AbortController,{signal:f}=i;f?.addEventListener("abort",()=>h.abort(f.reason),{signal:h.signal});let l={signal:h.signal,options:i,context:s},p=(c,w=!1)=>{let{aborted:o}=h.signal,S=i.ignoreFetchAbort&&c!==void 0;if(i.status&&(o&&!w?(i.status.fetchAborted=!0,i.status.fetchError=h.signal.reason,S&&(i.status.fetchAbortIgnored=!0)):i.status.fetchResolved=!0),o&&!S&&!w)return a(h.signal.reason);let m=g;return this.#t[e]===g&&(c===void 0?m.__staleWhileFetching?this.#t[e]=m.__staleWhileFetching:this.delete(t):(i.status&&(i.status.fetchUpdated=!0),this.set(t,c,l.options))),c},y=c=>(i.status&&(i.status.fetchRejected=!0,i.status.fetchError=c),a(c)),a=c=>{let{aborted:w}=h.signal,o=w&&i.allowStaleOnFetchAbort,S=o||i.allowStaleOnFetchRejection,m=S||i.noDeleteOnFetchRejection,d=g;if(this.#t[e]===g&&(!m||d.__staleWhileFetching===void 0?this.delete(t):o||(this.#t[e]=d.__staleWhileFetching)),S)return i.status&&d.__staleWhileFetching!==void 0&&(i.status.returnedStale=!0),d.__staleWhileFetching;if(d.__returned===d)throw c},u=(c,w)=>{let o=this.#D?.(t,n,l);o&&o instanceof Promise&&o.then(S=>c(S),w),h.signal.addEventListener("abort",()=>{(!i.ignoreFetchAbort||i.allowStaleOnFetchAbort)&&(c(),i.allowStaleOnFetchAbort&&(c=S=>p(S,!0)))})};i.status&&(i.status.fetchDispatched=!0);let g=new Promise(u).then(p,y),A=Object.assign(g,{__abortController:h,__staleWhileFetching:n,__returned:void 0});return e===void 0?(this.set(t,A,{...l.options,status:void 0}),e=this.#n.get(t)):this.#t[e]=A,A}#e(t){if(!this.#T)return!1;let e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof AbortController}async fetch(t,e={}){let{allowStale:i=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,ttl:h=this.ttl,noDisposeOnSet:f=this.noDisposeOnSet,size:l=0,sizeCalculation:p=this.sizeCalculation,noUpdateTTL:y=this.noUpdateTTL,noDeleteOnFetchRejection:a=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:u=this.allowStaleOnFetchRejection,ignoreFetchAbort:g=this.ignoreFetchAbort,allowStaleOnFetchAbort:A=this.allowStaleOnFetchAbort,context:c,forceRefresh:w=!1,status:o,signal:S}=e;if(!this.#T)return o&&(o.fetch="get"),this.get(t,{allowStale:i,updateAgeOnGet:s,noDeleteOnStaleGet:n,status:o});let m={allowStale:i,updateAgeOnGet:s,noDeleteOnStaleGet:n,ttl:h,noDisposeOnSet:f,size:l,sizeCalculation:p,noUpdateTTL:y,noDeleteOnFetchRejection:a,allowStaleOnFetchRejection:u,allowStaleOnFetchAbort:A,ignoreFetchAbort:g,status:o,signal:S},d=this.#n.get(t);if(d===void 0){o&&(o.fetch="miss");let _=this.#R(t,d,m,c);return _.__returned=_}else{let _=this.#t[d];if(this.#e(_)){let U=i&&_.__staleWhileFetching!==void 0;return o&&(o.fetch="inflight",U&&(o.returnedStale=!0)),U?_.__staleWhileFetching:_.__returned=_}let z=this.#u(d);if(!w&&!z)return o&&(o.fetch="hit"),this.#E(d),s&&this.#O(d),o&&this.#z(o,d),_;let b=this.#R(t,d,m,c),j=b.__staleWhileFetching!==void 0&&i;return o&&(o.fetch=z?"stale":"refresh",j&&z&&(o.returnedStale=!0)),j?b.__staleWhileFetching:b.__returned=b}}get(t,e={}){let{allowStale:i=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,status:h}=e,f=this.#n.get(t);if(f!==void 0){let l=this.#t[f],p=this.#e(l);return h&&this.#z(h,f),this.#u(f)?(h&&(h.get="stale"),p?(h&&i&&l.__staleWhileFetching!==void 0&&(h.returnedStale=!0),i?l.__staleWhileFetching:void 0):(n||this.delete(t),h&&i&&(h.returnedStale=!0),i?l:void 0)):(h&&(h.get="hit"),p?l.__staleWhileFetching:(this.#E(f),s&&this.#O(f),l))}else h&&(h.get="miss")}#M(t,e){this.#c[e]=t,this.#o[t]=e}#E(t){t!==this.#h&&(t===this.#l?this.#l=this.#o[t]:this.#M(this.#c[t],this.#o[t]),this.#M(this.#h,t),this.#h=t)}delete(t){let e=!1;if(this.#s!==0){let i=this.#n.get(t);if(i!==void 0)if(e=!0,this.#s===1)this.clear();else{this.#v(i);let s=this.#t[i];this.#e(s)?s.__abortController.abort(new Error("deleted")):(this.#y||this.#a)&&(this.#y&&this.#_?.(s,t,"delete"),this.#a&&this.#r?.push([s,t,"delete"])),this.#n.delete(t),this.#i[i]=void 0,this.#t[i]=void 0,i===this.#h?this.#h=this.#c[i]:i===this.#l?this.#l=this.#o[i]:(this.#o[this.#c[i]]=this.#o[i],this.#c[this.#o[i]]=this.#c[i]),this.#s--,this.#w.push(i)}}if(this.#a&&this.#r?.length){let i=this.#r,s;for(;s=i?.shift();)this.#g?.(...s)}return e}clear(){for(let t of this.#b({allowStale:!0})){let e=this.#t[t];if(this.#e(e))e.__abortController.abort(new Error("deleted"));else{let i=this.#i[t];this.#y&&this.#_?.(e,i,"delete"),this.#a&&this.#r?.push([e,i,"delete"])}}if(this.#n.clear(),this.#t.fill(void 0),this.#i.fill(void 0),this.#S&&this.#A&&(this.#S.fill(0),this.#A.fill(0)),this.#F&&this.#F.fill(0),this.#l=0,this.#h=0,this.#w.length=0,this.#p=0,this.#s=0,this.#a&&this.#r){let t=this.#r,e;for(;e=t?.shift();)this.#g?.(...e)}}};W.LRUCache=E;W.default=E});var B=exports&&exports.__importDefault||function(r){return r&&r.__esModule?r:{default:r}},L=B(H());module.exports=Object.assign(L.default,{default:L.default,LRUCache:L.default});
//# sourceMappingURL=index.min.js.map

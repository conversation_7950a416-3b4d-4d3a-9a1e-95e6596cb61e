{"version": 3, "sources": ["../../@better-scroll/core/dist/core.esm.js"], "sourcesContent": ["/*!\n * better-scroll / core\n * (c) 2016-2023 ustbhuangyi\n * Released under the MIT License.\n */\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\n\nvar propertiesConfig = [\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.currentPos',\r\n        key: 'x'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.currentPos',\r\n        key: 'y'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.hasScroll',\r\n        key: 'hasHorizontalScroll'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.hasScroll',\r\n        key: 'hasVerticalScroll'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.contentSize',\r\n        key: 'scrollerWidth'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.contentSize',\r\n        key: 'scrollerHeight'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.maxScrollPos',\r\n        key: 'maxScrollX'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.maxScrollPos',\r\n        key: 'maxScrollY'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.minScrollPos',\r\n        key: 'minScrollX'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.minScrollPos',\r\n        key: 'minScrollY'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.movingDirection',\r\n        key: 'movingDirectionX'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.movingDirection',\r\n        key: 'movingDirectionY'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.direction',\r\n        key: 'directionX'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.direction',\r\n        key: 'directionY'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.actions.enabled',\r\n        key: 'enabled'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.animater.pending',\r\n        key: 'pending'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.animater.stop',\r\n        key: 'stop'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollTo',\r\n        key: 'scrollTo'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBy',\r\n        key: 'scrollBy'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollToElement',\r\n        key: 'scrollToElement'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.resetPosition',\r\n        key: 'resetPosition'\r\n    }\r\n];\n\nfunction warn(msg) {\r\n    console.error(\"[BScroll warn]: \" + msg);\r\n}\n\n// ssr support\r\nvar inBrowser = typeof window !== 'undefined';\r\nvar ua = inBrowser && navigator.userAgent.toLowerCase();\r\nvar isWeChatDevTools = !!(ua && /wechatdevtools/.test(ua));\r\nvar isAndroid = ua && ua.indexOf('android') > 0;\r\n/* istanbul ignore next */\r\nvar isIOSBadVersion = (function () {\r\n    if (typeof ua === 'string') {\r\n        var regex = /os (\\d\\d?_\\d(_\\d)?)/;\r\n        var matches = regex.exec(ua);\r\n        if (!matches)\r\n            return false;\r\n        var parts = matches[1].split('_').map(function (item) {\r\n            return parseInt(item, 10);\r\n        });\r\n        // ios version >= 13.4 issue 982\r\n        return !!(parts[0] === 13 && parts[1] >= 4);\r\n    }\r\n    return false;\r\n})();\r\n/* istanbul ignore next */\r\nvar supportsPassive = false;\r\n/* istanbul ignore next */\r\nif (inBrowser) {\r\n    var EventName = 'test-passive';\r\n    try {\r\n        var opts = {};\r\n        Object.defineProperty(opts, 'passive', {\r\n            get: function () {\r\n                supportsPassive = true;\r\n            },\r\n        }); // https://github.com/facebook/flow/issues/285\r\n        window.addEventListener(EventName, function () { }, opts);\r\n    }\r\n    catch (e) { }\r\n}\n\nfunction getNow() {\r\n    return window.performance &&\r\n        window.performance.now &&\r\n        window.performance.timing\r\n        ? window.performance.now() + window.performance.timing.navigationStart\r\n        : +new Date();\r\n}\r\nvar extend = function (target, source) {\r\n    for (var key in source) {\r\n        target[key] = source[key];\r\n    }\r\n    return target;\r\n};\r\nfunction isUndef(v) {\r\n    return v === undefined || v === null;\r\n}\r\nfunction between(x, min, max) {\r\n    if (x < min) {\r\n        return min;\r\n    }\r\n    if (x > max) {\r\n        return max;\r\n    }\r\n    return x;\r\n}\n\nvar elementStyle = (inBrowser &&\r\n    document.createElement('div').style);\r\nvar vendor = (function () {\r\n    /* istanbul ignore if  */\r\n    if (!inBrowser) {\r\n        return false;\r\n    }\r\n    var transformNames = [\r\n        {\r\n            key: 'standard',\r\n            value: 'transform',\r\n        },\r\n        {\r\n            key: 'webkit',\r\n            value: 'webkitTransform',\r\n        },\r\n        {\r\n            key: 'Moz',\r\n            value: 'MozTransform',\r\n        },\r\n        {\r\n            key: 'O',\r\n            value: 'OTransform',\r\n        },\r\n        {\r\n            key: 'ms',\r\n            value: 'msTransform',\r\n        },\r\n    ];\r\n    for (var _i = 0, transformNames_1 = transformNames; _i < transformNames_1.length; _i++) {\r\n        var obj = transformNames_1[_i];\r\n        if (elementStyle[obj.value] !== undefined) {\r\n            return obj.key;\r\n        }\r\n    }\r\n    /* istanbul ignore next  */\r\n    return false;\r\n})();\r\n/* istanbul ignore next  */\r\nfunction prefixStyle(style) {\r\n    if (vendor === false) {\r\n        return style;\r\n    }\r\n    if (vendor === 'standard') {\r\n        if (style === 'transitionEnd') {\r\n            return 'transitionend';\r\n        }\r\n        return style;\r\n    }\r\n    return vendor + style.charAt(0).toUpperCase() + style.substr(1);\r\n}\r\nfunction getElement(el) {\r\n    return (typeof el === 'string' ? document.querySelector(el) : el);\r\n}\r\nfunction addEvent(el, type, fn, capture) {\r\n    var useCapture = supportsPassive\r\n        ? {\r\n            passive: false,\r\n            capture: !!capture,\r\n        }\r\n        : !!capture;\r\n    el.addEventListener(type, fn, useCapture);\r\n}\r\nfunction removeEvent(el, type, fn, capture) {\r\n    el.removeEventListener(type, fn, {\r\n        capture: !!capture,\r\n    });\r\n}\r\nfunction maybePrevent(e) {\r\n    if (e.cancelable) {\r\n        e.preventDefault();\r\n    }\r\n}\r\nfunction offset(el) {\r\n    var left = 0;\r\n    var top = 0;\r\n    while (el) {\r\n        left -= el.offsetLeft;\r\n        top -= el.offsetTop;\r\n        el = el.offsetParent;\r\n    }\r\n    return {\r\n        left: left,\r\n        top: top,\r\n    };\r\n}\r\nvendor && vendor !== 'standard' ? '-' + vendor.toLowerCase() + '-' : '';\r\nvar transform = prefixStyle('transform');\r\nvar transition = prefixStyle('transition');\r\nvar hasPerspective = inBrowser && prefixStyle('perspective') in elementStyle;\r\n// fix issue #361\r\nvar hasTouch = inBrowser && ('ontouchstart' in window || isWeChatDevTools);\r\nvar hasTransition = inBrowser && transition in elementStyle;\r\nvar style = {\r\n    transform: transform,\r\n    transition: transition,\r\n    transitionTimingFunction: prefixStyle('transitionTimingFunction'),\r\n    transitionDuration: prefixStyle('transitionDuration'),\r\n    transitionDelay: prefixStyle('transitionDelay'),\r\n    transformOrigin: prefixStyle('transformOrigin'),\r\n    transitionEnd: prefixStyle('transitionEnd'),\r\n    transitionProperty: prefixStyle('transitionProperty'),\r\n};\r\nvar eventTypeMap = {\r\n    touchstart: 1,\r\n    touchmove: 1,\r\n    touchend: 1,\r\n    touchcancel: 1,\r\n    mousedown: 2,\r\n    mousemove: 2,\r\n    mouseup: 2,\r\n};\r\nfunction getRect(el) {\r\n    /* istanbul ignore if  */\r\n    if (el instanceof window.SVGElement) {\r\n        var rect = el.getBoundingClientRect();\r\n        return {\r\n            top: rect.top,\r\n            left: rect.left,\r\n            width: rect.width,\r\n            height: rect.height,\r\n        };\r\n    }\r\n    else {\r\n        return {\r\n            top: el.offsetTop,\r\n            left: el.offsetLeft,\r\n            width: el.offsetWidth,\r\n            height: el.offsetHeight,\r\n        };\r\n    }\r\n}\r\nfunction preventDefaultExceptionFn(el, exceptions) {\r\n    for (var i in exceptions) {\r\n        if (exceptions[i].test(el[i])) {\r\n            return true;\r\n        }\r\n    }\r\n    return false;\r\n}\r\nvar tagExceptionFn = preventDefaultExceptionFn;\r\nfunction tap(e, eventName) {\r\n    var ev = document.createEvent('Event');\r\n    ev.initEvent(eventName, true, true);\r\n    ev.pageX = e.pageX;\r\n    ev.pageY = e.pageY;\r\n    e.target.dispatchEvent(ev);\r\n}\r\nfunction click(e, event) {\r\n    if (event === void 0) { event = 'click'; }\r\n    var eventSource;\r\n    if (e.type === 'mouseup') {\r\n        eventSource = e;\r\n    }\r\n    else if (e.type === 'touchend' || e.type === 'touchcancel') {\r\n        eventSource = e.changedTouches[0];\r\n    }\r\n    var posSrc = {};\r\n    if (eventSource) {\r\n        posSrc.screenX = eventSource.screenX || 0;\r\n        posSrc.screenY = eventSource.screenY || 0;\r\n        posSrc.clientX = eventSource.clientX || 0;\r\n        posSrc.clientY = eventSource.clientY || 0;\r\n    }\r\n    var ev;\r\n    var bubbles = true;\r\n    var cancelable = true;\r\n    var ctrlKey = e.ctrlKey, shiftKey = e.shiftKey, altKey = e.altKey, metaKey = e.metaKey;\r\n    var pressedKeysMap = {\r\n        ctrlKey: ctrlKey,\r\n        shiftKey: shiftKey,\r\n        altKey: altKey,\r\n        metaKey: metaKey,\r\n    };\r\n    if (typeof MouseEvent !== 'undefined') {\r\n        try {\r\n            ev = new MouseEvent(event, extend(__assign({ bubbles: bubbles,\r\n                cancelable: cancelable }, pressedKeysMap), posSrc));\r\n        }\r\n        catch (e) {\r\n            /* istanbul ignore next */\r\n            createEvent();\r\n        }\r\n    }\r\n    else {\r\n        createEvent();\r\n    }\r\n    function createEvent() {\r\n        ev = document.createEvent('Event');\r\n        ev.initEvent(event, bubbles, cancelable);\r\n        extend(ev, posSrc);\r\n    }\r\n    // forwardedTouchEvent set to true in case of the conflict with fastclick\r\n    ev.forwardedTouchEvent = true;\r\n    ev._constructed = true;\r\n    e.target.dispatchEvent(ev);\r\n}\r\nfunction dblclick(e) {\r\n    click(e, 'dblclick');\r\n}\n\nvar ease = {\r\n    // easeOutQuint\r\n    swipe: {\r\n        style: 'cubic-bezier(0.23, 1, 0.32, 1)',\r\n        fn: function (t) {\r\n            return 1 + --t * t * t * t * t;\r\n        }\r\n    },\r\n    // easeOutQuard\r\n    swipeBounce: {\r\n        style: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',\r\n        fn: function (t) {\r\n            return t * (2 - t);\r\n        }\r\n    },\r\n    // easeOutQuart\r\n    bounce: {\r\n        style: 'cubic-bezier(0.165, 0.84, 0.44, 1)',\r\n        fn: function (t) {\r\n            return 1 - --t * t * t * t;\r\n        }\r\n    }\r\n};\n\nvar DEFAULT_INTERVAL = 1000 / 60;\r\nvar windowCompat = inBrowser && window;\r\n/* istanbul ignore next */\r\nfunction noop$1() { }\r\nvar requestAnimationFrame = (function () {\r\n    /* istanbul ignore if  */\r\n    if (!inBrowser) {\r\n        return noop$1;\r\n    }\r\n    return (windowCompat.requestAnimationFrame ||\r\n        windowCompat.webkitRequestAnimationFrame ||\r\n        windowCompat.mozRequestAnimationFrame ||\r\n        windowCompat.oRequestAnimationFrame ||\r\n        // if all else fails, use setTimeout\r\n        function (callback) {\r\n            return window.setTimeout(callback, callback.interval || DEFAULT_INTERVAL); // make interval as precise as possible.\r\n        });\r\n})();\r\nvar cancelAnimationFrame = (function () {\r\n    /* istanbul ignore if  */\r\n    if (!inBrowser) {\r\n        return noop$1;\r\n    }\r\n    return (windowCompat.cancelAnimationFrame ||\r\n        windowCompat.webkitCancelAnimationFrame ||\r\n        windowCompat.mozCancelAnimationFrame ||\r\n        windowCompat.oCancelAnimationFrame ||\r\n        function (id) {\r\n            window.clearTimeout(id);\r\n        });\r\n})();\n\n/* istanbul ignore next */\r\nvar noop = function (val) { };\r\nvar sharedPropertyDefinition = {\r\n    enumerable: true,\r\n    configurable: true,\r\n    get: noop,\r\n    set: noop,\r\n};\r\nvar getProperty = function (obj, key) {\r\n    var keys = key.split('.');\r\n    for (var i = 0; i < keys.length - 1; i++) {\r\n        obj = obj[keys[i]];\r\n        if (typeof obj !== 'object' || !obj)\r\n            return;\r\n    }\r\n    var lastKey = keys.pop();\r\n    if (typeof obj[lastKey] === 'function') {\r\n        return function () {\r\n            return obj[lastKey].apply(obj, arguments);\r\n        };\r\n    }\r\n    else {\r\n        return obj[lastKey];\r\n    }\r\n};\r\nvar setProperty = function (obj, key, value) {\r\n    var keys = key.split('.');\r\n    var temp;\r\n    for (var i = 0; i < keys.length - 1; i++) {\r\n        temp = keys[i];\r\n        if (!obj[temp])\r\n            obj[temp] = {};\r\n        obj = obj[temp];\r\n    }\r\n    obj[keys.pop()] = value;\r\n};\r\nfunction propertiesProxy(target, sourceKey, key) {\r\n    sharedPropertyDefinition.get = function proxyGetter() {\r\n        return getProperty(this, sourceKey);\r\n    };\r\n    sharedPropertyDefinition.set = function proxySetter(val) {\r\n        setProperty(this, sourceKey, val);\r\n    };\r\n    Object.defineProperty(target, key, sharedPropertyDefinition);\r\n}\n\nvar EventEmitter = /** @class */ (function () {\r\n    function EventEmitter(names) {\r\n        this.events = {};\r\n        this.eventTypes = {};\r\n        this.registerType(names);\r\n    }\r\n    EventEmitter.prototype.on = function (type, fn, context) {\r\n        if (context === void 0) { context = this; }\r\n        this.hasType(type);\r\n        if (!this.events[type]) {\r\n            this.events[type] = [];\r\n        }\r\n        this.events[type].push([fn, context]);\r\n        return this;\r\n    };\r\n    EventEmitter.prototype.once = function (type, fn, context) {\r\n        var _this = this;\r\n        if (context === void 0) { context = this; }\r\n        this.hasType(type);\r\n        var magic = function () {\r\n            var args = [];\r\n            for (var _i = 0; _i < arguments.length; _i++) {\r\n                args[_i] = arguments[_i];\r\n            }\r\n            _this.off(type, magic);\r\n            var ret = fn.apply(context, args);\r\n            if (ret === true) {\r\n                return ret;\r\n            }\r\n        };\r\n        magic.fn = fn;\r\n        this.on(type, magic);\r\n        return this;\r\n    };\r\n    EventEmitter.prototype.off = function (type, fn) {\r\n        if (!type && !fn) {\r\n            this.events = {};\r\n            return this;\r\n        }\r\n        if (type) {\r\n            this.hasType(type);\r\n            if (!fn) {\r\n                this.events[type] = [];\r\n                return this;\r\n            }\r\n            var events = this.events[type];\r\n            if (!events) {\r\n                return this;\r\n            }\r\n            var count = events.length;\r\n            while (count--) {\r\n                if (events[count][0] === fn ||\r\n                    (events[count][0] && events[count][0].fn === fn)) {\r\n                    events.splice(count, 1);\r\n                }\r\n            }\r\n            return this;\r\n        }\r\n    };\r\n    EventEmitter.prototype.trigger = function (type) {\r\n        var args = [];\r\n        for (var _i = 1; _i < arguments.length; _i++) {\r\n            args[_i - 1] = arguments[_i];\r\n        }\r\n        this.hasType(type);\r\n        var events = this.events[type];\r\n        if (!events) {\r\n            return;\r\n        }\r\n        var len = events.length;\r\n        var eventsCopy = __spreadArrays(events);\r\n        var ret;\r\n        for (var i = 0; i < len; i++) {\r\n            var event_1 = eventsCopy[i];\r\n            var fn = event_1[0], context = event_1[1];\r\n            if (fn) {\r\n                ret = fn.apply(context, args);\r\n                if (ret === true) {\r\n                    return ret;\r\n                }\r\n            }\r\n        }\r\n    };\r\n    EventEmitter.prototype.registerType = function (names) {\r\n        var _this = this;\r\n        names.forEach(function (type) {\r\n            _this.eventTypes[type] = type;\r\n        });\r\n    };\r\n    EventEmitter.prototype.destroy = function () {\r\n        this.events = {};\r\n        this.eventTypes = {};\r\n    };\r\n    EventEmitter.prototype.hasType = function (type) {\r\n        var types = this.eventTypes;\r\n        var isType = types[type] === type;\r\n        if (!isType) {\r\n            warn(\"EventEmitter has used unknown event type: \\\"\" + type + \"\\\", should be oneof [\" +\r\n                (\"\" + Object.keys(types).map(function (_) { return JSON.stringify(_); })) +\r\n                \"]\");\r\n        }\r\n    };\r\n    return EventEmitter;\r\n}());\r\nvar EventRegister = /** @class */ (function () {\r\n    function EventRegister(wrapper, events) {\r\n        this.wrapper = wrapper;\r\n        this.events = events;\r\n        this.addDOMEvents();\r\n    }\r\n    EventRegister.prototype.destroy = function () {\r\n        this.removeDOMEvents();\r\n        this.events = [];\r\n    };\r\n    EventRegister.prototype.addDOMEvents = function () {\r\n        this.handleDOMEvents(addEvent);\r\n    };\r\n    EventRegister.prototype.removeDOMEvents = function () {\r\n        this.handleDOMEvents(removeEvent);\r\n    };\r\n    EventRegister.prototype.handleDOMEvents = function (eventOperation) {\r\n        var _this = this;\r\n        var wrapper = this.wrapper;\r\n        this.events.forEach(function (event) {\r\n            eventOperation(wrapper, event.name, _this, !!event.capture);\r\n        });\r\n    };\r\n    EventRegister.prototype.handleEvent = function (e) {\r\n        var eventType = e.type;\r\n        this.events.some(function (event) {\r\n            if (event.name === eventType) {\r\n                event.handler(e);\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n    };\r\n    return EventRegister;\r\n}());\n\nvar CustomOptions = /** @class */ (function () {\r\n    function CustomOptions() {\r\n    }\r\n    return CustomOptions;\r\n}());\r\nvar OptionsConstructor = /** @class */ (function (_super) {\r\n    __extends(OptionsConstructor, _super);\r\n    function OptionsConstructor() {\r\n        var _this = _super.call(this) || this;\r\n        _this.startX = 0;\r\n        _this.startY = 0;\r\n        _this.scrollX = false;\r\n        _this.scrollY = true;\r\n        _this.freeScroll = false;\r\n        _this.directionLockThreshold = 0;\r\n        _this.eventPassthrough = \"\" /* None */;\r\n        _this.click = false;\r\n        _this.dblclick = false;\r\n        _this.tap = '';\r\n        _this.bounce = {\r\n            top: true,\r\n            bottom: true,\r\n            left: true,\r\n            right: true,\r\n        };\r\n        _this.bounceTime = 800;\r\n        _this.momentum = true;\r\n        _this.momentumLimitTime = 300;\r\n        _this.momentumLimitDistance = 15;\r\n        _this.swipeTime = 2500;\r\n        _this.swipeBounceTime = 500;\r\n        _this.deceleration = 0.0015;\r\n        _this.flickLimitTime = 200;\r\n        _this.flickLimitDistance = 100;\r\n        _this.resizePolling = 60;\r\n        _this.probeType = 0 /* Default */;\r\n        _this.stopPropagation = false;\r\n        _this.preventDefault = true;\r\n        _this.preventDefaultException = {\r\n            tagName: /^(INPUT|TEXTAREA|BUTTON|SELECT|AUDIO)$/,\r\n        };\r\n        _this.tagException = {\r\n            tagName: /^TEXTAREA$/,\r\n        };\r\n        _this.HWCompositing = true;\r\n        _this.useTransition = true;\r\n        _this.bindToWrapper = false;\r\n        _this.bindToTarget = false;\r\n        _this.disableMouse = hasTouch;\r\n        _this.disableTouch = !hasTouch;\r\n        _this.autoBlur = true;\r\n        _this.autoEndDistance = 5;\r\n        _this.outOfBoundaryDampingFactor = 1 / 3;\r\n        _this.specifiedIndexAsContent = 0;\r\n        _this.quadrant = 1 /* First */;\r\n        return _this;\r\n    }\r\n    OptionsConstructor.prototype.merge = function (options) {\r\n        if (!options)\r\n            return this;\r\n        for (var key in options) {\r\n            if (key === 'bounce') {\r\n                this.bounce = this.resolveBounce(options[key]);\r\n                continue;\r\n            }\r\n            this[key] = options[key];\r\n        }\r\n        return this;\r\n    };\r\n    OptionsConstructor.prototype.process = function () {\r\n        this.translateZ =\r\n            this.HWCompositing && hasPerspective ? ' translateZ(1px)' : '';\r\n        this.useTransition = this.useTransition && hasTransition;\r\n        this.preventDefault = !this.eventPassthrough && this.preventDefault;\r\n        // If you want eventPassthrough I have to lock one of the axes\r\n        this.scrollX =\r\n            this.eventPassthrough === \"horizontal\" /* Horizontal */\r\n                ? false\r\n                : this.scrollX;\r\n        this.scrollY =\r\n            this.eventPassthrough === \"vertical\" /* Vertical */ ? false : this.scrollY;\r\n        // With eventPassthrough we also need lockDirection mechanism\r\n        this.freeScroll = this.freeScroll && !this.eventPassthrough;\r\n        // force true when freeScroll is true\r\n        this.scrollX = this.freeScroll ? true : this.scrollX;\r\n        this.scrollY = this.freeScroll ? true : this.scrollY;\r\n        this.directionLockThreshold = this.eventPassthrough\r\n            ? 0\r\n            : this.directionLockThreshold;\r\n        return this;\r\n    };\r\n    OptionsConstructor.prototype.resolveBounce = function (bounceOptions) {\r\n        var DEFAULT_BOUNCE = {\r\n            top: true,\r\n            right: true,\r\n            bottom: true,\r\n            left: true,\r\n        };\r\n        var NEGATED_BOUNCE = {\r\n            top: false,\r\n            right: false,\r\n            bottom: false,\r\n            left: false,\r\n        };\r\n        var ret;\r\n        if (typeof bounceOptions === 'object') {\r\n            ret = extend(DEFAULT_BOUNCE, bounceOptions);\r\n        }\r\n        else {\r\n            ret = bounceOptions ? DEFAULT_BOUNCE : NEGATED_BOUNCE;\r\n        }\r\n        return ret;\r\n    };\r\n    return OptionsConstructor;\r\n}(CustomOptions));\n\nvar ActionsHandler = /** @class */ (function () {\r\n    function ActionsHandler(wrapper, options) {\r\n        this.wrapper = wrapper;\r\n        this.options = options;\r\n        this.hooks = new EventEmitter([\r\n            'beforeStart',\r\n            'start',\r\n            'move',\r\n            'end',\r\n            'click',\r\n        ]);\r\n        this.handleDOMEvents();\r\n    }\r\n    ActionsHandler.prototype.handleDOMEvents = function () {\r\n        var _a = this.options, bindToWrapper = _a.bindToWrapper, disableMouse = _a.disableMouse, disableTouch = _a.disableTouch, click = _a.click;\r\n        var wrapper = this.wrapper;\r\n        var target = bindToWrapper ? wrapper : window;\r\n        var wrapperEvents = [];\r\n        var targetEvents = [];\r\n        var shouldRegisterTouch = !disableTouch;\r\n        var shouldRegisterMouse = !disableMouse;\r\n        if (click) {\r\n            wrapperEvents.push({\r\n                name: 'click',\r\n                handler: this.click.bind(this),\r\n                capture: true,\r\n            });\r\n        }\r\n        if (shouldRegisterTouch) {\r\n            wrapperEvents.push({\r\n                name: 'touchstart',\r\n                handler: this.start.bind(this),\r\n            });\r\n            targetEvents.push({\r\n                name: 'touchmove',\r\n                handler: this.move.bind(this),\r\n            }, {\r\n                name: 'touchend',\r\n                handler: this.end.bind(this),\r\n            }, {\r\n                name: 'touchcancel',\r\n                handler: this.end.bind(this),\r\n            });\r\n        }\r\n        if (shouldRegisterMouse) {\r\n            wrapperEvents.push({\r\n                name: 'mousedown',\r\n                handler: this.start.bind(this),\r\n            });\r\n            targetEvents.push({\r\n                name: 'mousemove',\r\n                handler: this.move.bind(this),\r\n            }, {\r\n                name: 'mouseup',\r\n                handler: this.end.bind(this),\r\n            });\r\n        }\r\n        this.wrapperEventRegister = new EventRegister(wrapper, wrapperEvents);\r\n        this.targetEventRegister = new EventRegister(target, targetEvents);\r\n    };\r\n    ActionsHandler.prototype.beforeHandler = function (e, type) {\r\n        var _a = this.options, preventDefault = _a.preventDefault, stopPropagation = _a.stopPropagation, preventDefaultException = _a.preventDefaultException;\r\n        var preventDefaultConditions = {\r\n            start: function () {\r\n                return (preventDefault &&\r\n                    !preventDefaultExceptionFn(e.target, preventDefaultException));\r\n            },\r\n            end: function () {\r\n                return (preventDefault &&\r\n                    !preventDefaultExceptionFn(e.target, preventDefaultException));\r\n            },\r\n            move: function () {\r\n                return preventDefault;\r\n            },\r\n        };\r\n        if (preventDefaultConditions[type]()) {\r\n            e.preventDefault();\r\n        }\r\n        if (stopPropagation) {\r\n            e.stopPropagation();\r\n        }\r\n    };\r\n    ActionsHandler.prototype.setInitiated = function (type) {\r\n        if (type === void 0) { type = 0; }\r\n        this.initiated = type;\r\n    };\r\n    ActionsHandler.prototype.start = function (e) {\r\n        var _eventType = eventTypeMap[e.type];\r\n        if (this.initiated && this.initiated !== _eventType) {\r\n            return;\r\n        }\r\n        this.setInitiated(_eventType);\r\n        // if textarea or other html tags in options.tagException is manipulated\r\n        // do not make bs scroll\r\n        if (tagExceptionFn(e.target, this.options.tagException)) {\r\n            this.setInitiated();\r\n            return;\r\n        }\r\n        // only allow mouse left button\r\n        if (_eventType === 2 /* Mouse */ && e.button !== 0 /* Left */)\r\n            return;\r\n        if (this.hooks.trigger(this.hooks.eventTypes.beforeStart, e)) {\r\n            return;\r\n        }\r\n        this.beforeHandler(e, 'start');\r\n        var point = (e.touches ? e.touches[0] : e);\r\n        this.pointX = point.pageX;\r\n        this.pointY = point.pageY;\r\n        this.hooks.trigger(this.hooks.eventTypes.start, e);\r\n    };\r\n    ActionsHandler.prototype.move = function (e) {\r\n        if (eventTypeMap[e.type] !== this.initiated) {\r\n            return;\r\n        }\r\n        this.beforeHandler(e, 'move');\r\n        var point = (e.touches ? e.touches[0] : e);\r\n        var deltaX = point.pageX - this.pointX;\r\n        var deltaY = point.pageY - this.pointY;\r\n        this.pointX = point.pageX;\r\n        this.pointY = point.pageY;\r\n        if (this.hooks.trigger(this.hooks.eventTypes.move, {\r\n            deltaX: deltaX,\r\n            deltaY: deltaY,\r\n            e: e,\r\n        })) {\r\n            return;\r\n        }\r\n        // auto end when out of viewport\r\n        var scrollLeft = document.documentElement.scrollLeft ||\r\n            window.pageXOffset ||\r\n            document.body.scrollLeft;\r\n        var scrollTop = document.documentElement.scrollTop ||\r\n            window.pageYOffset ||\r\n            document.body.scrollTop;\r\n        var pX = this.pointX - scrollLeft;\r\n        var pY = this.pointY - scrollTop;\r\n        var autoEndDistance = this.options.autoEndDistance;\r\n        if (pX > document.documentElement.clientWidth - autoEndDistance ||\r\n            pY > document.documentElement.clientHeight - autoEndDistance ||\r\n            pX < autoEndDistance ||\r\n            pY < autoEndDistance) {\r\n            this.end(e);\r\n        }\r\n    };\r\n    ActionsHandler.prototype.end = function (e) {\r\n        if (eventTypeMap[e.type] !== this.initiated) {\r\n            return;\r\n        }\r\n        this.setInitiated();\r\n        this.beforeHandler(e, 'end');\r\n        this.hooks.trigger(this.hooks.eventTypes.end, e);\r\n    };\r\n    ActionsHandler.prototype.click = function (e) {\r\n        this.hooks.trigger(this.hooks.eventTypes.click, e);\r\n    };\r\n    ActionsHandler.prototype.setContent = function (content) {\r\n        if (content !== this.wrapper) {\r\n            this.wrapper = content;\r\n            this.rebindDOMEvents();\r\n        }\r\n    };\r\n    ActionsHandler.prototype.rebindDOMEvents = function () {\r\n        this.wrapperEventRegister.destroy();\r\n        this.targetEventRegister.destroy();\r\n        this.handleDOMEvents();\r\n    };\r\n    ActionsHandler.prototype.destroy = function () {\r\n        this.wrapperEventRegister.destroy();\r\n        this.targetEventRegister.destroy();\r\n        this.hooks.destroy();\r\n    };\r\n    return ActionsHandler;\r\n}());\n\nvar translaterMetaData = {\r\n    x: ['translateX', 'px'],\r\n    y: ['translateY', 'px'],\r\n};\r\nvar Translater = /** @class */ (function () {\r\n    function Translater(content) {\r\n        this.setContent(content);\r\n        this.hooks = new EventEmitter(['beforeTranslate', 'translate']);\r\n    }\r\n    Translater.prototype.getComputedPosition = function () {\r\n        var cssStyle = window.getComputedStyle(this.content, null);\r\n        var matrix = cssStyle[style.transform].split(')')[0].split(', ');\r\n        var x = +(matrix[12] || matrix[4]) || 0;\r\n        var y = +(matrix[13] || matrix[5]) || 0;\r\n        return {\r\n            x: x,\r\n            y: y,\r\n        };\r\n    };\r\n    Translater.prototype.translate = function (point) {\r\n        var transformStyle = [];\r\n        Object.keys(point).forEach(function (key) {\r\n            if (!translaterMetaData[key]) {\r\n                return;\r\n            }\r\n            var transformFnName = translaterMetaData[key][0];\r\n            if (transformFnName) {\r\n                var transformFnArgUnit = translaterMetaData[key][1];\r\n                var transformFnArg = point[key];\r\n                transformStyle.push(transformFnName + \"(\" + transformFnArg + transformFnArgUnit + \")\");\r\n            }\r\n        });\r\n        this.hooks.trigger(this.hooks.eventTypes.beforeTranslate, transformStyle, point);\r\n        this.style[style.transform] = transformStyle.join(' ');\r\n        this.hooks.trigger(this.hooks.eventTypes.translate, point);\r\n    };\r\n    Translater.prototype.setContent = function (content) {\r\n        if (this.content !== content) {\r\n            this.content = content;\r\n            this.style = content.style;\r\n        }\r\n    };\r\n    Translater.prototype.destroy = function () {\r\n        this.hooks.destroy();\r\n    };\r\n    return Translater;\r\n}());\n\nvar Base = /** @class */ (function () {\r\n    function Base(content, translater, options) {\r\n        this.translater = translater;\r\n        this.options = options;\r\n        this.timer = 0;\r\n        this.hooks = new EventEmitter([\r\n            'move',\r\n            'end',\r\n            'beforeForceStop',\r\n            'forceStop',\r\n            'callStop',\r\n            'time',\r\n            'timeFunction',\r\n        ]);\r\n        this.setContent(content);\r\n    }\r\n    Base.prototype.translate = function (endPoint) {\r\n        this.translater.translate(endPoint);\r\n    };\r\n    Base.prototype.setPending = function (pending) {\r\n        this.pending = pending;\r\n    };\r\n    Base.prototype.setForceStopped = function (forceStopped) {\r\n        this.forceStopped = forceStopped;\r\n    };\r\n    Base.prototype.setCallStop = function (called) {\r\n        this.callStopWhenPending = called;\r\n    };\r\n    Base.prototype.setContent = function (content) {\r\n        if (this.content !== content) {\r\n            this.content = content;\r\n            this.style = content.style;\r\n            this.stop();\r\n        }\r\n    };\r\n    Base.prototype.clearTimer = function () {\r\n        if (this.timer) {\r\n            cancelAnimationFrame(this.timer);\r\n            this.timer = 0;\r\n        }\r\n    };\r\n    Base.prototype.destroy = function () {\r\n        this.hooks.destroy();\r\n        cancelAnimationFrame(this.timer);\r\n    };\r\n    return Base;\r\n}());\n\n// iOS 13.6 - 14.x, window.getComputedStyle sometimes will get wrong transform value\r\n// when bs use transition mode\r\n// eg: translateY -100px -> -200px, when the last frame which is about to scroll to -200px\r\n// window.getComputedStyle(this.content) will calculate transformY to be -100px(startPoint)\r\n// it is weird\r\n// so we should validate position caculated by 'window.getComputedStyle'\r\nvar isValidPostion = function (startPoint, endPoint, currentPos, prePos) {\r\n    var computeDirection = function (endValue, startValue) {\r\n        var delta = endValue - startValue;\r\n        var direction = delta > 0\r\n            ? -1 /* Negative */\r\n            : delta < 0\r\n                ? 1 /* Positive */\r\n                : 0 /* Default */;\r\n        return direction;\r\n    };\r\n    var directionX = computeDirection(endPoint.x, startPoint.x);\r\n    var directionY = computeDirection(endPoint.y, startPoint.y);\r\n    var deltaX = currentPos.x - prePos.x;\r\n    var deltaY = currentPos.y - prePos.y;\r\n    return directionX * deltaX <= 0 && directionY * deltaY <= 0;\r\n};\n\nvar Transition = /** @class */ (function (_super) {\r\n    __extends(Transition, _super);\r\n    function Transition() {\r\n        return _super !== null && _super.apply(this, arguments) || this;\r\n    }\r\n    Transition.prototype.startProbe = function (startPoint, endPoint) {\r\n        var _this = this;\r\n        var prePos = startPoint;\r\n        var probe = function () {\r\n            var pos = _this.translater.getComputedPosition();\r\n            if (isValidPostion(startPoint, endPoint, pos, prePos)) {\r\n                _this.hooks.trigger(_this.hooks.eventTypes.move, pos);\r\n            }\r\n            // call bs.stop() should not dispatch end hook again.\r\n            // forceStop hook will do this.\r\n            /* istanbul ignore if  */\r\n            if (!_this.pending) {\r\n                if (_this.callStopWhenPending) {\r\n                    _this.callStopWhenPending = false;\r\n                }\r\n                else {\r\n                    // transition ends should dispatch end hook.\r\n                    _this.hooks.trigger(_this.hooks.eventTypes.end, pos);\r\n                }\r\n            }\r\n            prePos = pos;\r\n            if (_this.pending) {\r\n                _this.timer = requestAnimationFrame(probe);\r\n            }\r\n        };\r\n        // when manually call bs.stop(), then bs.scrollTo()\r\n        // we should reset callStopWhenPending to dispatch end hook\r\n        if (this.callStopWhenPending) {\r\n            this.setCallStop(false);\r\n        }\r\n        cancelAnimationFrame(this.timer);\r\n        probe();\r\n    };\r\n    Transition.prototype.transitionTime = function (time) {\r\n        if (time === void 0) { time = 0; }\r\n        this.style[style.transitionDuration] = time + 'ms';\r\n        this.hooks.trigger(this.hooks.eventTypes.time, time);\r\n    };\r\n    Transition.prototype.transitionTimingFunction = function (easing) {\r\n        this.style[style.transitionTimingFunction] = easing;\r\n        this.hooks.trigger(this.hooks.eventTypes.timeFunction, easing);\r\n    };\r\n    Transition.prototype.transitionProperty = function () {\r\n        this.style[style.transitionProperty] = style.transform;\r\n    };\r\n    Transition.prototype.move = function (startPoint, endPoint, time, easingFn) {\r\n        this.setPending(time > 0);\r\n        this.transitionTimingFunction(easingFn);\r\n        this.transitionProperty();\r\n        this.transitionTime(time);\r\n        this.translate(endPoint);\r\n        var isRealtimeProbeType = this.options.probeType === 3 /* Realtime */;\r\n        if (time && isRealtimeProbeType) {\r\n            this.startProbe(startPoint, endPoint);\r\n        }\r\n        // if we change content's transformY in a tick\r\n        // such as: 0 -> 50px -> 0\r\n        // transitionend will not be triggered\r\n        // so we forceupdate by reflow\r\n        if (!time) {\r\n            this._reflow = this.content.offsetHeight;\r\n            if (isRealtimeProbeType) {\r\n                this.hooks.trigger(this.hooks.eventTypes.move, endPoint);\r\n            }\r\n            this.hooks.trigger(this.hooks.eventTypes.end, endPoint);\r\n        }\r\n    };\r\n    Transition.prototype.doStop = function () {\r\n        var pending = this.pending;\r\n        this.setForceStopped(false);\r\n        this.setCallStop(false);\r\n        // still in transition\r\n        if (pending) {\r\n            this.setPending(false);\r\n            cancelAnimationFrame(this.timer);\r\n            var _a = this.translater.getComputedPosition(), x = _a.x, y = _a.y;\r\n            this.transitionTime();\r\n            this.translate({ x: x, y: y });\r\n            this.setForceStopped(true);\r\n            this.setCallStop(true);\r\n            this.hooks.trigger(this.hooks.eventTypes.forceStop, { x: x, y: y });\r\n        }\r\n        return pending;\r\n    };\r\n    Transition.prototype.stop = function () {\r\n        var stopFromTransition = this.doStop();\r\n        if (stopFromTransition) {\r\n            this.hooks.trigger(this.hooks.eventTypes.callStop);\r\n        }\r\n    };\r\n    return Transition;\r\n}(Base));\n\nvar Animation = /** @class */ (function (_super) {\r\n    __extends(Animation, _super);\r\n    function Animation() {\r\n        return _super !== null && _super.apply(this, arguments) || this;\r\n    }\r\n    Animation.prototype.move = function (startPoint, endPoint, time, easingFn) {\r\n        // time is 0\r\n        if (!time) {\r\n            this.translate(endPoint);\r\n            if (this.options.probeType === 3 /* Realtime */) {\r\n                this.hooks.trigger(this.hooks.eventTypes.move, endPoint);\r\n            }\r\n            this.hooks.trigger(this.hooks.eventTypes.end, endPoint);\r\n            return;\r\n        }\r\n        this.animate(startPoint, endPoint, time, easingFn);\r\n    };\r\n    Animation.prototype.animate = function (startPoint, endPoint, duration, easingFn) {\r\n        var _this = this;\r\n        var startTime = getNow();\r\n        var destTime = startTime + duration;\r\n        var isRealtimeProbeType = this.options.probeType === 3 /* Realtime */;\r\n        var step = function () {\r\n            var now = getNow();\r\n            // js animation end\r\n            if (now >= destTime) {\r\n                _this.translate(endPoint);\r\n                if (isRealtimeProbeType) {\r\n                    _this.hooks.trigger(_this.hooks.eventTypes.move, endPoint);\r\n                }\r\n                _this.hooks.trigger(_this.hooks.eventTypes.end, endPoint);\r\n                return;\r\n            }\r\n            now = (now - startTime) / duration;\r\n            var easing = easingFn(now);\r\n            var newPoint = {};\r\n            Object.keys(endPoint).forEach(function (key) {\r\n                var startValue = startPoint[key];\r\n                var endValue = endPoint[key];\r\n                newPoint[key] = (endValue - startValue) * easing + startValue;\r\n            });\r\n            _this.translate(newPoint);\r\n            if (isRealtimeProbeType) {\r\n                _this.hooks.trigger(_this.hooks.eventTypes.move, newPoint);\r\n            }\r\n            if (_this.pending) {\r\n                _this.timer = requestAnimationFrame(step);\r\n            }\r\n            // call bs.stop() should not dispatch end hook again.\r\n            // forceStop hook will do this.\r\n            /* istanbul ignore if  */\r\n            if (!_this.pending) {\r\n                if (_this.callStopWhenPending) {\r\n                    _this.callStopWhenPending = false;\r\n                }\r\n                else {\r\n                    // raf ends should dispatch end hook.\r\n                    _this.hooks.trigger(_this.hooks.eventTypes.end, endPoint);\r\n                }\r\n            }\r\n        };\r\n        this.setPending(true);\r\n        // when manually call bs.stop(), then bs.scrollTo()\r\n        // we should reset callStopWhenPending to dispatch end hook\r\n        if (this.callStopWhenPending) {\r\n            this.setCallStop(false);\r\n        }\r\n        cancelAnimationFrame(this.timer);\r\n        step();\r\n    };\r\n    Animation.prototype.doStop = function () {\r\n        var pending = this.pending;\r\n        this.setForceStopped(false);\r\n        this.setCallStop(false);\r\n        // still in requestFrameAnimation\r\n        if (pending) {\r\n            this.setPending(false);\r\n            cancelAnimationFrame(this.timer);\r\n            var pos = this.translater.getComputedPosition();\r\n            this.setForceStopped(true);\r\n            this.setCallStop(true);\r\n            this.hooks.trigger(this.hooks.eventTypes.forceStop, pos);\r\n        }\r\n        return pending;\r\n    };\r\n    Animation.prototype.stop = function () {\r\n        var stopFromAnimation = this.doStop();\r\n        if (stopFromAnimation) {\r\n            this.hooks.trigger(this.hooks.eventTypes.callStop);\r\n        }\r\n    };\r\n    return Animation;\r\n}(Base));\n\nfunction createAnimater(element, translater, options) {\r\n    var useTransition = options.useTransition;\r\n    var animaterOptions = {};\r\n    Object.defineProperty(animaterOptions, 'probeType', {\r\n        enumerable: true,\r\n        configurable: false,\r\n        get: function () {\r\n            return options.probeType;\r\n        },\r\n    });\r\n    if (useTransition) {\r\n        return new Transition(element, translater, animaterOptions);\r\n    }\r\n    else {\r\n        return new Animation(element, translater, animaterOptions);\r\n    }\r\n}\n\nvar Behavior = /** @class */ (function () {\r\n    function Behavior(wrapper, content, options) {\r\n        this.wrapper = wrapper;\r\n        this.options = options;\r\n        this.hooks = new EventEmitter([\r\n            'beforeComputeBoundary',\r\n            'computeBoundary',\r\n            'momentum',\r\n            'end',\r\n            'ignoreHasScroll'\r\n        ]);\r\n        this.refresh(content);\r\n    }\r\n    Behavior.prototype.start = function () {\r\n        this.dist = 0;\r\n        this.setMovingDirection(0 /* Default */);\r\n        this.setDirection(0 /* Default */);\r\n    };\r\n    Behavior.prototype.move = function (delta) {\r\n        delta = this.hasScroll ? delta : 0;\r\n        this.setMovingDirection(delta);\r\n        return this.performDampingAlgorithm(delta, this.options.outOfBoundaryDampingFactor);\r\n    };\r\n    Behavior.prototype.setMovingDirection = function (delta) {\r\n        this.movingDirection =\r\n            delta > 0\r\n                ? -1 /* Negative */\r\n                : delta < 0\r\n                    ? 1 /* Positive */\r\n                    : 0 /* Default */;\r\n    };\r\n    Behavior.prototype.setDirection = function (delta) {\r\n        this.direction =\r\n            delta > 0\r\n                ? -1 /* Negative */\r\n                : delta < 0\r\n                    ? 1 /* Positive */\r\n                    : 0 /* Default */;\r\n    };\r\n    Behavior.prototype.performDampingAlgorithm = function (delta, dampingFactor) {\r\n        var newPos = this.currentPos + delta;\r\n        // Slow down or stop if outside of the boundaries\r\n        if (newPos > this.minScrollPos || newPos < this.maxScrollPos) {\r\n            if ((newPos > this.minScrollPos && this.options.bounces[0]) ||\r\n                (newPos < this.maxScrollPos && this.options.bounces[1])) {\r\n                newPos = this.currentPos + delta * dampingFactor;\r\n            }\r\n            else {\r\n                newPos =\r\n                    newPos > this.minScrollPos ? this.minScrollPos : this.maxScrollPos;\r\n            }\r\n        }\r\n        return newPos;\r\n    };\r\n    Behavior.prototype.end = function (duration) {\r\n        var momentumInfo = {\r\n            duration: 0\r\n        };\r\n        var absDist = Math.abs(this.currentPos - this.startPos);\r\n        // start momentum animation if needed\r\n        if (this.options.momentum &&\r\n            duration < this.options.momentumLimitTime &&\r\n            absDist > this.options.momentumLimitDistance) {\r\n            var wrapperSize = (this.direction === -1 /* Negative */ && this.options.bounces[0]) ||\r\n                (this.direction === 1 /* Positive */ && this.options.bounces[1])\r\n                ? this.wrapperSize\r\n                : 0;\r\n            momentumInfo = this.hasScroll\r\n                ? this.momentum(this.currentPos, this.startPos, duration, this.maxScrollPos, this.minScrollPos, wrapperSize, this.options)\r\n                : { destination: this.currentPos, duration: 0 };\r\n        }\r\n        else {\r\n            this.hooks.trigger(this.hooks.eventTypes.end, momentumInfo);\r\n        }\r\n        return momentumInfo;\r\n    };\r\n    Behavior.prototype.momentum = function (current, start, time, lowerMargin, upperMargin, wrapperSize, options) {\r\n        if (options === void 0) { options = this.options; }\r\n        var distance = current - start;\r\n        var speed = Math.abs(distance) / time;\r\n        var deceleration = options.deceleration, swipeBounceTime = options.swipeBounceTime, swipeTime = options.swipeTime;\r\n        var duration = Math.min(swipeTime, (speed * 2) / deceleration);\r\n        var momentumData = {\r\n            destination: current + ((speed * speed) / deceleration) * (distance < 0 ? -1 : 1),\r\n            duration: duration,\r\n            rate: 15\r\n        };\r\n        this.hooks.trigger(this.hooks.eventTypes.momentum, momentumData, distance);\r\n        if (momentumData.destination < lowerMargin) {\r\n            momentumData.destination = wrapperSize\r\n                ? Math.max(lowerMargin - wrapperSize / 4, lowerMargin - (wrapperSize / momentumData.rate) * speed)\r\n                : lowerMargin;\r\n            momentumData.duration = swipeBounceTime;\r\n        }\r\n        else if (momentumData.destination > upperMargin) {\r\n            momentumData.destination = wrapperSize\r\n                ? Math.min(upperMargin + wrapperSize / 4, upperMargin + (wrapperSize / momentumData.rate) * speed)\r\n                : upperMargin;\r\n            momentumData.duration = swipeBounceTime;\r\n        }\r\n        momentumData.destination = Math.round(momentumData.destination);\r\n        return momentumData;\r\n    };\r\n    Behavior.prototype.updateDirection = function () {\r\n        var absDist = this.currentPos - this.absStartPos;\r\n        this.setDirection(absDist);\r\n    };\r\n    Behavior.prototype.refresh = function (content) {\r\n        var _a = this.options.rect, size = _a.size, position = _a.position;\r\n        var isWrapperStatic = window.getComputedStyle(this.wrapper, null).position === 'static';\r\n        // Force reflow\r\n        var wrapperRect = getRect(this.wrapper);\r\n        // use client is more fair than offset\r\n        this.wrapperSize = this.wrapper[size === 'width' ? 'clientWidth' : 'clientHeight'];\r\n        this.setContent(content);\r\n        var contentRect = getRect(this.content);\r\n        this.contentSize = contentRect[size];\r\n        this.relativeOffset = contentRect[position];\r\n        /* istanbul ignore if  */\r\n        if (isWrapperStatic) {\r\n            this.relativeOffset -= wrapperRect[position];\r\n        }\r\n        this.computeBoundary();\r\n        this.setDirection(0 /* Default */);\r\n    };\r\n    Behavior.prototype.setContent = function (content) {\r\n        if (content !== this.content) {\r\n            this.content = content;\r\n            this.resetState();\r\n        }\r\n    };\r\n    Behavior.prototype.resetState = function () {\r\n        this.currentPos = 0;\r\n        this.startPos = 0;\r\n        this.dist = 0;\r\n        this.setDirection(0 /* Default */);\r\n        this.setMovingDirection(0 /* Default */);\r\n        this.resetStartPos();\r\n    };\r\n    Behavior.prototype.computeBoundary = function () {\r\n        this.hooks.trigger(this.hooks.eventTypes.beforeComputeBoundary);\r\n        var boundary = {\r\n            minScrollPos: 0,\r\n            maxScrollPos: this.wrapperSize - this.contentSize\r\n        };\r\n        if (boundary.maxScrollPos < 0) {\r\n            boundary.maxScrollPos -= this.relativeOffset;\r\n            if (this.options.specifiedIndexAsContent === 0) {\r\n                boundary.minScrollPos = -this.relativeOffset;\r\n            }\r\n        }\r\n        this.hooks.trigger(this.hooks.eventTypes.computeBoundary, boundary);\r\n        this.minScrollPos = boundary.minScrollPos;\r\n        this.maxScrollPos = boundary.maxScrollPos;\r\n        this.hasScroll =\r\n            this.options.scrollable && this.maxScrollPos < this.minScrollPos;\r\n        if (!this.hasScroll && this.minScrollPos < this.maxScrollPos) {\r\n            this.maxScrollPos = this.minScrollPos;\r\n            this.contentSize = this.wrapperSize;\r\n        }\r\n    };\r\n    Behavior.prototype.updatePosition = function (pos) {\r\n        this.currentPos = pos;\r\n    };\r\n    Behavior.prototype.getCurrentPos = function () {\r\n        return this.currentPos;\r\n    };\r\n    Behavior.prototype.checkInBoundary = function () {\r\n        var position = this.adjustPosition(this.currentPos);\r\n        var inBoundary = position === this.getCurrentPos();\r\n        return {\r\n            position: position,\r\n            inBoundary: inBoundary\r\n        };\r\n    };\r\n    // adjust position when out of boundary\r\n    Behavior.prototype.adjustPosition = function (pos) {\r\n        if (!this.hasScroll &&\r\n            !this.hooks.trigger(this.hooks.eventTypes.ignoreHasScroll)) {\r\n            pos = this.minScrollPos;\r\n        }\r\n        else if (pos > this.minScrollPos) {\r\n            pos = this.minScrollPos;\r\n        }\r\n        else if (pos < this.maxScrollPos) {\r\n            pos = this.maxScrollPos;\r\n        }\r\n        return pos;\r\n    };\r\n    Behavior.prototype.updateStartPos = function () {\r\n        this.startPos = this.currentPos;\r\n    };\r\n    Behavior.prototype.updateAbsStartPos = function () {\r\n        this.absStartPos = this.currentPos;\r\n    };\r\n    Behavior.prototype.resetStartPos = function () {\r\n        this.updateStartPos();\r\n        this.updateAbsStartPos();\r\n    };\r\n    Behavior.prototype.getAbsDist = function (delta) {\r\n        this.dist += delta;\r\n        return Math.abs(this.dist);\r\n    };\r\n    Behavior.prototype.destroy = function () {\r\n        this.hooks.destroy();\r\n    };\r\n    return Behavior;\r\n}());\n\nvar _a, _b, _c, _d;\r\nvar PassthroughHandlers = (_a = {},\r\n    _a[\"yes\" /* Yes */] = function (e) {\r\n        return true;\r\n    },\r\n    _a[\"no\" /* No */] = function (e) {\r\n        maybePrevent(e);\r\n        return false;\r\n    },\r\n    _a);\r\nvar DirectionMap = (_b = {},\r\n    _b[\"horizontal\" /* Horizontal */] = (_c = {},\r\n        _c[\"yes\" /* Yes */] = \"horizontal\" /* Horizontal */,\r\n        _c[\"no\" /* No */] = \"vertical\" /* Vertical */,\r\n        _c),\r\n    _b[\"vertical\" /* Vertical */] = (_d = {},\r\n        _d[\"yes\" /* Yes */] = \"vertical\" /* Vertical */,\r\n        _d[\"no\" /* No */] = \"horizontal\" /* Horizontal */,\r\n        _d),\r\n    _b);\r\nvar DirectionLockAction = /** @class */ (function () {\r\n    function DirectionLockAction(directionLockThreshold, freeScroll, eventPassthrough) {\r\n        this.directionLockThreshold = directionLockThreshold;\r\n        this.freeScroll = freeScroll;\r\n        this.eventPassthrough = eventPassthrough;\r\n        this.reset();\r\n    }\r\n    DirectionLockAction.prototype.reset = function () {\r\n        this.directionLocked = \"\" /* Default */;\r\n    };\r\n    DirectionLockAction.prototype.checkMovingDirection = function (absDistX, absDistY, e) {\r\n        this.computeDirectionLock(absDistX, absDistY);\r\n        return this.handleEventPassthrough(e);\r\n    };\r\n    DirectionLockAction.prototype.adjustDelta = function (deltaX, deltaY) {\r\n        if (this.directionLocked === \"horizontal\" /* Horizontal */) {\r\n            deltaY = 0;\r\n        }\r\n        else if (this.directionLocked === \"vertical\" /* Vertical */) {\r\n            deltaX = 0;\r\n        }\r\n        return {\r\n            deltaX: deltaX,\r\n            deltaY: deltaY,\r\n        };\r\n    };\r\n    DirectionLockAction.prototype.computeDirectionLock = function (absDistX, absDistY) {\r\n        // If you are scrolling in one direction, lock it\r\n        if (this.directionLocked === \"\" /* Default */ && !this.freeScroll) {\r\n            if (absDistX > absDistY + this.directionLockThreshold) {\r\n                this.directionLocked = \"horizontal\" /* Horizontal */; // lock horizontally\r\n            }\r\n            else if (absDistY >= absDistX + this.directionLockThreshold) {\r\n                this.directionLocked = \"vertical\" /* Vertical */; // lock vertically\r\n            }\r\n            else {\r\n                this.directionLocked = \"none\" /* None */; // no lock\r\n            }\r\n        }\r\n    };\r\n    DirectionLockAction.prototype.handleEventPassthrough = function (e) {\r\n        var handleMap = DirectionMap[this.directionLocked];\r\n        if (handleMap) {\r\n            if (this.eventPassthrough === handleMap[\"yes\" /* Yes */]) {\r\n                return PassthroughHandlers[\"yes\" /* Yes */](e);\r\n            }\r\n            else if (this.eventPassthrough === handleMap[\"no\" /* No */]) {\r\n                return PassthroughHandlers[\"no\" /* No */](e);\r\n            }\r\n        }\r\n        return false;\r\n    };\r\n    return DirectionLockAction;\r\n}());\n\nvar applyQuadrantTransformation = function (deltaX, deltaY, quadrant) {\r\n    if (quadrant === 2 /* Second */) {\r\n        return [deltaY, -deltaX];\r\n    }\r\n    else if (quadrant === 3 /* Third */) {\r\n        return [-deltaX, -deltaY];\r\n    }\r\n    else if (quadrant === 4 /* Forth */) {\r\n        return [-deltaY, deltaX];\r\n    }\r\n    else {\r\n        return [deltaX, deltaY];\r\n    }\r\n};\r\nvar ScrollerActions = /** @class */ (function () {\r\n    function ScrollerActions(scrollBehaviorX, scrollBehaviorY, actionsHandler, animater, options) {\r\n        this.hooks = new EventEmitter([\r\n            'start',\r\n            'beforeMove',\r\n            'scrollStart',\r\n            'scroll',\r\n            'beforeEnd',\r\n            'end',\r\n            'scrollEnd',\r\n            'contentNotMoved',\r\n            'detectMovingDirection',\r\n            'coordinateTransformation',\r\n        ]);\r\n        this.scrollBehaviorX = scrollBehaviorX;\r\n        this.scrollBehaviorY = scrollBehaviorY;\r\n        this.actionsHandler = actionsHandler;\r\n        this.animater = animater;\r\n        this.options = options;\r\n        this.directionLockAction = new DirectionLockAction(options.directionLockThreshold, options.freeScroll, options.eventPassthrough);\r\n        this.enabled = true;\r\n        this.bindActionsHandler();\r\n    }\r\n    ScrollerActions.prototype.bindActionsHandler = function () {\r\n        var _this = this;\r\n        // [mouse|touch]start event\r\n        this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.start, function (e) {\r\n            if (!_this.enabled)\r\n                return true;\r\n            return _this.handleStart(e);\r\n        });\r\n        // [mouse|touch]move event\r\n        this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.move, function (_a) {\r\n            var deltaX = _a.deltaX, deltaY = _a.deltaY, e = _a.e;\r\n            if (!_this.enabled)\r\n                return true;\r\n            var _b = applyQuadrantTransformation(deltaX, deltaY, _this.options.quadrant), transformateDeltaX = _b[0], transformateDeltaY = _b[1];\r\n            var transformateDeltaData = {\r\n                deltaX: transformateDeltaX,\r\n                deltaY: transformateDeltaY,\r\n            };\r\n            _this.hooks.trigger(_this.hooks.eventTypes.coordinateTransformation, transformateDeltaData);\r\n            return _this.handleMove(transformateDeltaData.deltaX, transformateDeltaData.deltaY, e);\r\n        });\r\n        // [mouse|touch]end event\r\n        this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.end, function (e) {\r\n            if (!_this.enabled)\r\n                return true;\r\n            return _this.handleEnd(e);\r\n        });\r\n        // click\r\n        this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.click, function (e) {\r\n            // handle native click event\r\n            if (_this.enabled && !e._constructed) {\r\n                _this.handleClick(e);\r\n            }\r\n        });\r\n    };\r\n    ScrollerActions.prototype.handleStart = function (e) {\r\n        var timestamp = getNow();\r\n        this.fingerMoved = false;\r\n        this.contentMoved = false;\r\n        this.startTime = timestamp;\r\n        this.directionLockAction.reset();\r\n        this.scrollBehaviorX.start();\r\n        this.scrollBehaviorY.start();\r\n        // force stopping last transition or animation\r\n        this.animater.doStop();\r\n        this.scrollBehaviorX.resetStartPos();\r\n        this.scrollBehaviorY.resetStartPos();\r\n        this.hooks.trigger(this.hooks.eventTypes.start, e);\r\n    };\r\n    ScrollerActions.prototype.handleMove = function (deltaX, deltaY, e) {\r\n        if (this.hooks.trigger(this.hooks.eventTypes.beforeMove, e)) {\r\n            return;\r\n        }\r\n        var absDistX = this.scrollBehaviorX.getAbsDist(deltaX);\r\n        var absDistY = this.scrollBehaviorY.getAbsDist(deltaY);\r\n        var timestamp = getNow();\r\n        // We need to move at least momentumLimitDistance pixels\r\n        // for the scrolling to initiate\r\n        if (this.checkMomentum(absDistX, absDistY, timestamp)) {\r\n            return true;\r\n        }\r\n        if (this.directionLockAction.checkMovingDirection(absDistX, absDistY, e)) {\r\n            this.actionsHandler.setInitiated();\r\n            return true;\r\n        }\r\n        var delta = this.directionLockAction.adjustDelta(deltaX, deltaY);\r\n        var prevX = this.scrollBehaviorX.getCurrentPos();\r\n        var newX = this.scrollBehaviorX.move(delta.deltaX);\r\n        var prevY = this.scrollBehaviorY.getCurrentPos();\r\n        var newY = this.scrollBehaviorY.move(delta.deltaY);\r\n        if (this.hooks.trigger(this.hooks.eventTypes.detectMovingDirection)) {\r\n            return;\r\n        }\r\n        if (!this.fingerMoved) {\r\n            this.fingerMoved = true;\r\n        }\r\n        var positionChanged = newX !== prevX || newY !== prevY;\r\n        if (!this.contentMoved && !positionChanged) {\r\n            this.hooks.trigger(this.hooks.eventTypes.contentNotMoved);\r\n        }\r\n        if (!this.contentMoved && positionChanged) {\r\n            this.contentMoved = true;\r\n            this.hooks.trigger(this.hooks.eventTypes.scrollStart);\r\n        }\r\n        if (this.contentMoved && positionChanged) {\r\n            this.animater.translate({\r\n                x: newX,\r\n                y: newY,\r\n            });\r\n            this.dispatchScroll(timestamp);\r\n        }\r\n    };\r\n    ScrollerActions.prototype.dispatchScroll = function (timestamp) {\r\n        // dispatch scroll in interval time\r\n        if (timestamp - this.startTime > this.options.momentumLimitTime) {\r\n            // refresh time and starting position to initiate a momentum\r\n            this.startTime = timestamp;\r\n            this.scrollBehaviorX.updateStartPos();\r\n            this.scrollBehaviorY.updateStartPos();\r\n            if (this.options.probeType === 1 /* Throttle */) {\r\n                this.hooks.trigger(this.hooks.eventTypes.scroll, this.getCurrentPos());\r\n            }\r\n        }\r\n        // dispatch scroll all the time\r\n        if (this.options.probeType > 1 /* Throttle */) {\r\n            this.hooks.trigger(this.hooks.eventTypes.scroll, this.getCurrentPos());\r\n        }\r\n    };\r\n    ScrollerActions.prototype.checkMomentum = function (absDistX, absDistY, timestamp) {\r\n        return (timestamp - this.endTime > this.options.momentumLimitTime &&\r\n            absDistY < this.options.momentumLimitDistance &&\r\n            absDistX < this.options.momentumLimitDistance);\r\n    };\r\n    ScrollerActions.prototype.handleEnd = function (e) {\r\n        if (this.hooks.trigger(this.hooks.eventTypes.beforeEnd, e)) {\r\n            return;\r\n        }\r\n        var currentPos = this.getCurrentPos();\r\n        this.scrollBehaviorX.updateDirection();\r\n        this.scrollBehaviorY.updateDirection();\r\n        if (this.hooks.trigger(this.hooks.eventTypes.end, e, currentPos)) {\r\n            return true;\r\n        }\r\n        currentPos = this.ensureIntegerPos(currentPos);\r\n        this.animater.translate(currentPos);\r\n        this.endTime = getNow();\r\n        var duration = this.endTime - this.startTime;\r\n        this.hooks.trigger(this.hooks.eventTypes.scrollEnd, currentPos, duration);\r\n    };\r\n    ScrollerActions.prototype.ensureIntegerPos = function (currentPos) {\r\n        this.ensuringInteger = true;\r\n        var x = currentPos.x, y = currentPos.y;\r\n        var _a = this.scrollBehaviorX, minScrollPosX = _a.minScrollPos, maxScrollPosX = _a.maxScrollPos;\r\n        var _b = this.scrollBehaviorY, minScrollPosY = _b.minScrollPos, maxScrollPosY = _b.maxScrollPos;\r\n        x = x > 0 ? Math.ceil(x) : Math.floor(x);\r\n        y = y > 0 ? Math.ceil(y) : Math.floor(y);\r\n        x = between(x, maxScrollPosX, minScrollPosX);\r\n        y = between(y, maxScrollPosY, minScrollPosY);\r\n        return { x: x, y: y };\r\n    };\r\n    ScrollerActions.prototype.handleClick = function (e) {\r\n        if (!preventDefaultExceptionFn(e.target, this.options.preventDefaultException)) {\r\n            maybePrevent(e);\r\n            e.stopPropagation();\r\n        }\r\n    };\r\n    ScrollerActions.prototype.getCurrentPos = function () {\r\n        return {\r\n            x: this.scrollBehaviorX.getCurrentPos(),\r\n            y: this.scrollBehaviorY.getCurrentPos(),\r\n        };\r\n    };\r\n    ScrollerActions.prototype.refresh = function () {\r\n        this.endTime = 0;\r\n    };\r\n    ScrollerActions.prototype.destroy = function () {\r\n        this.hooks.destroy();\r\n    };\r\n    return ScrollerActions;\r\n}());\n\nfunction createActionsHandlerOptions(bsOptions) {\r\n    var options = [\r\n        'click',\r\n        'bindToWrapper',\r\n        'disableMouse',\r\n        'disableTouch',\r\n        'preventDefault',\r\n        'stopPropagation',\r\n        'tagException',\r\n        'preventDefaultException',\r\n        'autoEndDistance',\r\n    ].reduce(function (prev, cur) {\r\n        prev[cur] = bsOptions[cur];\r\n        return prev;\r\n    }, {});\r\n    return options;\r\n}\r\nfunction createBehaviorOptions(bsOptions, extraProp, bounces, rect) {\r\n    var options = [\r\n        'momentum',\r\n        'momentumLimitTime',\r\n        'momentumLimitDistance',\r\n        'deceleration',\r\n        'swipeBounceTime',\r\n        'swipeTime',\r\n        'outOfBoundaryDampingFactor',\r\n        'specifiedIndexAsContent',\r\n    ].reduce(function (prev, cur) {\r\n        prev[cur] = bsOptions[cur];\r\n        return prev;\r\n    }, {});\r\n    // add extra property\r\n    options.scrollable = !!bsOptions[extraProp];\r\n    options.bounces = bounces;\r\n    options.rect = rect;\r\n    return options;\r\n}\n\nfunction bubbling(source, target, events) {\r\n    events.forEach(function (event) {\r\n        var sourceEvent;\r\n        var targetEvent;\r\n        if (typeof event === 'string') {\r\n            sourceEvent = targetEvent = event;\r\n        }\r\n        else {\r\n            sourceEvent = event.source;\r\n            targetEvent = event.target;\r\n        }\r\n        source.on(sourceEvent, function () {\r\n            var args = [];\r\n            for (var _i = 0; _i < arguments.length; _i++) {\r\n                args[_i] = arguments[_i];\r\n            }\r\n            return target.trigger.apply(target, __spreadArrays([targetEvent], args));\r\n        });\r\n    });\r\n}\n\nfunction isSamePoint(startPoint, endPoint) {\r\n    // keys of startPoint and endPoint should be equal\r\n    var keys = Object.keys(startPoint);\r\n    for (var _i = 0, keys_1 = keys; _i < keys_1.length; _i++) {\r\n        var key = keys_1[_i];\r\n        if (startPoint[key] !== endPoint[key])\r\n            return false;\r\n    }\r\n    return true;\r\n}\n\nvar MIN_SCROLL_DISTANCE = 1;\r\nvar Scroller = /** @class */ (function () {\r\n    function Scroller(wrapper, content, options) {\r\n        this.wrapper = wrapper;\r\n        this.content = content;\r\n        this.resizeTimeout = 0;\r\n        this.hooks = new EventEmitter([\r\n            'beforeStart',\r\n            'beforeMove',\r\n            'beforeScrollStart',\r\n            'scrollStart',\r\n            'scroll',\r\n            'beforeEnd',\r\n            'scrollEnd',\r\n            'resize',\r\n            'touchEnd',\r\n            'end',\r\n            'flick',\r\n            'scrollCancel',\r\n            'momentum',\r\n            'scrollTo',\r\n            'minDistanceScroll',\r\n            'scrollToElement',\r\n            'beforeRefresh',\r\n        ]);\r\n        this.options = options;\r\n        var _a = this.options.bounce, left = _a.left, right = _a.right, top = _a.top, bottom = _a.bottom;\r\n        // direction X\r\n        this.scrollBehaviorX = new Behavior(wrapper, content, createBehaviorOptions(options, 'scrollX', [left, right], {\r\n            size: 'width',\r\n            position: 'left',\r\n        }));\r\n        // direction Y\r\n        this.scrollBehaviorY = new Behavior(wrapper, content, createBehaviorOptions(options, 'scrollY', [top, bottom], {\r\n            size: 'height',\r\n            position: 'top',\r\n        }));\r\n        this.translater = new Translater(this.content);\r\n        this.animater = createAnimater(this.content, this.translater, this.options);\r\n        this.actionsHandler = new ActionsHandler(this.options.bindToTarget ? this.content : wrapper, createActionsHandlerOptions(this.options));\r\n        this.actions = new ScrollerActions(this.scrollBehaviorX, this.scrollBehaviorY, this.actionsHandler, this.animater, this.options);\r\n        var resizeHandler = this.resize.bind(this);\r\n        this.resizeRegister = new EventRegister(window, [\r\n            {\r\n                name: 'orientationchange',\r\n                handler: resizeHandler,\r\n            },\r\n            {\r\n                name: 'resize',\r\n                handler: resizeHandler,\r\n            },\r\n        ]);\r\n        this.registerTransitionEnd();\r\n        this.init();\r\n    }\r\n    Scroller.prototype.init = function () {\r\n        var _this = this;\r\n        this.bindTranslater();\r\n        this.bindAnimater();\r\n        this.bindActions();\r\n        // enable pointer events when scrolling ends\r\n        this.hooks.on(this.hooks.eventTypes.scrollEnd, function () {\r\n            _this.togglePointerEvents(true);\r\n        });\r\n    };\r\n    Scroller.prototype.registerTransitionEnd = function () {\r\n        this.transitionEndRegister = new EventRegister(this.content, [\r\n            {\r\n                name: style.transitionEnd,\r\n                handler: this.transitionEnd.bind(this),\r\n            },\r\n        ]);\r\n    };\r\n    Scroller.prototype.bindTranslater = function () {\r\n        var _this = this;\r\n        var hooks = this.translater.hooks;\r\n        hooks.on(hooks.eventTypes.beforeTranslate, function (transformStyle) {\r\n            if (_this.options.translateZ) {\r\n                transformStyle.push(_this.options.translateZ);\r\n            }\r\n        });\r\n        // disable pointer events when scrolling\r\n        hooks.on(hooks.eventTypes.translate, function (pos) {\r\n            var prevPos = _this.getCurrentPos();\r\n            _this.updatePositions(pos);\r\n            // scrollEnd will dispatch when scroll is force stopping in touchstart handler\r\n            // so in touchend handler, don't toggle pointer-events\r\n            if (_this.actions.ensuringInteger === true) {\r\n                _this.actions.ensuringInteger = false;\r\n                return;\r\n            }\r\n            // a valid translate\r\n            if (pos.x !== prevPos.x || pos.y !== prevPos.y) {\r\n                _this.togglePointerEvents(false);\r\n            }\r\n        });\r\n    };\r\n    Scroller.prototype.bindAnimater = function () {\r\n        var _this = this;\r\n        // reset position\r\n        this.animater.hooks.on(this.animater.hooks.eventTypes.end, function (pos) {\r\n            if (!_this.resetPosition(_this.options.bounceTime)) {\r\n                _this.animater.setPending(false);\r\n                _this.hooks.trigger(_this.hooks.eventTypes.scrollEnd, pos);\r\n            }\r\n        });\r\n        bubbling(this.animater.hooks, this.hooks, [\r\n            {\r\n                source: this.animater.hooks.eventTypes.move,\r\n                target: this.hooks.eventTypes.scroll,\r\n            },\r\n            {\r\n                source: this.animater.hooks.eventTypes.forceStop,\r\n                target: this.hooks.eventTypes.scrollEnd,\r\n            },\r\n        ]);\r\n    };\r\n    Scroller.prototype.bindActions = function () {\r\n        var _this = this;\r\n        var actions = this.actions;\r\n        bubbling(actions.hooks, this.hooks, [\r\n            {\r\n                source: actions.hooks.eventTypes.start,\r\n                target: this.hooks.eventTypes.beforeStart,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.start,\r\n                target: this.hooks.eventTypes.beforeScrollStart,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.beforeMove,\r\n                target: this.hooks.eventTypes.beforeMove,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.scrollStart,\r\n                target: this.hooks.eventTypes.scrollStart,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.scroll,\r\n                target: this.hooks.eventTypes.scroll,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.beforeEnd,\r\n                target: this.hooks.eventTypes.beforeEnd,\r\n            },\r\n        ]);\r\n        actions.hooks.on(actions.hooks.eventTypes.end, function (e, pos) {\r\n            _this.hooks.trigger(_this.hooks.eventTypes.touchEnd, pos);\r\n            if (_this.hooks.trigger(_this.hooks.eventTypes.end, pos)) {\r\n                return true;\r\n            }\r\n            // check if it is a click operation\r\n            if (!actions.fingerMoved) {\r\n                _this.hooks.trigger(_this.hooks.eventTypes.scrollCancel);\r\n                if (_this.checkClick(e)) {\r\n                    return true;\r\n                }\r\n            }\r\n            // reset if we are outside of the boundaries\r\n            if (_this.resetPosition(_this.options.bounceTime, ease.bounce)) {\r\n                _this.animater.setForceStopped(false);\r\n                return true;\r\n            }\r\n        });\r\n        actions.hooks.on(actions.hooks.eventTypes.scrollEnd, function (pos, duration) {\r\n            var deltaX = Math.abs(pos.x - _this.scrollBehaviorX.startPos);\r\n            var deltaY = Math.abs(pos.y - _this.scrollBehaviorY.startPos);\r\n            if (_this.checkFlick(duration, deltaX, deltaY)) {\r\n                _this.animater.setForceStopped(false);\r\n                _this.hooks.trigger(_this.hooks.eventTypes.flick);\r\n                return;\r\n            }\r\n            if (_this.momentum(pos, duration)) {\r\n                _this.animater.setForceStopped(false);\r\n                return;\r\n            }\r\n            if (actions.contentMoved) {\r\n                _this.hooks.trigger(_this.hooks.eventTypes.scrollEnd, pos);\r\n            }\r\n            if (_this.animater.forceStopped) {\r\n                _this.animater.setForceStopped(false);\r\n            }\r\n        });\r\n    };\r\n    Scroller.prototype.checkFlick = function (duration, deltaX, deltaY) {\r\n        var flickMinMovingDistance = 1; // distinguish flick from click\r\n        if (this.hooks.events.flick.length > 1 &&\r\n            duration < this.options.flickLimitTime &&\r\n            deltaX < this.options.flickLimitDistance &&\r\n            deltaY < this.options.flickLimitDistance &&\r\n            (deltaY > flickMinMovingDistance || deltaX > flickMinMovingDistance)) {\r\n            return true;\r\n        }\r\n    };\r\n    Scroller.prototype.momentum = function (pos, duration) {\r\n        var meta = {\r\n            time: 0,\r\n            easing: ease.swiper,\r\n            newX: pos.x,\r\n            newY: pos.y,\r\n        };\r\n        // start momentum animation if needed\r\n        var momentumX = this.scrollBehaviorX.end(duration);\r\n        var momentumY = this.scrollBehaviorY.end(duration);\r\n        meta.newX = isUndef(momentumX.destination)\r\n            ? meta.newX\r\n            : momentumX.destination;\r\n        meta.newY = isUndef(momentumY.destination)\r\n            ? meta.newY\r\n            : momentumY.destination;\r\n        meta.time = Math.max(momentumX.duration, momentumY.duration);\r\n        this.hooks.trigger(this.hooks.eventTypes.momentum, meta, this);\r\n        // when x or y changed, do momentum animation now!\r\n        if (meta.newX !== pos.x || meta.newY !== pos.y) {\r\n            // change easing function when scroller goes out of the boundaries\r\n            if (meta.newX > this.scrollBehaviorX.minScrollPos ||\r\n                meta.newX < this.scrollBehaviorX.maxScrollPos ||\r\n                meta.newY > this.scrollBehaviorY.minScrollPos ||\r\n                meta.newY < this.scrollBehaviorY.maxScrollPos) {\r\n                meta.easing = ease.swipeBounce;\r\n            }\r\n            this.scrollTo(meta.newX, meta.newY, meta.time, meta.easing);\r\n            return true;\r\n        }\r\n    };\r\n    Scroller.prototype.checkClick = function (e) {\r\n        var cancelable = {\r\n            preventClick: this.animater.forceStopped,\r\n        };\r\n        // we scrolled less than momentumLimitDistance pixels\r\n        if (this.hooks.trigger(this.hooks.eventTypes.checkClick)) {\r\n            this.animater.setForceStopped(false);\r\n            return true;\r\n        }\r\n        if (!cancelable.preventClick) {\r\n            var _dblclick = this.options.dblclick;\r\n            var dblclickTrigged = false;\r\n            if (_dblclick && this.lastClickTime) {\r\n                var _a = _dblclick.delay, delay = _a === void 0 ? 300 : _a;\r\n                if (getNow() - this.lastClickTime < delay) {\r\n                    dblclickTrigged = true;\r\n                    dblclick(e);\r\n                }\r\n            }\r\n            if (this.options.tap) {\r\n                tap(e, this.options.tap);\r\n            }\r\n            if (this.options.click &&\r\n                !preventDefaultExceptionFn(e.target, this.options.preventDefaultException)) {\r\n                click(e);\r\n            }\r\n            this.lastClickTime = dblclickTrigged ? null : getNow();\r\n            return true;\r\n        }\r\n        return false;\r\n    };\r\n    Scroller.prototype.resize = function () {\r\n        var _this = this;\r\n        if (!this.actions.enabled) {\r\n            return;\r\n        }\r\n        // fix a scroll problem under Android condition\r\n        /* istanbul ignore if  */\r\n        if (isAndroid) {\r\n            this.wrapper.scrollTop = 0;\r\n        }\r\n        clearTimeout(this.resizeTimeout);\r\n        this.resizeTimeout = window.setTimeout(function () {\r\n            _this.hooks.trigger(_this.hooks.eventTypes.resize);\r\n        }, this.options.resizePolling);\r\n    };\r\n    /* istanbul ignore next */\r\n    Scroller.prototype.transitionEnd = function (e) {\r\n        if (e.target !== this.content || !this.animater.pending) {\r\n            return;\r\n        }\r\n        var animater = this.animater;\r\n        animater.transitionTime();\r\n        if (!this.resetPosition(this.options.bounceTime, ease.bounce)) {\r\n            this.animater.setPending(false);\r\n            if (this.options.probeType !== 3 /* Realtime */) {\r\n                this.hooks.trigger(this.hooks.eventTypes.scrollEnd, this.getCurrentPos());\r\n            }\r\n        }\r\n    };\r\n    Scroller.prototype.togglePointerEvents = function (enabled) {\r\n        if (enabled === void 0) { enabled = true; }\r\n        var el = this.content.children.length\r\n            ? this.content.children\r\n            : [this.content];\r\n        var pointerEvents = enabled ? 'auto' : 'none';\r\n        for (var i = 0; i < el.length; i++) {\r\n            var node = el[i];\r\n            // ignore BetterScroll instance's wrapper DOM\r\n            /* istanbul ignore if  */\r\n            if (node.isBScrollContainer) {\r\n                continue;\r\n            }\r\n            node.style.pointerEvents = pointerEvents;\r\n        }\r\n    };\r\n    Scroller.prototype.refresh = function (content) {\r\n        var contentChanged = this.setContent(content);\r\n        this.hooks.trigger(this.hooks.eventTypes.beforeRefresh);\r\n        this.scrollBehaviorX.refresh(content);\r\n        this.scrollBehaviorY.refresh(content);\r\n        if (contentChanged) {\r\n            this.translater.setContent(content);\r\n            this.animater.setContent(content);\r\n            this.transitionEndRegister.destroy();\r\n            this.registerTransitionEnd();\r\n            if (this.options.bindToTarget) {\r\n                this.actionsHandler.setContent(content);\r\n            }\r\n        }\r\n        this.actions.refresh();\r\n        this.wrapperOffset = offset(this.wrapper);\r\n    };\r\n    Scroller.prototype.setContent = function (content) {\r\n        var contentChanged = content !== this.content;\r\n        if (contentChanged) {\r\n            this.content = content;\r\n        }\r\n        return contentChanged;\r\n    };\r\n    Scroller.prototype.scrollBy = function (deltaX, deltaY, time, easing) {\r\n        if (time === void 0) { time = 0; }\r\n        var _a = this.getCurrentPos(), x = _a.x, y = _a.y;\r\n        easing = !easing ? ease.bounce : easing;\r\n        deltaX += x;\r\n        deltaY += y;\r\n        this.scrollTo(deltaX, deltaY, time, easing);\r\n    };\r\n    Scroller.prototype.scrollTo = function (x, y, time, easing, extraTransform) {\r\n        if (time === void 0) { time = 0; }\r\n        if (easing === void 0) { easing = ease.bounce; }\r\n        if (extraTransform === void 0) { extraTransform = {\r\n            start: {},\r\n            end: {},\r\n        }; }\r\n        var easingFn = this.options.useTransition ? easing.style : easing.fn;\r\n        var currentPos = this.getCurrentPos();\r\n        var startPoint = __assign({ x: currentPos.x, y: currentPos.y }, extraTransform.start);\r\n        var endPoint = __assign({ x: x,\r\n            y: y }, extraTransform.end);\r\n        this.hooks.trigger(this.hooks.eventTypes.scrollTo, endPoint);\r\n        // it is an useless move\r\n        if (isSamePoint(startPoint, endPoint))\r\n            return;\r\n        var deltaX = Math.abs(endPoint.x - startPoint.x);\r\n        var deltaY = Math.abs(endPoint.y - startPoint.y);\r\n        // considering of browser compatibility for decimal transform value\r\n        // force translating immediately\r\n        if (deltaX < MIN_SCROLL_DISTANCE && deltaY < MIN_SCROLL_DISTANCE) {\r\n            time = 0;\r\n            this.hooks.trigger(this.hooks.eventTypes.minDistanceScroll);\r\n        }\r\n        this.animater.move(startPoint, endPoint, time, easingFn);\r\n    };\r\n    Scroller.prototype.scrollToElement = function (el, time, offsetX, offsetY, easing) {\r\n        var targetEle = getElement(el);\r\n        var pos = offset(targetEle);\r\n        var getOffset = function (offset, size, wrapperSize) {\r\n            if (typeof offset === 'number') {\r\n                return offset;\r\n            }\r\n            // if offsetX/Y are true we center the element to the screen\r\n            return offset ? Math.round(size / 2 - wrapperSize / 2) : 0;\r\n        };\r\n        offsetX = getOffset(offsetX, targetEle.offsetWidth, this.wrapper.offsetWidth);\r\n        offsetY = getOffset(offsetY, targetEle.offsetHeight, this.wrapper.offsetHeight);\r\n        var getPos = function (pos, wrapperPos, offset, scrollBehavior) {\r\n            pos -= wrapperPos;\r\n            pos = scrollBehavior.adjustPosition(pos - offset);\r\n            return pos;\r\n        };\r\n        pos.left = getPos(pos.left, this.wrapperOffset.left, offsetX, this.scrollBehaviorX);\r\n        pos.top = getPos(pos.top, this.wrapperOffset.top, offsetY, this.scrollBehaviorY);\r\n        if (this.hooks.trigger(this.hooks.eventTypes.scrollToElement, targetEle, pos)) {\r\n            return;\r\n        }\r\n        this.scrollTo(pos.left, pos.top, time, easing);\r\n    };\r\n    Scroller.prototype.resetPosition = function (time, easing) {\r\n        if (time === void 0) { time = 0; }\r\n        if (easing === void 0) { easing = ease.bounce; }\r\n        var _a = this.scrollBehaviorX.checkInBoundary(), x = _a.position, xInBoundary = _a.inBoundary;\r\n        var _b = this.scrollBehaviorY.checkInBoundary(), y = _b.position, yInBoundary = _b.inBoundary;\r\n        if (xInBoundary && yInBoundary) {\r\n            return false;\r\n        }\r\n        /* istanbul ignore if  */\r\n        if (isIOSBadVersion) {\r\n            // fix ios 13.4 bouncing\r\n            // see it in issues 982\r\n            this.reflow();\r\n        }\r\n        // out of boundary\r\n        this.scrollTo(x, y, time, easing);\r\n        return true;\r\n    };\r\n    /* istanbul ignore next */\r\n    Scroller.prototype.reflow = function () {\r\n        this._reflow = this.content.offsetHeight;\r\n    };\r\n    Scroller.prototype.updatePositions = function (pos) {\r\n        this.scrollBehaviorX.updatePosition(pos.x);\r\n        this.scrollBehaviorY.updatePosition(pos.y);\r\n    };\r\n    Scroller.prototype.getCurrentPos = function () {\r\n        return this.actions.getCurrentPos();\r\n    };\r\n    Scroller.prototype.enable = function () {\r\n        this.actions.enabled = true;\r\n    };\r\n    Scroller.prototype.disable = function () {\r\n        cancelAnimationFrame(this.animater.timer);\r\n        this.actions.enabled = false;\r\n    };\r\n    Scroller.prototype.destroy = function () {\r\n        var _this = this;\r\n        var keys = [\r\n            'resizeRegister',\r\n            'transitionEndRegister',\r\n            'actionsHandler',\r\n            'actions',\r\n            'hooks',\r\n            'animater',\r\n            'translater',\r\n            'scrollBehaviorX',\r\n            'scrollBehaviorY',\r\n        ];\r\n        keys.forEach(function (key) { return _this[key].destroy(); });\r\n    };\r\n    return Scroller;\r\n}());\n\nvar BScrollConstructor = /** @class */ (function (_super) {\r\n    __extends(BScrollConstructor, _super);\r\n    function BScrollConstructor(el, options) {\r\n        var _this = _super.call(this, [\r\n            'refresh',\r\n            'contentChanged',\r\n            'enable',\r\n            'disable',\r\n            'beforeScrollStart',\r\n            'scrollStart',\r\n            'scroll',\r\n            'scrollEnd',\r\n            'scrollCancel',\r\n            'touchEnd',\r\n            'flick',\r\n            'destroy'\r\n        ]) || this;\r\n        var wrapper = getElement(el);\r\n        if (!wrapper) {\r\n            warn('Can not resolve the wrapper DOM.');\r\n            return _this;\r\n        }\r\n        _this.plugins = {};\r\n        _this.options = new OptionsConstructor().merge(options).process();\r\n        if (!_this.setContent(wrapper).valid) {\r\n            return _this;\r\n        }\r\n        _this.hooks = new EventEmitter([\r\n            'refresh',\r\n            'enable',\r\n            'disable',\r\n            'destroy',\r\n            'beforeInitialScrollTo',\r\n            'contentChanged'\r\n        ]);\r\n        _this.init(wrapper);\r\n        return _this;\r\n    }\r\n    BScrollConstructor.use = function (ctor) {\r\n        var name = ctor.pluginName;\r\n        var installed = BScrollConstructor.plugins.some(function (plugin) { return ctor === plugin.ctor; });\r\n        if (installed)\r\n            return BScrollConstructor;\r\n        if (isUndef(name)) {\r\n            warn(\"Plugin Class must specify plugin's name in static property by 'pluginName' field.\");\r\n            return BScrollConstructor;\r\n        }\r\n        BScrollConstructor.pluginsMap[name] = true;\r\n        BScrollConstructor.plugins.push({\r\n            name: name,\r\n            applyOrder: ctor.applyOrder,\r\n            ctor: ctor\r\n        });\r\n        return BScrollConstructor;\r\n    };\r\n    BScrollConstructor.prototype.setContent = function (wrapper) {\r\n        var contentChanged = false;\r\n        var valid = true;\r\n        var content = wrapper.children[this.options.specifiedIndexAsContent];\r\n        if (!content) {\r\n            warn('The wrapper need at least one child element to be content element to scroll.');\r\n            valid = false;\r\n        }\r\n        else {\r\n            contentChanged = this.content !== content;\r\n            if (contentChanged) {\r\n                this.content = content;\r\n            }\r\n        }\r\n        return {\r\n            valid: valid,\r\n            contentChanged: contentChanged\r\n        };\r\n    };\r\n    BScrollConstructor.prototype.init = function (wrapper) {\r\n        var _this = this;\r\n        this.wrapper = wrapper;\r\n        // mark wrapper to recognize bs instance by DOM attribute\r\n        wrapper.isBScrollContainer = true;\r\n        this.scroller = new Scroller(wrapper, this.content, this.options);\r\n        this.scroller.hooks.on(this.scroller.hooks.eventTypes.resize, function () {\r\n            _this.refresh();\r\n        });\r\n        this.eventBubbling();\r\n        this.handleAutoBlur();\r\n        this.enable();\r\n        this.proxy(propertiesConfig);\r\n        this.applyPlugins();\r\n        // maybe boundary has changed, should refresh\r\n        this.refreshWithoutReset(this.content);\r\n        var _a = this.options, startX = _a.startX, startY = _a.startY;\r\n        var position = {\r\n            x: startX,\r\n            y: startY\r\n        };\r\n        // maybe plugins want to control scroll position\r\n        if (this.hooks.trigger(this.hooks.eventTypes.beforeInitialScrollTo, position)) {\r\n            return;\r\n        }\r\n        this.scroller.scrollTo(position.x, position.y);\r\n    };\r\n    BScrollConstructor.prototype.applyPlugins = function () {\r\n        var _this = this;\r\n        var options = this.options;\r\n        BScrollConstructor.plugins\r\n            .sort(function (a, b) {\r\n            var _a;\r\n            var applyOrderMap = (_a = {},\r\n                _a[\"pre\" /* Pre */] = -1,\r\n                _a[\"post\" /* Post */] = 1,\r\n                _a);\r\n            var aOrder = a.applyOrder ? applyOrderMap[a.applyOrder] : 0;\r\n            var bOrder = b.applyOrder ? applyOrderMap[b.applyOrder] : 0;\r\n            return aOrder - bOrder;\r\n        })\r\n            .forEach(function (item) {\r\n            var ctor = item.ctor;\r\n            if (options[item.name] && typeof ctor === 'function') {\r\n                _this.plugins[item.name] = new ctor(_this);\r\n            }\r\n        });\r\n    };\r\n    BScrollConstructor.prototype.handleAutoBlur = function () {\r\n        /* istanbul ignore if  */\r\n        if (this.options.autoBlur) {\r\n            this.on(this.eventTypes.beforeScrollStart, function () {\r\n                var activeElement = document.activeElement;\r\n                if (activeElement &&\r\n                    (activeElement.tagName === 'INPUT' ||\r\n                        activeElement.tagName === 'TEXTAREA')) {\r\n                    activeElement.blur();\r\n                }\r\n            });\r\n        }\r\n    };\r\n    BScrollConstructor.prototype.eventBubbling = function () {\r\n        bubbling(this.scroller.hooks, this, [\r\n            this.eventTypes.beforeScrollStart,\r\n            this.eventTypes.scrollStart,\r\n            this.eventTypes.scroll,\r\n            this.eventTypes.scrollEnd,\r\n            this.eventTypes.scrollCancel,\r\n            this.eventTypes.touchEnd,\r\n            this.eventTypes.flick\r\n        ]);\r\n    };\r\n    BScrollConstructor.prototype.refreshWithoutReset = function (content) {\r\n        this.scroller.refresh(content);\r\n        this.hooks.trigger(this.hooks.eventTypes.refresh, content);\r\n        this.trigger(this.eventTypes.refresh, content);\r\n    };\r\n    BScrollConstructor.prototype.proxy = function (propertiesConfig) {\r\n        var _this = this;\r\n        propertiesConfig.forEach(function (_a) {\r\n            var key = _a.key, sourceKey = _a.sourceKey;\r\n            propertiesProxy(_this, sourceKey, key);\r\n        });\r\n    };\r\n    BScrollConstructor.prototype.refresh = function () {\r\n        var _a = this.setContent(this.wrapper), contentChanged = _a.contentChanged, valid = _a.valid;\r\n        if (valid) {\r\n            var content = this.content;\r\n            this.refreshWithoutReset(content);\r\n            if (contentChanged) {\r\n                this.hooks.trigger(this.hooks.eventTypes.contentChanged, content);\r\n                this.trigger(this.eventTypes.contentChanged, content);\r\n            }\r\n            this.scroller.resetPosition();\r\n        }\r\n    };\r\n    BScrollConstructor.prototype.enable = function () {\r\n        this.scroller.enable();\r\n        this.hooks.trigger(this.hooks.eventTypes.enable);\r\n        this.trigger(this.eventTypes.enable);\r\n    };\r\n    BScrollConstructor.prototype.disable = function () {\r\n        this.scroller.disable();\r\n        this.hooks.trigger(this.hooks.eventTypes.disable);\r\n        this.trigger(this.eventTypes.disable);\r\n    };\r\n    BScrollConstructor.prototype.destroy = function () {\r\n        this.hooks.trigger(this.hooks.eventTypes.destroy);\r\n        this.trigger(this.eventTypes.destroy);\r\n        this.scroller.destroy();\r\n    };\r\n    BScrollConstructor.prototype.eventRegister = function (names) {\r\n        this.registerType(names);\r\n    };\r\n    BScrollConstructor.plugins = [];\r\n    BScrollConstructor.pluginsMap = {};\r\n    return BScrollConstructor;\r\n}(EventEmitter));\r\nfunction createBScroll(el, options) {\r\n    var bs = new BScrollConstructor(el, options);\r\n    return bs;\r\n}\r\ncreateBScroll.use = BScrollConstructor.use;\r\ncreateBScroll.plugins = BScrollConstructor.plugins;\r\ncreateBScroll.pluginsMap = BScrollConstructor.pluginsMap;\r\nvar BScroll = createBScroll;\n\nexport { Behavior, CustomOptions, createBScroll, BScroll as default };\n"], "mappings": ";;;AAqBA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUA,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA;AAAG,UAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,QAAAD,GAAE,KAAKC,GAAE;AAAA,EAAI;AACpG,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEA,SAAS,UAAU,GAAG,GAAG;AACrB,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAEA,IAAI,WAAW,WAAW;AACtB,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU;AACd,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAG,YAAE,KAAK,EAAE;AAAA,IAC9E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,iBAAiB;AACtB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI;AAAK,SAAK,UAAU,GAAG;AAC7E,WAAS,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI;AACzC,aAAS,IAAI,UAAU,IAAI,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,KAAK;AAC1D,QAAE,KAAK,EAAE;AACjB,SAAO;AACX;AAEA,IAAI,mBAAmB;AAAA,EACnB;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AACJ;AAEA,SAAS,KAAK,KAAK;AACf,UAAQ,MAAM,qBAAqB,GAAG;AAC1C;AAGA,IAAI,YAAY,OAAO,WAAW;AAClC,IAAI,KAAK,aAAa,UAAU,UAAU,YAAY;AACtD,IAAI,mBAAmB,CAAC,EAAE,MAAM,iBAAiB,KAAK,EAAE;AACxD,IAAI,YAAY,MAAM,GAAG,QAAQ,SAAS,IAAI;AAE9C,IAAI,kBAAmB,WAAY;AAC/B,MAAI,OAAO,OAAO,UAAU;AACxB,QAAI,QAAQ;AACZ,QAAI,UAAU,MAAM,KAAK,EAAE;AAC3B,QAAI,CAAC;AACD,aAAO;AACX,QAAI,QAAQ,QAAQ,GAAG,MAAM,GAAG,EAAE,IAAI,SAAU,MAAM;AAClD,aAAO,SAAS,MAAM,EAAE;AAAA,IAC5B,CAAC;AAED,WAAO,CAAC,EAAE,MAAM,OAAO,MAAM,MAAM,MAAM;AAAA,EAC7C;AACA,SAAO;AACX,EAAG;AAEH,IAAI,kBAAkB;AAEtB,IAAI,WAAW;AACP,cAAY;AAChB,MAAI;AACI,WAAO,CAAC;AACZ,WAAO,eAAe,MAAM,WAAW;AAAA,MACnC,KAAK,WAAY;AACb,0BAAkB;AAAA,MACtB;AAAA,IACJ,CAAC;AACD,WAAO,iBAAiB,WAAW,WAAY;AAAA,IAAE,GAAG,IAAI;AAAA,EAC5D,SACO,GAAP;AAAA,EAAY;AAChB;AAXQ;AAEI;AAWZ,SAAS,SAAS;AACd,SAAO,OAAO,eACV,OAAO,YAAY,OACnB,OAAO,YAAY,SACjB,OAAO,YAAY,IAAI,IAAI,OAAO,YAAY,OAAO,kBACrD,CAAC,IAAI,KAAK;AACpB;AACA,IAAI,SAAS,SAAU,QAAQ,QAAQ;AACnC,WAAS,OAAO,QAAQ;AACpB,WAAO,OAAO,OAAO;AAAA,EACzB;AACA,SAAO;AACX;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,MAAM,UAAa,MAAM;AACpC;AACA,SAAS,QAAQ,GAAG,KAAK,KAAK;AAC1B,MAAI,IAAI,KAAK;AACT,WAAO;AAAA,EACX;AACA,MAAI,IAAI,KAAK;AACT,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,IAAI,eAAgB,aAChB,SAAS,cAAc,KAAK,EAAE;AAClC,IAAI,SAAU,WAAY;AAEtB,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,MAAI,iBAAiB;AAAA,IACjB;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,EACJ;AACA,WAAS,KAAK,GAAG,mBAAmB,gBAAgB,KAAK,iBAAiB,QAAQ,MAAM;AACpF,QAAI,MAAM,iBAAiB;AAC3B,QAAI,aAAa,IAAI,WAAW,QAAW;AACvC,aAAO,IAAI;AAAA,IACf;AAAA,EACJ;AAEA,SAAO;AACX,EAAG;AAEH,SAAS,YAAYC,QAAO;AACxB,MAAI,WAAW,OAAO;AAClB,WAAOA;AAAA,EACX;AACA,MAAI,WAAW,YAAY;AACvB,QAAIA,WAAU,iBAAiB;AAC3B,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX;AACA,SAAO,SAASA,OAAM,OAAO,CAAC,EAAE,YAAY,IAAIA,OAAM,OAAO,CAAC;AAClE;AACA,SAAS,WAAW,IAAI;AACpB,SAAQ,OAAO,OAAO,WAAW,SAAS,cAAc,EAAE,IAAI;AAClE;AACA,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACrC,MAAI,aAAa,kBACX;AAAA,IACE,SAAS;AAAA,IACT,SAAS,CAAC,CAAC;AAAA,EACf,IACE,CAAC,CAAC;AACR,KAAG,iBAAiB,MAAM,IAAI,UAAU;AAC5C;AACA,SAAS,YAAY,IAAI,MAAM,IAAI,SAAS;AACxC,KAAG,oBAAoB,MAAM,IAAI;AAAA,IAC7B,SAAS,CAAC,CAAC;AAAA,EACf,CAAC;AACL;AACA,SAAS,aAAa,GAAG;AACrB,MAAI,EAAE,YAAY;AACd,MAAE,eAAe;AAAA,EACrB;AACJ;AACA,SAAS,OAAO,IAAI;AAChB,MAAI,OAAO;AACX,MAAI,MAAM;AACV,SAAO,IAAI;AACP,YAAQ,GAAG;AACX,WAAO,GAAG;AACV,SAAK,GAAG;AAAA,EACZ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AACA,UAAU,WAAW,aAAa,MAAM,OAAO,YAAY,IAAI,MAAM;AACrE,IAAI,YAAY,YAAY,WAAW;AACvC,IAAI,aAAa,YAAY,YAAY;AACzC,IAAI,iBAAiB,aAAa,YAAY,aAAa,KAAK;AAEhE,IAAI,WAAW,cAAc,kBAAkB,UAAU;AACzD,IAAI,gBAAgB,aAAa,cAAc;AAC/C,IAAI,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,0BAA0B,YAAY,0BAA0B;AAAA,EAChE,oBAAoB,YAAY,oBAAoB;AAAA,EACpD,iBAAiB,YAAY,iBAAiB;AAAA,EAC9C,iBAAiB,YAAY,iBAAiB;AAAA,EAC9C,eAAe,YAAY,eAAe;AAAA,EAC1C,oBAAoB,YAAY,oBAAoB;AACxD;AACA,IAAI,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AACb;AACA,SAAS,QAAQ,IAAI;AAEjB,MAAI,cAAc,OAAO,YAAY;AACjC,QAAI,OAAO,GAAG,sBAAsB;AACpC,WAAO;AAAA,MACH,KAAK,KAAK;AAAA,MACV,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACjB;AAAA,EACJ,OACK;AACD,WAAO;AAAA,MACH,KAAK,GAAG;AAAA,MACR,MAAM,GAAG;AAAA,MACT,OAAO,GAAG;AAAA,MACV,QAAQ,GAAG;AAAA,IACf;AAAA,EACJ;AACJ;AACA,SAAS,0BAA0B,IAAI,YAAY;AAC/C,WAAS,KAAK,YAAY;AACtB,QAAI,WAAW,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAI,iBAAiB;AACrB,SAAS,IAAI,GAAG,WAAW;AACvB,MAAI,KAAK,SAAS,YAAY,OAAO;AACrC,KAAG,UAAU,WAAW,MAAM,IAAI;AAClC,KAAG,QAAQ,EAAE;AACb,KAAG,QAAQ,EAAE;AACb,IAAE,OAAO,cAAc,EAAE;AAC7B;AACA,SAAS,MAAM,GAAG,OAAO;AACrB,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAS;AACzC,MAAI;AACJ,MAAI,EAAE,SAAS,WAAW;AACtB,kBAAc;AAAA,EAClB,WACS,EAAE,SAAS,cAAc,EAAE,SAAS,eAAe;AACxD,kBAAc,EAAE,eAAe;AAAA,EACnC;AACA,MAAI,SAAS,CAAC;AACd,MAAI,aAAa;AACb,WAAO,UAAU,YAAY,WAAW;AACxC,WAAO,UAAU,YAAY,WAAW;AACxC,WAAO,UAAU,YAAY,WAAW;AACxC,WAAO,UAAU,YAAY,WAAW;AAAA,EAC5C;AACA,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,aAAa;AACjB,MAAI,UAAU,EAAE,SAAS,WAAW,EAAE,UAAU,SAAS,EAAE,QAAQ,UAAU,EAAE;AAC/E,MAAI,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,MAAI,OAAO,eAAe,aAAa;AACnC,QAAI;AACA,WAAK,IAAI,WAAW,OAAO,OAAO,SAAS;AAAA,QAAE;AAAA,QACzC;AAAA,MAAuB,GAAG,cAAc,GAAG,MAAM,CAAC;AAAA,IAC1D,SACOC,IAAP;AAEI,kBAAY;AAAA,IAChB;AAAA,EACJ,OACK;AACD,gBAAY;AAAA,EAChB;AACA,WAAS,cAAc;AACnB,SAAK,SAAS,YAAY,OAAO;AACjC,OAAG,UAAU,OAAO,SAAS,UAAU;AACvC,WAAO,IAAI,MAAM;AAAA,EACrB;AAEA,KAAG,sBAAsB;AACzB,KAAG,eAAe;AAClB,IAAE,OAAO,cAAc,EAAE;AAC7B;AACA,SAAS,SAAS,GAAG;AACjB,QAAM,GAAG,UAAU;AACvB;AAEA,IAAI,OAAO;AAAA,EAEP,OAAO;AAAA,IACH,OAAO;AAAA,IACP,IAAI,SAAU,GAAG;AACb,aAAO,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI;AAAA,IACjC;AAAA,EACJ;AAAA,EAEA,aAAa;AAAA,IACT,OAAO;AAAA,IACP,IAAI,SAAU,GAAG;AACb,aAAO,KAAK,IAAI;AAAA,IACpB;AAAA,EACJ;AAAA,EAEA,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,IAAI,SAAU,GAAG;AACb,aAAO,IAAI,EAAE,IAAI,IAAI,IAAI;AAAA,IAC7B;AAAA,EACJ;AACJ;AAEA,IAAI,mBAAmB,MAAO;AAC9B,IAAI,eAAe,aAAa;AAEhC,SAAS,SAAS;AAAE;AACpB,IAAI,wBAAyB,WAAY;AAErC,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,SAAQ,aAAa,yBACjB,aAAa,+BACb,aAAa,4BACb,aAAa,0BAEb,SAAU,UAAU;AAChB,WAAO,OAAO,WAAW,UAAU,SAAS,YAAY,gBAAgB;AAAA,EAC5E;AACR,EAAG;AACH,IAAI,uBAAwB,WAAY;AAEpC,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,SAAQ,aAAa,wBACjB,aAAa,8BACb,aAAa,2BACb,aAAa,yBACb,SAAU,IAAI;AACV,WAAO,aAAa,EAAE;AAAA,EAC1B;AACR,EAAG;AAGH,IAAI,OAAO,SAAU,KAAK;AAAE;AAC5B,IAAI,2BAA2B;AAAA,EAC3B,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,KAAK;AAAA,EACL,KAAK;AACT;AACA,IAAI,cAAc,SAAU,KAAK,KAAK;AAClC,MAAI,OAAO,IAAI,MAAM,GAAG;AACxB,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACtC,UAAM,IAAI,KAAK;AACf,QAAI,OAAO,QAAQ,YAAY,CAAC;AAC5B;AAAA,EACR;AACA,MAAI,UAAU,KAAK,IAAI;AACvB,MAAI,OAAO,IAAI,aAAa,YAAY;AACpC,WAAO,WAAY;AACf,aAAO,IAAI,SAAS,MAAM,KAAK,SAAS;AAAA,IAC5C;AAAA,EACJ,OACK;AACD,WAAO,IAAI;AAAA,EACf;AACJ;AACA,IAAI,cAAc,SAAU,KAAK,KAAK,OAAO;AACzC,MAAI,OAAO,IAAI,MAAM,GAAG;AACxB,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACtC,WAAO,KAAK;AACZ,QAAI,CAAC,IAAI;AACL,UAAI,QAAQ,CAAC;AACjB,UAAM,IAAI;AAAA,EACd;AACA,MAAI,KAAK,IAAI,KAAK;AACtB;AACA,SAAS,gBAAgB,QAAQ,WAAW,KAAK;AAC7C,2BAAyB,MAAM,SAAS,cAAc;AAClD,WAAO,YAAY,MAAM,SAAS;AAAA,EACtC;AACA,2BAAyB,MAAM,SAAS,YAAY,KAAK;AACrD,gBAAY,MAAM,WAAW,GAAG;AAAA,EACpC;AACA,SAAO,eAAe,QAAQ,KAAK,wBAAwB;AAC/D;AAEA,IAAI,eAA8B,WAAY;AAC1C,WAASC,cAAa,OAAO;AACzB,SAAK,SAAS,CAAC;AACf,SAAK,aAAa,CAAC;AACnB,SAAK,aAAa,KAAK;AAAA,EAC3B;AACA,EAAAA,cAAa,UAAU,KAAK,SAAU,MAAM,IAAI,SAAS;AACrD,QAAI,YAAY,QAAQ;AAAE,gBAAU;AAAA,IAAM;AAC1C,SAAK,QAAQ,IAAI;AACjB,QAAI,CAAC,KAAK,OAAO,OAAO;AACpB,WAAK,OAAO,QAAQ,CAAC;AAAA,IACzB;AACA,SAAK,OAAO,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC;AACpC,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,UAAU,OAAO,SAAU,MAAM,IAAI,SAAS;AACvD,QAAI,QAAQ;AACZ,QAAI,YAAY,QAAQ;AAAE,gBAAU;AAAA,IAAM;AAC1C,SAAK,QAAQ,IAAI;AACjB,QAAI,QAAQ,WAAY;AACpB,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,MAAM,UAAU;AAAA,MACzB;AACA,YAAM,IAAI,MAAM,KAAK;AACrB,UAAI,MAAM,GAAG,MAAM,SAAS,IAAI;AAChC,UAAI,QAAQ,MAAM;AACd,eAAO;AAAA,MACX;AAAA,IACJ;AACA,UAAM,KAAK;AACX,SAAK,GAAG,MAAM,KAAK;AACnB,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,UAAU,MAAM,SAAU,MAAM,IAAI;AAC7C,QAAI,CAAC,QAAQ,CAAC,IAAI;AACd,WAAK,SAAS,CAAC;AACf,aAAO;AAAA,IACX;AACA,QAAI,MAAM;AACN,WAAK,QAAQ,IAAI;AACjB,UAAI,CAAC,IAAI;AACL,aAAK,OAAO,QAAQ,CAAC;AACrB,eAAO;AAAA,MACX;AACA,UAAI,SAAS,KAAK,OAAO;AACzB,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,SAAS;AACZ,YAAI,OAAO,OAAO,OAAO,MACpB,OAAO,OAAO,MAAM,OAAO,OAAO,GAAG,OAAO,IAAK;AAClD,iBAAO,OAAO,OAAO,CAAC;AAAA,QAC1B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,EAAAA,cAAa,UAAU,UAAU,SAAU,MAAM;AAC7C,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,KAAK,KAAK,UAAU;AAAA,IAC7B;AACA,SAAK,QAAQ,IAAI;AACjB,QAAI,SAAS,KAAK,OAAO;AACzB,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,QAAI,MAAM,OAAO;AACjB,QAAI,aAAa,eAAe,MAAM;AACtC,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAI,UAAU,WAAW;AACzB,UAAI,KAAK,QAAQ,IAAI,UAAU,QAAQ;AACvC,UAAI,IAAI;AACJ,cAAM,GAAG,MAAM,SAAS,IAAI;AAC5B,YAAI,QAAQ,MAAM;AACd,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,cAAa,UAAU,eAAe,SAAU,OAAO;AACnD,QAAI,QAAQ;AACZ,UAAM,QAAQ,SAAU,MAAM;AAC1B,YAAM,WAAW,QAAQ;AAAA,IAC7B,CAAC;AAAA,EACL;AACA,EAAAA,cAAa,UAAU,UAAU,WAAY;AACzC,SAAK,SAAS,CAAC;AACf,SAAK,aAAa,CAAC;AAAA,EACvB;AACA,EAAAA,cAAa,UAAU,UAAU,SAAU,MAAM;AAC7C,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,MAAM,UAAU;AAC7B,QAAI,CAAC,QAAQ;AACT,WAAK,gDAAiD,OAAO,0BACxD,KAAK,OAAO,KAAK,KAAK,EAAE,IAAI,SAAU,GAAG;AAAE,eAAO,KAAK,UAAU,CAAC;AAAA,MAAG,CAAC,KACvE,GAAG;AAAA,IACX;AAAA,EACJ;AACA,SAAOA;AACX,EAAE;AACF,IAAI,gBAA+B,WAAY;AAC3C,WAASC,eAAc,SAAS,QAAQ;AACpC,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACtB;AACA,EAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,SAAK,gBAAgB;AACrB,SAAK,SAAS,CAAC;AAAA,EACnB;AACA,EAAAA,eAAc,UAAU,eAAe,WAAY;AAC/C,SAAK,gBAAgB,QAAQ;AAAA,EACjC;AACA,EAAAA,eAAc,UAAU,kBAAkB,WAAY;AAClD,SAAK,gBAAgB,WAAW;AAAA,EACpC;AACA,EAAAA,eAAc,UAAU,kBAAkB,SAAU,gBAAgB;AAChE,QAAI,QAAQ;AACZ,QAAI,UAAU,KAAK;AACnB,SAAK,OAAO,QAAQ,SAAU,OAAO;AACjC,qBAAe,SAAS,MAAM,MAAM,OAAO,CAAC,CAAC,MAAM,OAAO;AAAA,IAC9D,CAAC;AAAA,EACL;AACA,EAAAA,eAAc,UAAU,cAAc,SAAU,GAAG;AAC/C,QAAI,YAAY,EAAE;AAClB,SAAK,OAAO,KAAK,SAAU,OAAO;AAC9B,UAAI,MAAM,SAAS,WAAW;AAC1B,cAAM,QAAQ,CAAC;AACf,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,SAAOA;AACX,EAAE;AAEF,IAAI,gBAA+B,WAAY;AAC3C,WAASC,iBAAgB;AAAA,EACzB;AACA,SAAOA;AACX,EAAE;AACF,IAAI,qBAAoC,SAAU,QAAQ;AACtD,YAAUC,qBAAoB,MAAM;AACpC,WAASA,sBAAqB;AAC1B,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,SAAS;AACf,UAAM,SAAS;AACf,UAAM,UAAU;AAChB,UAAM,UAAU;AAChB,UAAM,aAAa;AACnB,UAAM,yBAAyB;AAC/B,UAAM,mBAAmB;AACzB,UAAM,QAAQ;AACd,UAAM,WAAW;AACjB,UAAM,MAAM;AACZ,UAAM,SAAS;AAAA,MACX,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACX;AACA,UAAM,aAAa;AACnB,UAAM,WAAW;AACjB,UAAM,oBAAoB;AAC1B,UAAM,wBAAwB;AAC9B,UAAM,YAAY;AAClB,UAAM,kBAAkB;AACxB,UAAM,eAAe;AACrB,UAAM,iBAAiB;AACvB,UAAM,qBAAqB;AAC3B,UAAM,gBAAgB;AACtB,UAAM,YAAY;AAClB,UAAM,kBAAkB;AACxB,UAAM,iBAAiB;AACvB,UAAM,0BAA0B;AAAA,MAC5B,SAAS;AAAA,IACb;AACA,UAAM,eAAe;AAAA,MACjB,SAAS;AAAA,IACb;AACA,UAAM,gBAAgB;AACtB,UAAM,gBAAgB;AACtB,UAAM,gBAAgB;AACtB,UAAM,eAAe;AACrB,UAAM,eAAe;AACrB,UAAM,eAAe,CAAC;AACtB,UAAM,WAAW;AACjB,UAAM,kBAAkB;AACxB,UAAM,6BAA6B,IAAI;AACvC,UAAM,0BAA0B;AAChC,UAAM,WAAW;AACjB,WAAO;AAAA,EACX;AACA,EAAAA,oBAAmB,UAAU,QAAQ,SAAU,SAAS;AACpD,QAAI,CAAC;AACD,aAAO;AACX,aAAS,OAAO,SAAS;AACrB,UAAI,QAAQ,UAAU;AAClB,aAAK,SAAS,KAAK,cAAc,QAAQ,IAAI;AAC7C;AAAA,MACJ;AACA,WAAK,OAAO,QAAQ;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,oBAAmB,UAAU,UAAU,WAAY;AAC/C,SAAK,aACD,KAAK,iBAAiB,iBAAiB,qBAAqB;AAChE,SAAK,gBAAgB,KAAK,iBAAiB;AAC3C,SAAK,iBAAiB,CAAC,KAAK,oBAAoB,KAAK;AAErD,SAAK,UACD,KAAK,qBAAqB,eACpB,QACA,KAAK;AACf,SAAK,UACD,KAAK,qBAAqB,aAA4B,QAAQ,KAAK;AAEvE,SAAK,aAAa,KAAK,cAAc,CAAC,KAAK;AAE3C,SAAK,UAAU,KAAK,aAAa,OAAO,KAAK;AAC7C,SAAK,UAAU,KAAK,aAAa,OAAO,KAAK;AAC7C,SAAK,yBAAyB,KAAK,mBAC7B,IACA,KAAK;AACX,WAAO;AAAA,EACX;AACA,EAAAA,oBAAmB,UAAU,gBAAgB,SAAU,eAAe;AAClE,QAAI,iBAAiB;AAAA,MACjB,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,IACV;AACA,QAAI,iBAAiB;AAAA,MACjB,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,IACV;AACA,QAAI;AACJ,QAAI,OAAO,kBAAkB,UAAU;AACnC,YAAM,OAAO,gBAAgB,aAAa;AAAA,IAC9C,OACK;AACD,YAAM,gBAAgB,iBAAiB;AAAA,IAC3C;AACA,WAAO;AAAA,EACX;AACA,SAAOA;AACX,EAAE,aAAa;AAEf,IAAI,iBAAgC,WAAY;AAC5C,WAASC,gBAAe,SAAS,SAAS;AACtC,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,QAAQ,IAAI,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,SAAK,gBAAgB;AAAA,EACzB;AACA,EAAAA,gBAAe,UAAU,kBAAkB,WAAY;AACnD,QAAIC,MAAK,KAAK,SAAS,gBAAgBA,IAAG,eAAe,eAAeA,IAAG,cAAc,eAAeA,IAAG,cAAcC,SAAQD,IAAG;AACpI,QAAI,UAAU,KAAK;AACnB,QAAI,SAAS,gBAAgB,UAAU;AACvC,QAAI,gBAAgB,CAAC;AACrB,QAAI,eAAe,CAAC;AACpB,QAAI,sBAAsB,CAAC;AAC3B,QAAI,sBAAsB,CAAC;AAC3B,QAAIC,QAAO;AACP,oBAAc,KAAK;AAAA,QACf,MAAM;AAAA,QACN,SAAS,KAAK,MAAM,KAAK,IAAI;AAAA,QAC7B,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,QAAI,qBAAqB;AACrB,oBAAc,KAAK;AAAA,QACf,MAAM;AAAA,QACN,SAAS,KAAK,MAAM,KAAK,IAAI;AAAA,MACjC,CAAC;AACD,mBAAa,KAAK;AAAA,QACd,MAAM;AAAA,QACN,SAAS,KAAK,KAAK,KAAK,IAAI;AAAA,MAChC,GAAG;AAAA,QACC,MAAM;AAAA,QACN,SAAS,KAAK,IAAI,KAAK,IAAI;AAAA,MAC/B,GAAG;AAAA,QACC,MAAM;AAAA,QACN,SAAS,KAAK,IAAI,KAAK,IAAI;AAAA,MAC/B,CAAC;AAAA,IACL;AACA,QAAI,qBAAqB;AACrB,oBAAc,KAAK;AAAA,QACf,MAAM;AAAA,QACN,SAAS,KAAK,MAAM,KAAK,IAAI;AAAA,MACjC,CAAC;AACD,mBAAa,KAAK;AAAA,QACd,MAAM;AAAA,QACN,SAAS,KAAK,KAAK,KAAK,IAAI;AAAA,MAChC,GAAG;AAAA,QACC,MAAM;AAAA,QACN,SAAS,KAAK,IAAI,KAAK,IAAI;AAAA,MAC/B,CAAC;AAAA,IACL;AACA,SAAK,uBAAuB,IAAI,cAAc,SAAS,aAAa;AACpE,SAAK,sBAAsB,IAAI,cAAc,QAAQ,YAAY;AAAA,EACrE;AACA,EAAAF,gBAAe,UAAU,gBAAgB,SAAU,GAAG,MAAM;AACxD,QAAIC,MAAK,KAAK,SAAS,iBAAiBA,IAAG,gBAAgB,kBAAkBA,IAAG,iBAAiB,0BAA0BA,IAAG;AAC9H,QAAI,2BAA2B;AAAA,MAC3B,OAAO,WAAY;AACf,eAAQ,kBACJ,CAAC,0BAA0B,EAAE,QAAQ,uBAAuB;AAAA,MACpE;AAAA,MACA,KAAK,WAAY;AACb,eAAQ,kBACJ,CAAC,0BAA0B,EAAE,QAAQ,uBAAuB;AAAA,MACpE;AAAA,MACA,MAAM,WAAY;AACd,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,yBAAyB,MAAM,GAAG;AAClC,QAAE,eAAe;AAAA,IACrB;AACA,QAAI,iBAAiB;AACjB,QAAE,gBAAgB;AAAA,IACtB;AAAA,EACJ;AACA,EAAAD,gBAAe,UAAU,eAAe,SAAU,MAAM;AACpD,QAAI,SAAS,QAAQ;AAAE,aAAO;AAAA,IAAG;AACjC,SAAK,YAAY;AAAA,EACrB;AACA,EAAAA,gBAAe,UAAU,QAAQ,SAAU,GAAG;AAC1C,QAAI,aAAa,aAAa,EAAE;AAChC,QAAI,KAAK,aAAa,KAAK,cAAc,YAAY;AACjD;AAAA,IACJ;AACA,SAAK,aAAa,UAAU;AAG5B,QAAI,eAAe,EAAE,QAAQ,KAAK,QAAQ,YAAY,GAAG;AACrD,WAAK,aAAa;AAClB;AAAA,IACJ;AAEA,QAAI,eAAe,KAAiB,EAAE,WAAW;AAC7C;AACJ,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,aAAa,CAAC,GAAG;AAC1D;AAAA,IACJ;AACA,SAAK,cAAc,GAAG,OAAO;AAC7B,QAAI,QAAS,EAAE,UAAU,EAAE,QAAQ,KAAK;AACxC,SAAK,SAAS,MAAM;AACpB,SAAK,SAAS,MAAM;AACpB,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO,CAAC;AAAA,EACrD;AACA,EAAAA,gBAAe,UAAU,OAAO,SAAU,GAAG;AACzC,QAAI,aAAa,EAAE,UAAU,KAAK,WAAW;AACzC;AAAA,IACJ;AACA,SAAK,cAAc,GAAG,MAAM;AAC5B,QAAI,QAAS,EAAE,UAAU,EAAE,QAAQ,KAAK;AACxC,QAAI,SAAS,MAAM,QAAQ,KAAK;AAChC,QAAI,SAAS,MAAM,QAAQ,KAAK;AAChC,SAAK,SAAS,MAAM;AACpB,SAAK,SAAS,MAAM;AACpB,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM;AAAA,MAC/C;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC,GAAG;AACA;AAAA,IACJ;AAEA,QAAI,aAAa,SAAS,gBAAgB,cACtC,OAAO,eACP,SAAS,KAAK;AAClB,QAAI,YAAY,SAAS,gBAAgB,aACrC,OAAO,eACP,SAAS,KAAK;AAClB,QAAI,KAAK,KAAK,SAAS;AACvB,QAAI,KAAK,KAAK,SAAS;AACvB,QAAI,kBAAkB,KAAK,QAAQ;AACnC,QAAI,KAAK,SAAS,gBAAgB,cAAc,mBAC5C,KAAK,SAAS,gBAAgB,eAAe,mBAC7C,KAAK,mBACL,KAAK,iBAAiB;AACtB,WAAK,IAAI,CAAC;AAAA,IACd;AAAA,EACJ;AACA,EAAAA,gBAAe,UAAU,MAAM,SAAU,GAAG;AACxC,QAAI,aAAa,EAAE,UAAU,KAAK,WAAW;AACzC;AAAA,IACJ;AACA,SAAK,aAAa;AAClB,SAAK,cAAc,GAAG,KAAK;AAC3B,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,CAAC;AAAA,EACnD;AACA,EAAAA,gBAAe,UAAU,QAAQ,SAAU,GAAG;AAC1C,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO,CAAC;AAAA,EACrD;AACA,EAAAA,gBAAe,UAAU,aAAa,SAAU,SAAS;AACrD,QAAI,YAAY,KAAK,SAAS;AAC1B,WAAK,UAAU;AACf,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACJ;AACA,EAAAA,gBAAe,UAAU,kBAAkB,WAAY;AACnD,SAAK,qBAAqB,QAAQ;AAClC,SAAK,oBAAoB,QAAQ;AACjC,SAAK,gBAAgB;AAAA,EACzB;AACA,EAAAA,gBAAe,UAAU,UAAU,WAAY;AAC3C,SAAK,qBAAqB,QAAQ;AAClC,SAAK,oBAAoB,QAAQ;AACjC,SAAK,MAAM,QAAQ;AAAA,EACvB;AACA,SAAOA;AACX,EAAE;AAEF,IAAI,qBAAqB;AAAA,EACrB,GAAG,CAAC,cAAc,IAAI;AAAA,EACtB,GAAG,CAAC,cAAc,IAAI;AAC1B;AACA,IAAI,aAA4B,WAAY;AACxC,WAASG,YAAW,SAAS;AACzB,SAAK,WAAW,OAAO;AACvB,SAAK,QAAQ,IAAI,aAAa,CAAC,mBAAmB,WAAW,CAAC;AAAA,EAClE;AACA,EAAAA,YAAW,UAAU,sBAAsB,WAAY;AACnD,QAAI,WAAW,OAAO,iBAAiB,KAAK,SAAS,IAAI;AACzD,QAAI,SAAS,SAAS,MAAM,WAAW,MAAM,GAAG,EAAE,GAAG,MAAM,IAAI;AAC/D,QAAI,IAAI,EAAE,OAAO,OAAO,OAAO,OAAO;AACtC,QAAI,IAAI,EAAE,OAAO,OAAO,OAAO,OAAO;AACtC,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,YAAW,UAAU,YAAY,SAAU,OAAO;AAC9C,QAAI,iBAAiB,CAAC;AACtB,WAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,KAAK;AACtC,UAAI,CAAC,mBAAmB,MAAM;AAC1B;AAAA,MACJ;AACA,UAAI,kBAAkB,mBAAmB,KAAK;AAC9C,UAAI,iBAAiB;AACjB,YAAI,qBAAqB,mBAAmB,KAAK;AACjD,YAAI,iBAAiB,MAAM;AAC3B,uBAAe,KAAK,kBAAkB,MAAM,iBAAiB,qBAAqB,GAAG;AAAA,MACzF;AAAA,IACJ,CAAC;AACD,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,iBAAiB,gBAAgB,KAAK;AAC/E,SAAK,MAAM,MAAM,aAAa,eAAe,KAAK,GAAG;AACrD,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,KAAK;AAAA,EAC7D;AACA,EAAAA,YAAW,UAAU,aAAa,SAAU,SAAS;AACjD,QAAI,KAAK,YAAY,SAAS;AAC1B,WAAK,UAAU;AACf,WAAK,QAAQ,QAAQ;AAAA,IACzB;AAAA,EACJ;AACA,EAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,SAAK,MAAM,QAAQ;AAAA,EACvB;AACA,SAAOA;AACX,EAAE;AAEF,IAAI,OAAsB,WAAY;AAClC,WAASC,MAAK,SAAS,YAAY,SAAS;AACxC,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ,IAAI,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,SAAK,WAAW,OAAO;AAAA,EAC3B;AACA,EAAAA,MAAK,UAAU,YAAY,SAAU,UAAU;AAC3C,SAAK,WAAW,UAAU,QAAQ;AAAA,EACtC;AACA,EAAAA,MAAK,UAAU,aAAa,SAAU,SAAS;AAC3C,SAAK,UAAU;AAAA,EACnB;AACA,EAAAA,MAAK,UAAU,kBAAkB,SAAU,cAAc;AACrD,SAAK,eAAe;AAAA,EACxB;AACA,EAAAA,MAAK,UAAU,cAAc,SAAU,QAAQ;AAC3C,SAAK,sBAAsB;AAAA,EAC/B;AACA,EAAAA,MAAK,UAAU,aAAa,SAAU,SAAS;AAC3C,QAAI,KAAK,YAAY,SAAS;AAC1B,WAAK,UAAU;AACf,WAAK,QAAQ,QAAQ;AACrB,WAAK,KAAK;AAAA,IACd;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,aAAa,WAAY;AACpC,QAAI,KAAK,OAAO;AACZ,2BAAqB,KAAK,KAAK;AAC/B,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,UAAU,WAAY;AACjC,SAAK,MAAM,QAAQ;AACnB,yBAAqB,KAAK,KAAK;AAAA,EACnC;AACA,SAAOA;AACX,EAAE;AAQF,IAAI,iBAAiB,SAAU,YAAY,UAAU,YAAY,QAAQ;AACrE,MAAI,mBAAmB,SAAU,UAAU,YAAY;AACnD,QAAI,QAAQ,WAAW;AACvB,QAAI,YAAY,QAAQ,IAClB,KACA,QAAQ,IACJ,IACA;AACV,WAAO;AAAA,EACX;AACA,MAAI,aAAa,iBAAiB,SAAS,GAAG,WAAW,CAAC;AAC1D,MAAI,aAAa,iBAAiB,SAAS,GAAG,WAAW,CAAC;AAC1D,MAAI,SAAS,WAAW,IAAI,OAAO;AACnC,MAAI,SAAS,WAAW,IAAI,OAAO;AACnC,SAAO,aAAa,UAAU,KAAK,aAAa,UAAU;AAC9D;AAEA,IAAI,aAA4B,SAAU,QAAQ;AAC9C,YAAUC,aAAY,MAAM;AAC5B,WAASA,cAAa;AAClB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC/D;AACA,EAAAA,YAAW,UAAU,aAAa,SAAU,YAAY,UAAU;AAC9D,QAAI,QAAQ;AACZ,QAAI,SAAS;AACb,QAAI,QAAQ,WAAY;AACpB,UAAI,MAAM,MAAM,WAAW,oBAAoB;AAC/C,UAAI,eAAe,YAAY,UAAU,KAAK,MAAM,GAAG;AACnD,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,MAAM,GAAG;AAAA,MACxD;AAIA,UAAI,CAAC,MAAM,SAAS;AAChB,YAAI,MAAM,qBAAqB;AAC3B,gBAAM,sBAAsB;AAAA,QAChC,OACK;AAED,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK,GAAG;AAAA,QACvD;AAAA,MACJ;AACA,eAAS;AACT,UAAI,MAAM,SAAS;AACf,cAAM,QAAQ,sBAAsB,KAAK;AAAA,MAC7C;AAAA,IACJ;AAGA,QAAI,KAAK,qBAAqB;AAC1B,WAAK,YAAY,KAAK;AAAA,IAC1B;AACA,yBAAqB,KAAK,KAAK;AAC/B,UAAM;AAAA,EACV;AACA,EAAAA,YAAW,UAAU,iBAAiB,SAAU,MAAM;AAClD,QAAI,SAAS,QAAQ;AAAE,aAAO;AAAA,IAAG;AACjC,SAAK,MAAM,MAAM,sBAAsB,OAAO;AAC9C,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM,IAAI;AAAA,EACvD;AACA,EAAAA,YAAW,UAAU,2BAA2B,SAAU,QAAQ;AAC9D,SAAK,MAAM,MAAM,4BAA4B;AAC7C,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,cAAc,MAAM;AAAA,EACjE;AACA,EAAAA,YAAW,UAAU,qBAAqB,WAAY;AAClD,SAAK,MAAM,MAAM,sBAAsB,MAAM;AAAA,EACjD;AACA,EAAAA,YAAW,UAAU,OAAO,SAAU,YAAY,UAAU,MAAM,UAAU;AACxE,SAAK,WAAW,OAAO,CAAC;AACxB,SAAK,yBAAyB,QAAQ;AACtC,SAAK,mBAAmB;AACxB,SAAK,eAAe,IAAI;AACxB,SAAK,UAAU,QAAQ;AACvB,QAAI,sBAAsB,KAAK,QAAQ,cAAc;AACrD,QAAI,QAAQ,qBAAqB;AAC7B,WAAK,WAAW,YAAY,QAAQ;AAAA,IACxC;AAKA,QAAI,CAAC,MAAM;AACP,WAAK,UAAU,KAAK,QAAQ;AAC5B,UAAI,qBAAqB;AACrB,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM,QAAQ;AAAA,MAC3D;AACA,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,QAAQ;AAAA,IAC1D;AAAA,EACJ;AACA,EAAAA,YAAW,UAAU,SAAS,WAAY;AACtC,QAAI,UAAU,KAAK;AACnB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,YAAY,KAAK;AAEtB,QAAI,SAAS;AACT,WAAK,WAAW,KAAK;AACrB,2BAAqB,KAAK,KAAK;AAC/B,UAAIJ,MAAK,KAAK,WAAW,oBAAoB,GAAG,IAAIA,IAAG,GAAG,IAAIA,IAAG;AACjE,WAAK,eAAe;AACpB,WAAK,UAAU,EAAE,GAAM,EAAK,CAAC;AAC7B,WAAK,gBAAgB,IAAI;AACzB,WAAK,YAAY,IAAI;AACrB,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,EAAE,GAAM,EAAK,CAAC;AAAA,IACtE;AACA,WAAO;AAAA,EACX;AACA,EAAAI,YAAW,UAAU,OAAO,WAAY;AACpC,QAAI,qBAAqB,KAAK,OAAO;AACrC,QAAI,oBAAoB;AACpB,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,QAAQ;AAAA,IACrD;AAAA,EACJ;AACA,SAAOA;AACX,EAAE,IAAI;AAEN,IAAI,YAA2B,SAAU,QAAQ;AAC7C,YAAUC,YAAW,MAAM;AAC3B,WAASA,aAAY;AACjB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC/D;AACA,EAAAA,WAAU,UAAU,OAAO,SAAU,YAAY,UAAU,MAAM,UAAU;AAEvE,QAAI,CAAC,MAAM;AACP,WAAK,UAAU,QAAQ;AACvB,UAAI,KAAK,QAAQ,cAAc,GAAkB;AAC7C,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM,QAAQ;AAAA,MAC3D;AACA,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,QAAQ;AACtD;AAAA,IACJ;AACA,SAAK,QAAQ,YAAY,UAAU,MAAM,QAAQ;AAAA,EACrD;AACA,EAAAA,WAAU,UAAU,UAAU,SAAU,YAAY,UAAU,UAAU,UAAU;AAC9E,QAAI,QAAQ;AACZ,QAAI,YAAY,OAAO;AACvB,QAAI,WAAW,YAAY;AAC3B,QAAI,sBAAsB,KAAK,QAAQ,cAAc;AACrD,QAAI,OAAO,WAAY;AACnB,UAAI,MAAM,OAAO;AAEjB,UAAI,OAAO,UAAU;AACjB,cAAM,UAAU,QAAQ;AACxB,YAAI,qBAAqB;AACrB,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,MAAM,QAAQ;AAAA,QAC7D;AACA,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK,QAAQ;AACxD;AAAA,MACJ;AACA,aAAO,MAAM,aAAa;AAC1B,UAAI,SAAS,SAAS,GAAG;AACzB,UAAI,WAAW,CAAC;AAChB,aAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACzC,YAAI,aAAa,WAAW;AAC5B,YAAI,WAAW,SAAS;AACxB,iBAAS,QAAQ,WAAW,cAAc,SAAS;AAAA,MACvD,CAAC;AACD,YAAM,UAAU,QAAQ;AACxB,UAAI,qBAAqB;AACrB,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,MAAM,QAAQ;AAAA,MAC7D;AACA,UAAI,MAAM,SAAS;AACf,cAAM,QAAQ,sBAAsB,IAAI;AAAA,MAC5C;AAIA,UAAI,CAAC,MAAM,SAAS;AAChB,YAAI,MAAM,qBAAqB;AAC3B,gBAAM,sBAAsB;AAAA,QAChC,OACK;AAED,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK,QAAQ;AAAA,QAC5D;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,WAAW,IAAI;AAGpB,QAAI,KAAK,qBAAqB;AAC1B,WAAK,YAAY,KAAK;AAAA,IAC1B;AACA,yBAAqB,KAAK,KAAK;AAC/B,SAAK;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,QAAI,UAAU,KAAK;AACnB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,YAAY,KAAK;AAEtB,QAAI,SAAS;AACT,WAAK,WAAW,KAAK;AACrB,2BAAqB,KAAK,KAAK;AAC/B,UAAI,MAAM,KAAK,WAAW,oBAAoB;AAC9C,WAAK,gBAAgB,IAAI;AACzB,WAAK,YAAY,IAAI;AACrB,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,GAAG;AAAA,IAC3D;AACA,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,OAAO,WAAY;AACnC,QAAI,oBAAoB,KAAK,OAAO;AACpC,QAAI,mBAAmB;AACnB,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,QAAQ;AAAA,IACrD;AAAA,EACJ;AACA,SAAOA;AACX,EAAE,IAAI;AAEN,SAAS,eAAe,SAAS,YAAY,SAAS;AAClD,MAAI,gBAAgB,QAAQ;AAC5B,MAAI,kBAAkB,CAAC;AACvB,SAAO,eAAe,iBAAiB,aAAa;AAAA,IAChD,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,KAAK,WAAY;AACb,aAAO,QAAQ;AAAA,IACnB;AAAA,EACJ,CAAC;AACD,MAAI,eAAe;AACf,WAAO,IAAI,WAAW,SAAS,YAAY,eAAe;AAAA,EAC9D,OACK;AACD,WAAO,IAAI,UAAU,SAAS,YAAY,eAAe;AAAA,EAC7D;AACJ;AAEA,IAAI,WAA0B,WAAY;AACtC,WAASC,UAAS,SAAS,SAAS,SAAS;AACzC,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,QAAQ,IAAI,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,SAAK,QAAQ,OAAO;AAAA,EACxB;AACA,EAAAA,UAAS,UAAU,QAAQ,WAAY;AACnC,SAAK,OAAO;AACZ,SAAK,mBAAmB,CAAe;AACvC,SAAK,aAAa,CAAe;AAAA,EACrC;AACA,EAAAA,UAAS,UAAU,OAAO,SAAU,OAAO;AACvC,YAAQ,KAAK,YAAY,QAAQ;AACjC,SAAK,mBAAmB,KAAK;AAC7B,WAAO,KAAK,wBAAwB,OAAO,KAAK,QAAQ,0BAA0B;AAAA,EACtF;AACA,EAAAA,UAAS,UAAU,qBAAqB,SAAU,OAAO;AACrD,SAAK,kBACD,QAAQ,IACF,KACA,QAAQ,IACJ,IACA;AAAA,EAClB;AACA,EAAAA,UAAS,UAAU,eAAe,SAAU,OAAO;AAC/C,SAAK,YACD,QAAQ,IACF,KACA,QAAQ,IACJ,IACA;AAAA,EAClB;AACA,EAAAA,UAAS,UAAU,0BAA0B,SAAU,OAAO,eAAe;AACzE,QAAI,SAAS,KAAK,aAAa;AAE/B,QAAI,SAAS,KAAK,gBAAgB,SAAS,KAAK,cAAc;AAC1D,UAAK,SAAS,KAAK,gBAAgB,KAAK,QAAQ,QAAQ,MACnD,SAAS,KAAK,gBAAgB,KAAK,QAAQ,QAAQ,IAAK;AACzD,iBAAS,KAAK,aAAa,QAAQ;AAAA,MACvC,OACK;AACD,iBACI,SAAS,KAAK,eAAe,KAAK,eAAe,KAAK;AAAA,MAC9D;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,MAAM,SAAU,UAAU;AACzC,QAAI,eAAe;AAAA,MACf,UAAU;AAAA,IACd;AACA,QAAI,UAAU,KAAK,IAAI,KAAK,aAAa,KAAK,QAAQ;AAEtD,QAAI,KAAK,QAAQ,YACb,WAAW,KAAK,QAAQ,qBACxB,UAAU,KAAK,QAAQ,uBAAuB;AAC9C,UAAI,cAAe,KAAK,cAAc,MAAqB,KAAK,QAAQ,QAAQ,MAC3E,KAAK,cAAc,KAAoB,KAAK,QAAQ,QAAQ,KAC3D,KAAK,cACL;AACN,qBAAe,KAAK,YACd,KAAK,SAAS,KAAK,YAAY,KAAK,UAAU,UAAU,KAAK,cAAc,KAAK,cAAc,aAAa,KAAK,OAAO,IACvH,EAAE,aAAa,KAAK,YAAY,UAAU,EAAE;AAAA,IACtD,OACK;AACD,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,YAAY;AAAA,IAC9D;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,WAAW,SAAU,SAAS,OAAO,MAAM,aAAa,aAAa,aAAa,SAAS;AAC1G,QAAI,YAAY,QAAQ;AAAE,gBAAU,KAAK;AAAA,IAAS;AAClD,QAAI,WAAW,UAAU;AACzB,QAAI,QAAQ,KAAK,IAAI,QAAQ,IAAI;AACjC,QAAI,eAAe,QAAQ,cAAc,kBAAkB,QAAQ,iBAAiB,YAAY,QAAQ;AACxG,QAAI,WAAW,KAAK,IAAI,WAAY,QAAQ,IAAK,YAAY;AAC7D,QAAI,eAAe;AAAA,MACf,aAAa,UAAY,QAAQ,QAAS,gBAAiB,WAAW,IAAI,KAAK;AAAA,MAC/E;AAAA,MACA,MAAM;AAAA,IACV;AACA,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,UAAU,cAAc,QAAQ;AACzE,QAAI,aAAa,cAAc,aAAa;AACxC,mBAAa,cAAc,cACrB,KAAK,IAAI,cAAc,cAAc,GAAG,cAAe,cAAc,aAAa,OAAQ,KAAK,IAC/F;AACN,mBAAa,WAAW;AAAA,IAC5B,WACS,aAAa,cAAc,aAAa;AAC7C,mBAAa,cAAc,cACrB,KAAK,IAAI,cAAc,cAAc,GAAG,cAAe,cAAc,aAAa,OAAQ,KAAK,IAC/F;AACN,mBAAa,WAAW;AAAA,IAC5B;AACA,iBAAa,cAAc,KAAK,MAAM,aAAa,WAAW;AAC9D,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,kBAAkB,WAAY;AAC7C,QAAI,UAAU,KAAK,aAAa,KAAK;AACrC,SAAK,aAAa,OAAO;AAAA,EAC7B;AACA,EAAAA,UAAS,UAAU,UAAU,SAAU,SAAS;AAC5C,QAAIN,MAAK,KAAK,QAAQ,MAAM,OAAOA,IAAG,MAAM,WAAWA,IAAG;AAC1D,QAAI,kBAAkB,OAAO,iBAAiB,KAAK,SAAS,IAAI,EAAE,aAAa;AAE/E,QAAI,cAAc,QAAQ,KAAK,OAAO;AAEtC,SAAK,cAAc,KAAK,QAAQ,SAAS,UAAU,gBAAgB;AACnE,SAAK,WAAW,OAAO;AACvB,QAAI,cAAc,QAAQ,KAAK,OAAO;AACtC,SAAK,cAAc,YAAY;AAC/B,SAAK,iBAAiB,YAAY;AAElC,QAAI,iBAAiB;AACjB,WAAK,kBAAkB,YAAY;AAAA,IACvC;AACA,SAAK,gBAAgB;AACrB,SAAK,aAAa,CAAe;AAAA,EACrC;AACA,EAAAM,UAAS,UAAU,aAAa,SAAU,SAAS;AAC/C,QAAI,YAAY,KAAK,SAAS;AAC1B,WAAK,UAAU;AACf,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,aAAa,WAAY;AACxC,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,aAAa,CAAe;AACjC,SAAK,mBAAmB,CAAe;AACvC,SAAK,cAAc;AAAA,EACvB;AACA,EAAAA,UAAS,UAAU,kBAAkB,WAAY;AAC7C,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,qBAAqB;AAC9D,QAAI,WAAW;AAAA,MACX,cAAc;AAAA,MACd,cAAc,KAAK,cAAc,KAAK;AAAA,IAC1C;AACA,QAAI,SAAS,eAAe,GAAG;AAC3B,eAAS,gBAAgB,KAAK;AAC9B,UAAI,KAAK,QAAQ,4BAA4B,GAAG;AAC5C,iBAAS,eAAe,CAAC,KAAK;AAAA,MAClC;AAAA,IACJ;AACA,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,iBAAiB,QAAQ;AAClE,SAAK,eAAe,SAAS;AAC7B,SAAK,eAAe,SAAS;AAC7B,SAAK,YACD,KAAK,QAAQ,cAAc,KAAK,eAAe,KAAK;AACxD,QAAI,CAAC,KAAK,aAAa,KAAK,eAAe,KAAK,cAAc;AAC1D,WAAK,eAAe,KAAK;AACzB,WAAK,cAAc,KAAK;AAAA,IAC5B;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,iBAAiB,SAAU,KAAK;AAC/C,SAAK,aAAa;AAAA,EACtB;AACA,EAAAA,UAAS,UAAU,gBAAgB,WAAY;AAC3C,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,UAAS,UAAU,kBAAkB,WAAY;AAC7C,QAAI,WAAW,KAAK,eAAe,KAAK,UAAU;AAClD,QAAI,aAAa,aAAa,KAAK,cAAc;AACjD,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAEA,EAAAA,UAAS,UAAU,iBAAiB,SAAU,KAAK;AAC/C,QAAI,CAAC,KAAK,aACN,CAAC,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,eAAe,GAAG;AAC5D,YAAM,KAAK;AAAA,IACf,WACS,MAAM,KAAK,cAAc;AAC9B,YAAM,KAAK;AAAA,IACf,WACS,MAAM,KAAK,cAAc;AAC9B,YAAM,KAAK;AAAA,IACf;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,iBAAiB,WAAY;AAC5C,SAAK,WAAW,KAAK;AAAA,EACzB;AACA,EAAAA,UAAS,UAAU,oBAAoB,WAAY;AAC/C,SAAK,cAAc,KAAK;AAAA,EAC5B;AACA,EAAAA,UAAS,UAAU,gBAAgB,WAAY;AAC3C,SAAK,eAAe;AACpB,SAAK,kBAAkB;AAAA,EAC3B;AACA,EAAAA,UAAS,UAAU,aAAa,SAAU,OAAO;AAC7C,SAAK,QAAQ;AACb,WAAO,KAAK,IAAI,KAAK,IAAI;AAAA,EAC7B;AACA,EAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,SAAK,MAAM,QAAQ;AAAA,EACvB;AACA,SAAOA;AACX,EAAE;AAEF,IAAI;AAAJ,IAAQ;AAAR,IAAY;AAAZ,IAAgB;AAChB,IAAI,uBAAuB,KAAK,CAAC,GAC7B,GAAG,SAAmB,SAAU,GAAG;AAC/B,SAAO;AACX,GACA,GAAG,QAAiB,SAAU,GAAG;AAC7B,eAAa,CAAC;AACd,SAAO;AACX,GACA;AACJ,IAAI,gBAAgB,KAAK,CAAC,GACtB,GAAG,iBAAkC,KAAK,CAAC,GACvC,GAAG,SAAmB,cACtB,GAAG,QAAiB,YACpB,KACJ,GAAG,eAA8B,KAAK,CAAC,GACnC,GAAG,SAAmB,YACtB,GAAG,QAAiB,cACpB,KACJ;AACJ,IAAI,sBAAqC,WAAY;AACjD,WAASC,qBAAoB,wBAAwB,YAAY,kBAAkB;AAC/E,SAAK,yBAAyB;AAC9B,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,MAAM;AAAA,EACf;AACA,EAAAA,qBAAoB,UAAU,QAAQ,WAAY;AAC9C,SAAK,kBAAkB;AAAA,EAC3B;AACA,EAAAA,qBAAoB,UAAU,uBAAuB,SAAU,UAAU,UAAU,GAAG;AAClF,SAAK,qBAAqB,UAAU,QAAQ;AAC5C,WAAO,KAAK,uBAAuB,CAAC;AAAA,EACxC;AACA,EAAAA,qBAAoB,UAAU,cAAc,SAAU,QAAQ,QAAQ;AAClE,QAAI,KAAK,oBAAoB,cAA+B;AACxD,eAAS;AAAA,IACb,WACS,KAAK,oBAAoB,YAA2B;AACzD,eAAS;AAAA,IACb;AACA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,qBAAoB,UAAU,uBAAuB,SAAU,UAAU,UAAU;AAE/E,QAAI,KAAK,oBAAoB,MAAoB,CAAC,KAAK,YAAY;AAC/D,UAAI,WAAW,WAAW,KAAK,wBAAwB;AACnD,aAAK,kBAAkB;AAAA,MAC3B,WACS,YAAY,WAAW,KAAK,wBAAwB;AACzD,aAAK,kBAAkB;AAAA,MAC3B,OACK;AACD,aAAK,kBAAkB;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,qBAAoB,UAAU,yBAAyB,SAAU,GAAG;AAChE,QAAI,YAAY,aAAa,KAAK;AAClC,QAAI,WAAW;AACX,UAAI,KAAK,qBAAqB,UAAU,QAAkB;AACtD,eAAO,oBAAoB,OAAiB,CAAC;AAAA,MACjD,WACS,KAAK,qBAAqB,UAAU,OAAgB;AACzD,eAAO,oBAAoB,MAAe,CAAC;AAAA,MAC/C;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,SAAOA;AACX,EAAE;AAEF,IAAI,8BAA8B,SAAU,QAAQ,QAAQ,UAAU;AAClE,MAAI,aAAa,GAAgB;AAC7B,WAAO,CAAC,QAAQ,CAAC,MAAM;AAAA,EAC3B,WACS,aAAa,GAAe;AACjC,WAAO,CAAC,CAAC,QAAQ,CAAC,MAAM;AAAA,EAC5B,WACS,aAAa,GAAe;AACjC,WAAO,CAAC,CAAC,QAAQ,MAAM;AAAA,EAC3B,OACK;AACD,WAAO,CAAC,QAAQ,MAAM;AAAA,EAC1B;AACJ;AACA,IAAI,kBAAiC,WAAY;AAC7C,WAASC,iBAAgB,iBAAiB,iBAAiB,gBAAgB,UAAU,SAAS;AAC1F,SAAK,QAAQ,IAAI,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,sBAAsB,IAAI,oBAAoB,QAAQ,wBAAwB,QAAQ,YAAY,QAAQ,gBAAgB;AAC/H,SAAK,UAAU;AACf,SAAK,mBAAmB;AAAA,EAC5B;AACA,EAAAA,iBAAgB,UAAU,qBAAqB,WAAY;AACvD,QAAI,QAAQ;AAEZ,SAAK,eAAe,MAAM,GAAG,KAAK,eAAe,MAAM,WAAW,OAAO,SAAU,GAAG;AAClF,UAAI,CAAC,MAAM;AACP,eAAO;AACX,aAAO,MAAM,YAAY,CAAC;AAAA,IAC9B,CAAC;AAED,SAAK,eAAe,MAAM,GAAG,KAAK,eAAe,MAAM,WAAW,MAAM,SAAUR,KAAI;AAClF,UAAI,SAASA,IAAG,QAAQ,SAASA,IAAG,QAAQ,IAAIA,IAAG;AACnD,UAAI,CAAC,MAAM;AACP,eAAO;AACX,UAAIS,MAAK,4BAA4B,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,GAAG,qBAAqBA,IAAG,IAAI,qBAAqBA,IAAG;AAClI,UAAI,wBAAwB;AAAA,QACxB,QAAQ;AAAA,QACR,QAAQ;AAAA,MACZ;AACA,YAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,0BAA0B,qBAAqB;AAC1F,aAAO,MAAM,WAAW,sBAAsB,QAAQ,sBAAsB,QAAQ,CAAC;AAAA,IACzF,CAAC;AAED,SAAK,eAAe,MAAM,GAAG,KAAK,eAAe,MAAM,WAAW,KAAK,SAAU,GAAG;AAChF,UAAI,CAAC,MAAM;AACP,eAAO;AACX,aAAO,MAAM,UAAU,CAAC;AAAA,IAC5B,CAAC;AAED,SAAK,eAAe,MAAM,GAAG,KAAK,eAAe,MAAM,WAAW,OAAO,SAAU,GAAG;AAElF,UAAI,MAAM,WAAW,CAAC,EAAE,cAAc;AAClC,cAAM,YAAY,CAAC;AAAA,MACvB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,EAAAD,iBAAgB,UAAU,cAAc,SAAU,GAAG;AACjD,QAAI,YAAY,OAAO;AACvB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,oBAAoB,MAAM;AAC/B,SAAK,gBAAgB,MAAM;AAC3B,SAAK,gBAAgB,MAAM;AAE3B,SAAK,SAAS,OAAO;AACrB,SAAK,gBAAgB,cAAc;AACnC,SAAK,gBAAgB,cAAc;AACnC,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO,CAAC;AAAA,EACrD;AACA,EAAAA,iBAAgB,UAAU,aAAa,SAAU,QAAQ,QAAQ,GAAG;AAChE,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,YAAY,CAAC,GAAG;AACzD;AAAA,IACJ;AACA,QAAI,WAAW,KAAK,gBAAgB,WAAW,MAAM;AACrD,QAAI,WAAW,KAAK,gBAAgB,WAAW,MAAM;AACrD,QAAI,YAAY,OAAO;AAGvB,QAAI,KAAK,cAAc,UAAU,UAAU,SAAS,GAAG;AACnD,aAAO;AAAA,IACX;AACA,QAAI,KAAK,oBAAoB,qBAAqB,UAAU,UAAU,CAAC,GAAG;AACtE,WAAK,eAAe,aAAa;AACjC,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,KAAK,oBAAoB,YAAY,QAAQ,MAAM;AAC/D,QAAI,QAAQ,KAAK,gBAAgB,cAAc;AAC/C,QAAI,OAAO,KAAK,gBAAgB,KAAK,MAAM,MAAM;AACjD,QAAI,QAAQ,KAAK,gBAAgB,cAAc;AAC/C,QAAI,OAAO,KAAK,gBAAgB,KAAK,MAAM,MAAM;AACjD,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,qBAAqB,GAAG;AACjE;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,aAAa;AACnB,WAAK,cAAc;AAAA,IACvB;AACA,QAAI,kBAAkB,SAAS,SAAS,SAAS;AACjD,QAAI,CAAC,KAAK,gBAAgB,CAAC,iBAAiB;AACxC,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,eAAe;AAAA,IAC5D;AACA,QAAI,CAAC,KAAK,gBAAgB,iBAAiB;AACvC,WAAK,eAAe;AACpB,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW;AAAA,IACxD;AACA,QAAI,KAAK,gBAAgB,iBAAiB;AACtC,WAAK,SAAS,UAAU;AAAA,QACpB,GAAG;AAAA,QACH,GAAG;AAAA,MACP,CAAC;AACD,WAAK,eAAe,SAAS;AAAA,IACjC;AAAA,EACJ;AACA,EAAAA,iBAAgB,UAAU,iBAAiB,SAAU,WAAW;AAE5D,QAAI,YAAY,KAAK,YAAY,KAAK,QAAQ,mBAAmB;AAE7D,WAAK,YAAY;AACjB,WAAK,gBAAgB,eAAe;AACpC,WAAK,gBAAgB,eAAe;AACpC,UAAI,KAAK,QAAQ,cAAc,GAAkB;AAC7C,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,QAAQ,KAAK,cAAc,CAAC;AAAA,MACzE;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ,YAAY,GAAkB;AAC3C,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,QAAQ,KAAK,cAAc,CAAC;AAAA,IACzE;AAAA,EACJ;AACA,EAAAA,iBAAgB,UAAU,gBAAgB,SAAU,UAAU,UAAU,WAAW;AAC/E,WAAQ,YAAY,KAAK,UAAU,KAAK,QAAQ,qBAC5C,WAAW,KAAK,QAAQ,yBACxB,WAAW,KAAK,QAAQ;AAAA,EAChC;AACA,EAAAA,iBAAgB,UAAU,YAAY,SAAU,GAAG;AAC/C,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,CAAC,GAAG;AACxD;AAAA,IACJ;AACA,QAAI,aAAa,KAAK,cAAc;AACpC,SAAK,gBAAgB,gBAAgB;AACrC,SAAK,gBAAgB,gBAAgB;AACrC,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,GAAG,UAAU,GAAG;AAC9D,aAAO;AAAA,IACX;AACA,iBAAa,KAAK,iBAAiB,UAAU;AAC7C,SAAK,SAAS,UAAU,UAAU;AAClC,SAAK,UAAU,OAAO;AACtB,QAAI,WAAW,KAAK,UAAU,KAAK;AACnC,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,YAAY,QAAQ;AAAA,EAC5E;AACA,EAAAA,iBAAgB,UAAU,mBAAmB,SAAU,YAAY;AAC/D,SAAK,kBAAkB;AACvB,QAAI,IAAI,WAAW,GAAG,IAAI,WAAW;AACrC,QAAIR,MAAK,KAAK,iBAAiB,gBAAgBA,IAAG,cAAc,gBAAgBA,IAAG;AACnF,QAAIS,MAAK,KAAK,iBAAiB,gBAAgBA,IAAG,cAAc,gBAAgBA,IAAG;AACnF,QAAI,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC;AACvC,QAAI,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC;AACvC,QAAI,QAAQ,GAAG,eAAe,aAAa;AAC3C,QAAI,QAAQ,GAAG,eAAe,aAAa;AAC3C,WAAO,EAAE,GAAM,EAAK;AAAA,EACxB;AACA,EAAAD,iBAAgB,UAAU,cAAc,SAAU,GAAG;AACjD,QAAI,CAAC,0BAA0B,EAAE,QAAQ,KAAK,QAAQ,uBAAuB,GAAG;AAC5E,mBAAa,CAAC;AACd,QAAE,gBAAgB;AAAA,IACtB;AAAA,EACJ;AACA,EAAAA,iBAAgB,UAAU,gBAAgB,WAAY;AAClD,WAAO;AAAA,MACH,GAAG,KAAK,gBAAgB,cAAc;AAAA,MACtC,GAAG,KAAK,gBAAgB,cAAc;AAAA,IAC1C;AAAA,EACJ;AACA,EAAAA,iBAAgB,UAAU,UAAU,WAAY;AAC5C,SAAK,UAAU;AAAA,EACnB;AACA,EAAAA,iBAAgB,UAAU,UAAU,WAAY;AAC5C,SAAK,MAAM,QAAQ;AAAA,EACvB;AACA,SAAOA;AACX,EAAE;AAEF,SAAS,4BAA4B,WAAW;AAC5C,MAAI,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,OAAO,SAAU,MAAM,KAAK;AAC1B,SAAK,OAAO,UAAU;AACtB,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AACA,SAAS,sBAAsB,WAAW,WAAW,SAAS,MAAM;AAChE,MAAI,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,OAAO,SAAU,MAAM,KAAK;AAC1B,SAAK,OAAO,UAAU;AACtB,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AAEL,UAAQ,aAAa,CAAC,CAAC,UAAU;AACjC,UAAQ,UAAU;AAClB,UAAQ,OAAO;AACf,SAAO;AACX;AAEA,SAAS,SAAS,QAAQ,QAAQ,QAAQ;AACtC,SAAO,QAAQ,SAAU,OAAO;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,UAAU,UAAU;AAC3B,oBAAc,cAAc;AAAA,IAChC,OACK;AACD,oBAAc,MAAM;AACpB,oBAAc,MAAM;AAAA,IACxB;AACA,WAAO,GAAG,aAAa,WAAY;AAC/B,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,MAAM,UAAU;AAAA,MACzB;AACA,aAAO,OAAO,QAAQ,MAAM,QAAQ,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC;AAAA,IAC3E,CAAC;AAAA,EACL,CAAC;AACL;AAEA,SAAS,YAAY,YAAY,UAAU;AAEvC,MAAI,OAAO,OAAO,KAAK,UAAU;AACjC,WAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,QAAI,MAAM,OAAO;AACjB,QAAI,WAAW,SAAS,SAAS;AAC7B,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAEA,IAAI,sBAAsB;AAC1B,IAAI,WAA0B,WAAY;AACtC,WAASE,UAAS,SAAS,SAAS,SAAS;AACzC,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,QAAQ,IAAI,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,SAAK,UAAU;AACf,QAAIV,MAAK,KAAK,QAAQ,QAAQ,OAAOA,IAAG,MAAM,QAAQA,IAAG,OAAO,MAAMA,IAAG,KAAK,SAASA,IAAG;AAE1F,SAAK,kBAAkB,IAAI,SAAS,SAAS,SAAS,sBAAsB,SAAS,WAAW,CAAC,MAAM,KAAK,GAAG;AAAA,MAC3G,MAAM;AAAA,MACN,UAAU;AAAA,IACd,CAAC,CAAC;AAEF,SAAK,kBAAkB,IAAI,SAAS,SAAS,SAAS,sBAAsB,SAAS,WAAW,CAAC,KAAK,MAAM,GAAG;AAAA,MAC3G,MAAM;AAAA,MACN,UAAU;AAAA,IACd,CAAC,CAAC;AACF,SAAK,aAAa,IAAI,WAAW,KAAK,OAAO;AAC7C,SAAK,WAAW,eAAe,KAAK,SAAS,KAAK,YAAY,KAAK,OAAO;AAC1E,SAAK,iBAAiB,IAAI,eAAe,KAAK,QAAQ,eAAe,KAAK,UAAU,SAAS,4BAA4B,KAAK,OAAO,CAAC;AACtI,SAAK,UAAU,IAAI,gBAAgB,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,gBAAgB,KAAK,UAAU,KAAK,OAAO;AAC/H,QAAI,gBAAgB,KAAK,OAAO,KAAK,IAAI;AACzC,SAAK,iBAAiB,IAAI,cAAc,QAAQ;AAAA,MAC5C;AAAA,QACI,MAAM;AAAA,QACN,SAAS;AAAA,MACb;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,SAAS;AAAA,MACb;AAAA,IACJ,CAAC;AACD,SAAK,sBAAsB;AAC3B,SAAK,KAAK;AAAA,EACd;AACA,EAAAU,UAAS,UAAU,OAAO,WAAY;AAClC,QAAI,QAAQ;AACZ,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,YAAY;AAEjB,SAAK,MAAM,GAAG,KAAK,MAAM,WAAW,WAAW,WAAY;AACvD,YAAM,oBAAoB,IAAI;AAAA,IAClC,CAAC;AAAA,EACL;AACA,EAAAA,UAAS,UAAU,wBAAwB,WAAY;AACnD,SAAK,wBAAwB,IAAI,cAAc,KAAK,SAAS;AAAA,MACzD;AAAA,QACI,MAAM,MAAM;AAAA,QACZ,SAAS,KAAK,cAAc,KAAK,IAAI;AAAA,MACzC;AAAA,IACJ,CAAC;AAAA,EACL;AACA,EAAAA,UAAS,UAAU,iBAAiB,WAAY;AAC5C,QAAI,QAAQ;AACZ,QAAI,QAAQ,KAAK,WAAW;AAC5B,UAAM,GAAG,MAAM,WAAW,iBAAiB,SAAU,gBAAgB;AACjE,UAAI,MAAM,QAAQ,YAAY;AAC1B,uBAAe,KAAK,MAAM,QAAQ,UAAU;AAAA,MAChD;AAAA,IACJ,CAAC;AAED,UAAM,GAAG,MAAM,WAAW,WAAW,SAAU,KAAK;AAChD,UAAI,UAAU,MAAM,cAAc;AAClC,YAAM,gBAAgB,GAAG;AAGzB,UAAI,MAAM,QAAQ,oBAAoB,MAAM;AACxC,cAAM,QAAQ,kBAAkB;AAChC;AAAA,MACJ;AAEA,UAAI,IAAI,MAAM,QAAQ,KAAK,IAAI,MAAM,QAAQ,GAAG;AAC5C,cAAM,oBAAoB,KAAK;AAAA,MACnC;AAAA,IACJ,CAAC;AAAA,EACL;AACA,EAAAA,UAAS,UAAU,eAAe,WAAY;AAC1C,QAAI,QAAQ;AAEZ,SAAK,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,WAAW,KAAK,SAAU,KAAK;AACtE,UAAI,CAAC,MAAM,cAAc,MAAM,QAAQ,UAAU,GAAG;AAChD,cAAM,SAAS,WAAW,KAAK;AAC/B,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,WAAW,GAAG;AAAA,MAC7D;AAAA,IACJ,CAAC;AACD,aAAS,KAAK,SAAS,OAAO,KAAK,OAAO;AAAA,MACtC;AAAA,QACI,QAAQ,KAAK,SAAS,MAAM,WAAW;AAAA,QACvC,QAAQ,KAAK,MAAM,WAAW;AAAA,MAClC;AAAA,MACA;AAAA,QACI,QAAQ,KAAK,SAAS,MAAM,WAAW;AAAA,QACvC,QAAQ,KAAK,MAAM,WAAW;AAAA,MAClC;AAAA,IACJ,CAAC;AAAA,EACL;AACA,EAAAA,UAAS,UAAU,cAAc,WAAY;AACzC,QAAI,QAAQ;AACZ,QAAI,UAAU,KAAK;AACnB,aAAS,QAAQ,OAAO,KAAK,OAAO;AAAA,MAChC;AAAA,QACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,QACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,MAClC;AAAA,MACA;AAAA,QACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,QACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,MAClC;AAAA,MACA;AAAA,QACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,QACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,MAClC;AAAA,MACA;AAAA,QACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,QACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,MAClC;AAAA,MACA;AAAA,QACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,QACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,MAClC;AAAA,MACA;AAAA,QACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,QACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,MAClC;AAAA,IACJ,CAAC;AACD,YAAQ,MAAM,GAAG,QAAQ,MAAM,WAAW,KAAK,SAAU,GAAG,KAAK;AAC7D,YAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,UAAU,GAAG;AACxD,UAAI,MAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK,GAAG,GAAG;AACtD,eAAO;AAAA,MACX;AAEA,UAAI,CAAC,QAAQ,aAAa;AACtB,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,YAAY;AACvD,YAAI,MAAM,WAAW,CAAC,GAAG;AACrB,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,UAAI,MAAM,cAAc,MAAM,QAAQ,YAAY,KAAK,MAAM,GAAG;AAC5D,cAAM,SAAS,gBAAgB,KAAK;AACpC,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AACD,YAAQ,MAAM,GAAG,QAAQ,MAAM,WAAW,WAAW,SAAU,KAAK,UAAU;AAC1E,UAAI,SAAS,KAAK,IAAI,IAAI,IAAI,MAAM,gBAAgB,QAAQ;AAC5D,UAAI,SAAS,KAAK,IAAI,IAAI,IAAI,MAAM,gBAAgB,QAAQ;AAC5D,UAAI,MAAM,WAAW,UAAU,QAAQ,MAAM,GAAG;AAC5C,cAAM,SAAS,gBAAgB,KAAK;AACpC,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK;AAChD;AAAA,MACJ;AACA,UAAI,MAAM,SAAS,KAAK,QAAQ,GAAG;AAC/B,cAAM,SAAS,gBAAgB,KAAK;AACpC;AAAA,MACJ;AACA,UAAI,QAAQ,cAAc;AACtB,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,WAAW,GAAG;AAAA,MAC7D;AACA,UAAI,MAAM,SAAS,cAAc;AAC7B,cAAM,SAAS,gBAAgB,KAAK;AAAA,MACxC;AAAA,IACJ,CAAC;AAAA,EACL;AACA,EAAAA,UAAS,UAAU,aAAa,SAAU,UAAU,QAAQ,QAAQ;AAChE,QAAI,yBAAyB;AAC7B,QAAI,KAAK,MAAM,OAAO,MAAM,SAAS,KACjC,WAAW,KAAK,QAAQ,kBACxB,SAAS,KAAK,QAAQ,sBACtB,SAAS,KAAK,QAAQ,uBACrB,SAAS,0BAA0B,SAAS,yBAAyB;AACtE,aAAO;AAAA,IACX;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,WAAW,SAAU,KAAK,UAAU;AACnD,QAAI,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,KAAK;AAAA,MACb,MAAM,IAAI;AAAA,MACV,MAAM,IAAI;AAAA,IACd;AAEA,QAAI,YAAY,KAAK,gBAAgB,IAAI,QAAQ;AACjD,QAAI,YAAY,KAAK,gBAAgB,IAAI,QAAQ;AACjD,SAAK,OAAO,QAAQ,UAAU,WAAW,IACnC,KAAK,OACL,UAAU;AAChB,SAAK,OAAO,QAAQ,UAAU,WAAW,IACnC,KAAK,OACL,UAAU;AAChB,SAAK,OAAO,KAAK,IAAI,UAAU,UAAU,UAAU,QAAQ;AAC3D,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,UAAU,MAAM,IAAI;AAE7D,QAAI,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,IAAI,GAAG;AAE5C,UAAI,KAAK,OAAO,KAAK,gBAAgB,gBACjC,KAAK,OAAO,KAAK,gBAAgB,gBACjC,KAAK,OAAO,KAAK,gBAAgB,gBACjC,KAAK,OAAO,KAAK,gBAAgB,cAAc;AAC/C,aAAK,SAAS,KAAK;AAAA,MACvB;AACA,WAAK,SAAS,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAC1D,aAAO;AAAA,IACX;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,aAAa,SAAU,GAAG;AACzC,QAAI,aAAa;AAAA,MACb,cAAc,KAAK,SAAS;AAAA,IAChC;AAEA,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,UAAU,GAAG;AACtD,WAAK,SAAS,gBAAgB,KAAK;AACnC,aAAO;AAAA,IACX;AACA,QAAI,CAAC,WAAW,cAAc;AAC1B,UAAI,YAAY,KAAK,QAAQ;AAC7B,UAAI,kBAAkB;AACtB,UAAI,aAAa,KAAK,eAAe;AACjC,YAAIV,MAAK,UAAU,OAAO,QAAQA,QAAO,SAAS,MAAMA;AACxD,YAAI,OAAO,IAAI,KAAK,gBAAgB,OAAO;AACvC,4BAAkB;AAClB,mBAAS,CAAC;AAAA,QACd;AAAA,MACJ;AACA,UAAI,KAAK,QAAQ,KAAK;AAClB,YAAI,GAAG,KAAK,QAAQ,GAAG;AAAA,MAC3B;AACA,UAAI,KAAK,QAAQ,SACb,CAAC,0BAA0B,EAAE,QAAQ,KAAK,QAAQ,uBAAuB,GAAG;AAC5E,cAAM,CAAC;AAAA,MACX;AACA,WAAK,gBAAgB,kBAAkB,OAAO,OAAO;AACrD,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,EAAAU,UAAS,UAAU,SAAS,WAAY;AACpC,QAAI,QAAQ;AACZ,QAAI,CAAC,KAAK,QAAQ,SAAS;AACvB;AAAA,IACJ;AAGA,QAAI,WAAW;AACX,WAAK,QAAQ,YAAY;AAAA,IAC7B;AACA,iBAAa,KAAK,aAAa;AAC/B,SAAK,gBAAgB,OAAO,WAAW,WAAY;AAC/C,YAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,MAAM;AAAA,IACrD,GAAG,KAAK,QAAQ,aAAa;AAAA,EACjC;AAEA,EAAAA,UAAS,UAAU,gBAAgB,SAAU,GAAG;AAC5C,QAAI,EAAE,WAAW,KAAK,WAAW,CAAC,KAAK,SAAS,SAAS;AACrD;AAAA,IACJ;AACA,QAAI,WAAW,KAAK;AACpB,aAAS,eAAe;AACxB,QAAI,CAAC,KAAK,cAAc,KAAK,QAAQ,YAAY,KAAK,MAAM,GAAG;AAC3D,WAAK,SAAS,WAAW,KAAK;AAC9B,UAAI,KAAK,QAAQ,cAAc,GAAkB;AAC7C,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,KAAK,cAAc,CAAC;AAAA,MAC5E;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,sBAAsB,SAAU,SAAS;AACxD,QAAI,YAAY,QAAQ;AAAE,gBAAU;AAAA,IAAM;AAC1C,QAAI,KAAK,KAAK,QAAQ,SAAS,SACzB,KAAK,QAAQ,WACb,CAAC,KAAK,OAAO;AACnB,QAAI,gBAAgB,UAAU,SAAS;AACvC,aAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,UAAI,OAAO,GAAG;AAGd,UAAI,KAAK,oBAAoB;AACzB;AAAA,MACJ;AACA,WAAK,MAAM,gBAAgB;AAAA,IAC/B;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,UAAU,SAAU,SAAS;AAC5C,QAAI,iBAAiB,KAAK,WAAW,OAAO;AAC5C,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,aAAa;AACtD,SAAK,gBAAgB,QAAQ,OAAO;AACpC,SAAK,gBAAgB,QAAQ,OAAO;AACpC,QAAI,gBAAgB;AAChB,WAAK,WAAW,WAAW,OAAO;AAClC,WAAK,SAAS,WAAW,OAAO;AAChC,WAAK,sBAAsB,QAAQ;AACnC,WAAK,sBAAsB;AAC3B,UAAI,KAAK,QAAQ,cAAc;AAC3B,aAAK,eAAe,WAAW,OAAO;AAAA,MAC1C;AAAA,IACJ;AACA,SAAK,QAAQ,QAAQ;AACrB,SAAK,gBAAgB,OAAO,KAAK,OAAO;AAAA,EAC5C;AACA,EAAAA,UAAS,UAAU,aAAa,SAAU,SAAS;AAC/C,QAAI,iBAAiB,YAAY,KAAK;AACtC,QAAI,gBAAgB;AAChB,WAAK,UAAU;AAAA,IACnB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,WAAW,SAAU,QAAQ,QAAQ,MAAM,QAAQ;AAClE,QAAI,SAAS,QAAQ;AAAE,aAAO;AAAA,IAAG;AACjC,QAAIV,MAAK,KAAK,cAAc,GAAG,IAAIA,IAAG,GAAG,IAAIA,IAAG;AAChD,aAAS,CAAC,SAAS,KAAK,SAAS;AACjC,cAAU;AACV,cAAU;AACV,SAAK,SAAS,QAAQ,QAAQ,MAAM,MAAM;AAAA,EAC9C;AACA,EAAAU,UAAS,UAAU,WAAW,SAAU,GAAG,GAAG,MAAM,QAAQ,gBAAgB;AACxE,QAAI,SAAS,QAAQ;AAAE,aAAO;AAAA,IAAG;AACjC,QAAI,WAAW,QAAQ;AAAE,eAAS,KAAK;AAAA,IAAQ;AAC/C,QAAI,mBAAmB,QAAQ;AAAE,uBAAiB;AAAA,QAC9C,OAAO,CAAC;AAAA,QACR,KAAK,CAAC;AAAA,MACV;AAAA,IAAG;AACH,QAAI,WAAW,KAAK,QAAQ,gBAAgB,OAAO,QAAQ,OAAO;AAClE,QAAI,aAAa,KAAK,cAAc;AACpC,QAAI,aAAa,SAAS,EAAE,GAAG,WAAW,GAAG,GAAG,WAAW,EAAE,GAAG,eAAe,KAAK;AACpF,QAAI,WAAW,SAAS;AAAA,MAAE;AAAA,MACtB;AAAA,IAAK,GAAG,eAAe,GAAG;AAC9B,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,UAAU,QAAQ;AAE3D,QAAI,YAAY,YAAY,QAAQ;AAChC;AACJ,QAAI,SAAS,KAAK,IAAI,SAAS,IAAI,WAAW,CAAC;AAC/C,QAAI,SAAS,KAAK,IAAI,SAAS,IAAI,WAAW,CAAC;AAG/C,QAAI,SAAS,uBAAuB,SAAS,qBAAqB;AAC9D,aAAO;AACP,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,iBAAiB;AAAA,IAC9D;AACA,SAAK,SAAS,KAAK,YAAY,UAAU,MAAM,QAAQ;AAAA,EAC3D;AACA,EAAAA,UAAS,UAAU,kBAAkB,SAAU,IAAI,MAAM,SAAS,SAAS,QAAQ;AAC/E,QAAI,YAAY,WAAW,EAAE;AAC7B,QAAI,MAAM,OAAO,SAAS;AAC1B,QAAI,YAAY,SAAUC,SAAQ,MAAM,aAAa;AACjD,UAAI,OAAOA,YAAW,UAAU;AAC5B,eAAOA;AAAA,MACX;AAEA,aAAOA,UAAS,KAAK,MAAM,OAAO,IAAI,cAAc,CAAC,IAAI;AAAA,IAC7D;AACA,cAAU,UAAU,SAAS,UAAU,aAAa,KAAK,QAAQ,WAAW;AAC5E,cAAU,UAAU,SAAS,UAAU,cAAc,KAAK,QAAQ,YAAY;AAC9E,QAAI,SAAS,SAAUC,MAAK,YAAYD,SAAQ,gBAAgB;AAC5D,MAAAC,QAAO;AACP,MAAAA,OAAM,eAAe,eAAeA,OAAMD,OAAM;AAChD,aAAOC;AAAA,IACX;AACA,QAAI,OAAO,OAAO,IAAI,MAAM,KAAK,cAAc,MAAM,SAAS,KAAK,eAAe;AAClF,QAAI,MAAM,OAAO,IAAI,KAAK,KAAK,cAAc,KAAK,SAAS,KAAK,eAAe;AAC/E,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,iBAAiB,WAAW,GAAG,GAAG;AAC3E;AAAA,IACJ;AACA,SAAK,SAAS,IAAI,MAAM,IAAI,KAAK,MAAM,MAAM;AAAA,EACjD;AACA,EAAAF,UAAS,UAAU,gBAAgB,SAAU,MAAM,QAAQ;AACvD,QAAI,SAAS,QAAQ;AAAE,aAAO;AAAA,IAAG;AACjC,QAAI,WAAW,QAAQ;AAAE,eAAS,KAAK;AAAA,IAAQ;AAC/C,QAAIV,MAAK,KAAK,gBAAgB,gBAAgB,GAAG,IAAIA,IAAG,UAAU,cAAcA,IAAG;AACnF,QAAIS,MAAK,KAAK,gBAAgB,gBAAgB,GAAG,IAAIA,IAAG,UAAU,cAAcA,IAAG;AACnF,QAAI,eAAe,aAAa;AAC5B,aAAO;AAAA,IACX;AAEA,QAAI,iBAAiB;AAGjB,WAAK,OAAO;AAAA,IAChB;AAEA,SAAK,SAAS,GAAG,GAAG,MAAM,MAAM;AAChC,WAAO;AAAA,EACX;AAEA,EAAAC,UAAS,UAAU,SAAS,WAAY;AACpC,SAAK,UAAU,KAAK,QAAQ;AAAA,EAChC;AACA,EAAAA,UAAS,UAAU,kBAAkB,SAAU,KAAK;AAChD,SAAK,gBAAgB,eAAe,IAAI,CAAC;AACzC,SAAK,gBAAgB,eAAe,IAAI,CAAC;AAAA,EAC7C;AACA,EAAAA,UAAS,UAAU,gBAAgB,WAAY;AAC3C,WAAO,KAAK,QAAQ,cAAc;AAAA,EACtC;AACA,EAAAA,UAAS,UAAU,SAAS,WAAY;AACpC,SAAK,QAAQ,UAAU;AAAA,EAC3B;AACA,EAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,yBAAqB,KAAK,SAAS,KAAK;AACxC,SAAK,QAAQ,UAAU;AAAA,EAC3B;AACA,EAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,QAAI,QAAQ;AACZ,QAAI,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,SAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,MAAM,KAAK,QAAQ;AAAA,IAAG,CAAC;AAAA,EAChE;AACA,SAAOA;AACX,EAAE;AAEF,IAAI,qBAAoC,SAAU,QAAQ;AACtD,YAAUG,qBAAoB,MAAM;AACpC,WAASA,oBAAmB,IAAI,SAAS;AACrC,QAAI,QAAQ,OAAO,KAAK,MAAM;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC,KAAK;AACN,QAAI,UAAU,WAAW,EAAE;AAC3B,QAAI,CAAC,SAAS;AACV,WAAK,kCAAkC;AACvC,aAAO;AAAA,IACX;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,UAAU,IAAI,mBAAmB,EAAE,MAAM,OAAO,EAAE,QAAQ;AAChE,QAAI,CAAC,MAAM,WAAW,OAAO,EAAE,OAAO;AAClC,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,IAAI,aAAa;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,UAAM,KAAK,OAAO;AAClB,WAAO;AAAA,EACX;AACA,EAAAA,oBAAmB,MAAM,SAAU,MAAM;AACrC,QAAI,OAAO,KAAK;AAChB,QAAI,YAAYA,oBAAmB,QAAQ,KAAK,SAAU,QAAQ;AAAE,aAAO,SAAS,OAAO;AAAA,IAAM,CAAC;AAClG,QAAI;AACA,aAAOA;AACX,QAAI,QAAQ,IAAI,GAAG;AACf,WAAK,mFAAmF;AACxF,aAAOA;AAAA,IACX;AACA,IAAAA,oBAAmB,WAAW,QAAQ;AACtC,IAAAA,oBAAmB,QAAQ,KAAK;AAAA,MAC5B;AAAA,MACA,YAAY,KAAK;AAAA,MACjB;AAAA,IACJ,CAAC;AACD,WAAOA;AAAA,EACX;AACA,EAAAA,oBAAmB,UAAU,aAAa,SAAU,SAAS;AACzD,QAAI,iBAAiB;AACrB,QAAI,QAAQ;AACZ,QAAI,UAAU,QAAQ,SAAS,KAAK,QAAQ;AAC5C,QAAI,CAAC,SAAS;AACV,WAAK,8EAA8E;AACnF,cAAQ;AAAA,IACZ,OACK;AACD,uBAAiB,KAAK,YAAY;AAClC,UAAI,gBAAgB;AAChB,aAAK,UAAU;AAAA,MACnB;AAAA,IACJ;AACA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,oBAAmB,UAAU,OAAO,SAAU,SAAS;AACnD,QAAI,QAAQ;AACZ,SAAK,UAAU;AAEf,YAAQ,qBAAqB;AAC7B,SAAK,WAAW,IAAI,SAAS,SAAS,KAAK,SAAS,KAAK,OAAO;AAChE,SAAK,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,WAAW,QAAQ,WAAY;AACtE,YAAM,QAAQ;AAAA,IAClB,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,MAAM,gBAAgB;AAC3B,SAAK,aAAa;AAElB,SAAK,oBAAoB,KAAK,OAAO;AACrC,QAAIb,MAAK,KAAK,SAAS,SAASA,IAAG,QAAQ,SAASA,IAAG;AACvD,QAAI,WAAW;AAAA,MACX,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AAEA,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,uBAAuB,QAAQ,GAAG;AAC3E;AAAA,IACJ;AACA,SAAK,SAAS,SAAS,SAAS,GAAG,SAAS,CAAC;AAAA,EACjD;AACA,EAAAa,oBAAmB,UAAU,eAAe,WAAY;AACpD,QAAI,QAAQ;AACZ,QAAI,UAAU,KAAK;AACnB,IAAAA,oBAAmB,QACd,KAAK,SAAU,GAAG,GAAG;AACtB,UAAIb;AACJ,UAAI,iBAAiBA,MAAK,CAAC,GACvBA,IAAG,SAAmB,IACtBA,IAAG,UAAqB,GACxBA;AACJ,UAAI,SAAS,EAAE,aAAa,cAAc,EAAE,cAAc;AAC1D,UAAI,SAAS,EAAE,aAAa,cAAc,EAAE,cAAc;AAC1D,aAAO,SAAS;AAAA,IACpB,CAAC,EACI,QAAQ,SAAU,MAAM;AACzB,UAAI,OAAO,KAAK;AAChB,UAAI,QAAQ,KAAK,SAAS,OAAO,SAAS,YAAY;AAClD,cAAM,QAAQ,KAAK,QAAQ,IAAI,KAAK,KAAK;AAAA,MAC7C;AAAA,IACJ,CAAC;AAAA,EACL;AACA,EAAAa,oBAAmB,UAAU,iBAAiB,WAAY;AAEtD,QAAI,KAAK,QAAQ,UAAU;AACvB,WAAK,GAAG,KAAK,WAAW,mBAAmB,WAAY;AACnD,YAAI,gBAAgB,SAAS;AAC7B,YAAI,kBACC,cAAc,YAAY,WACvB,cAAc,YAAY,aAAa;AAC3C,wBAAc,KAAK;AAAA,QACvB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,EAAAA,oBAAmB,UAAU,gBAAgB,WAAY;AACrD,aAAS,KAAK,SAAS,OAAO,MAAM;AAAA,MAChC,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AAAA,IACpB,CAAC;AAAA,EACL;AACA,EAAAA,oBAAmB,UAAU,sBAAsB,SAAU,SAAS;AAClE,SAAK,SAAS,QAAQ,OAAO;AAC7B,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,SAAS,OAAO;AACzD,SAAK,QAAQ,KAAK,WAAW,SAAS,OAAO;AAAA,EACjD;AACA,EAAAA,oBAAmB,UAAU,QAAQ,SAAUC,mBAAkB;AAC7D,QAAI,QAAQ;AACZ,IAAAA,kBAAiB,QAAQ,SAAUd,KAAI;AACnC,UAAI,MAAMA,IAAG,KAAK,YAAYA,IAAG;AACjC,sBAAgB,OAAO,WAAW,GAAG;AAAA,IACzC,CAAC;AAAA,EACL;AACA,EAAAa,oBAAmB,UAAU,UAAU,WAAY;AAC/C,QAAIb,MAAK,KAAK,WAAW,KAAK,OAAO,GAAG,iBAAiBA,IAAG,gBAAgB,QAAQA,IAAG;AACvF,QAAI,OAAO;AACP,UAAI,UAAU,KAAK;AACnB,WAAK,oBAAoB,OAAO;AAChC,UAAI,gBAAgB;AAChB,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,gBAAgB,OAAO;AAChE,aAAK,QAAQ,KAAK,WAAW,gBAAgB,OAAO;AAAA,MACxD;AACA,WAAK,SAAS,cAAc;AAAA,IAChC;AAAA,EACJ;AACA,EAAAa,oBAAmB,UAAU,SAAS,WAAY;AAC9C,SAAK,SAAS,OAAO;AACrB,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM;AAC/C,SAAK,QAAQ,KAAK,WAAW,MAAM;AAAA,EACvC;AACA,EAAAA,oBAAmB,UAAU,UAAU,WAAY;AAC/C,SAAK,SAAS,QAAQ;AACtB,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO;AAChD,SAAK,QAAQ,KAAK,WAAW,OAAO;AAAA,EACxC;AACA,EAAAA,oBAAmB,UAAU,UAAU,WAAY;AAC/C,SAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO;AAChD,SAAK,QAAQ,KAAK,WAAW,OAAO;AACpC,SAAK,SAAS,QAAQ;AAAA,EAC1B;AACA,EAAAA,oBAAmB,UAAU,gBAAgB,SAAU,OAAO;AAC1D,SAAK,aAAa,KAAK;AAAA,EAC3B;AACA,EAAAA,oBAAmB,UAAU,CAAC;AAC9B,EAAAA,oBAAmB,aAAa,CAAC;AACjC,SAAOA;AACX,EAAE,YAAY;AACd,SAAS,cAAc,IAAI,SAAS;AAChC,MAAI,KAAK,IAAI,mBAAmB,IAAI,OAAO;AAC3C,SAAO;AACX;AACA,cAAc,MAAM,mBAAmB;AACvC,cAAc,UAAU,mBAAmB;AAC3C,cAAc,aAAa,mBAAmB;AAC9C,IAAI,UAAU;", "names": ["d", "b", "__assign", "style", "e", "EventEmitter", "EventRegister", "CustomOptions", "OptionsConstructor", "ActionsHandler", "_a", "click", "Translater", "Base", "Transition", "Animation", "Behavior", "DirectionLockAction", "ScrollerActions", "_b", "<PERSON><PERSON><PERSON>", "offset", "pos", "BScrollConstructor", "propertiesConfig"]}
{"version": 3, "sources": ["../../pinia/dist/pinia.mjs"], "sourcesContent": ["/*!\n  * pinia v2.0.27\n  * (c) 2022 <PERSON>\n  * @license MIT\n  */\nimport { getCurrentInstance, inject, toRaw, watch, unref, markRaw, effectScope, ref, isVue2, isRef, isReactive, set, getCurrentScope, onScopeDispose, reactive, toRef, del, nextTick, computed, toRefs } from 'vue-demi';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\r\n * setActivePinia must be called to handle SSR at the top of functions like\r\n * `fetch`, `setup`, `serverPrefetch` and others\r\n */\r\nlet activePinia;\r\n/**\r\n * Sets or unsets the active pinia. Used in SSR and internally when calling\r\n * actions and getters\r\n *\r\n * @param pinia - Pinia instance\r\n */\r\nconst setActivePinia = (pinia) => (activePinia = pinia);\r\n/**\r\n * Get the currently active pinia if there is any.\r\n */\r\nconst getActivePinia = () => (getCurrentInstance() && inject(piniaSymbol)) || activePinia;\r\nconst piniaSymbol = ((process.env.NODE_ENV !== 'production') ? Symbol('pinia') : /* istanbul ignore next */ Symbol());\n\nfunction isPlainObject(\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\no) {\r\n    return (o &&\r\n        typeof o === 'object' &&\r\n        Object.prototype.toString.call(o) === '[object Object]' &&\r\n        typeof o.toJSON !== 'function');\r\n}\r\n// type DeepReadonly<T> = { readonly [P in keyof T]: DeepReadonly<T[P]> }\r\n// TODO: can we change these to numbers?\r\n/**\r\n * Possible types for SubscriptionCallback\r\n */\r\nvar MutationType;\r\n(function (MutationType) {\r\n    /**\r\n     * Direct mutation of the state:\r\n     *\r\n     * - `store.name = 'new name'`\r\n     * - `store.$state.name = 'new name'`\r\n     * - `store.list.push('new item')`\r\n     */\r\n    MutationType[\"direct\"] = \"direct\";\r\n    /**\r\n     * Mutated the state with `$patch` and an object\r\n     *\r\n     * - `store.$patch({ name: 'newName' })`\r\n     */\r\n    MutationType[\"patchObject\"] = \"patch object\";\r\n    /**\r\n     * Mutated the state with `$patch` and a function\r\n     *\r\n     * - `store.$patch(state => state.name = 'newName')`\r\n     */\r\n    MutationType[\"patchFunction\"] = \"patch function\";\r\n    // maybe reset? for $state = {} and $reset\r\n})(MutationType || (MutationType = {}));\n\nconst IS_CLIENT = typeof window !== 'undefined';\r\n/**\r\n * Should we add the devtools plugins.\r\n * - only if dev mode or forced through the prod devtools flag\r\n * - not in test\r\n * - only if window exists (could change in the future)\r\n */\r\nconst USE_DEVTOOLS = ((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test') && IS_CLIENT;\n\n/*\r\n * FileSaver.js A saveAs() FileSaver implementation.\r\n *\r\n * Originally by Eli Grey, adapted as an ESM module by Eduardo San Martin\r\n * Morote.\r\n *\r\n * License : MIT\r\n */\r\n// The one and only way of getting global scope in all environments\r\n// https://stackoverflow.com/q/3277182/1008999\r\nconst _global = /*#__PURE__*/ (() => typeof window === 'object' && window.window === window\r\n    ? window\r\n    : typeof self === 'object' && self.self === self\r\n        ? self\r\n        : typeof global === 'object' && global.global === global\r\n            ? global\r\n            : typeof globalThis === 'object'\r\n                ? globalThis\r\n                : { HTMLElement: null })();\r\nfunction bom(blob, { autoBom = false } = {}) {\r\n    // prepend BOM for UTF-8 XML and text/* types (including HTML)\r\n    // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\r\n    if (autoBom &&\r\n        /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\r\n        return new Blob([String.fromCharCode(0xfeff), blob], { type: blob.type });\r\n    }\r\n    return blob;\r\n}\r\nfunction download(url, name, opts) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', url);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = function () {\r\n        saveAs(xhr.response, name, opts);\r\n    };\r\n    xhr.onerror = function () {\r\n        console.error('could not download file');\r\n    };\r\n    xhr.send();\r\n}\r\nfunction corsEnabled(url) {\r\n    const xhr = new XMLHttpRequest();\r\n    // use sync to avoid popup blocker\r\n    xhr.open('HEAD', url, false);\r\n    try {\r\n        xhr.send();\r\n    }\r\n    catch (e) { }\r\n    return xhr.status >= 200 && xhr.status <= 299;\r\n}\r\n// `a.click()` doesn't work for all browsers (#465)\r\nfunction click(node) {\r\n    try {\r\n        node.dispatchEvent(new MouseEvent('click'));\r\n    }\r\n    catch (e) {\r\n        const evt = document.createEvent('MouseEvents');\r\n        evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80, 20, false, false, false, false, 0, null);\r\n        node.dispatchEvent(evt);\r\n    }\r\n}\r\nconst _navigator = \r\n typeof navigator === 'object' ? navigator : { userAgent: '' };\r\n// Detect WebView inside a native macOS app by ruling out all browsers\r\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\r\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\r\nconst isMacOSWebView = /*#__PURE__*/ (() => /Macintosh/.test(_navigator.userAgent) &&\r\n    /AppleWebKit/.test(_navigator.userAgent) &&\r\n    !/Safari/.test(_navigator.userAgent))();\r\nconst saveAs = !IS_CLIENT\r\n    ? () => { } // noop\r\n    : // Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView or mini program\r\n        typeof HTMLAnchorElement !== 'undefined' &&\r\n            'download' in HTMLAnchorElement.prototype &&\r\n            !isMacOSWebView\r\n            ? downloadSaveAs\r\n            : // Use msSaveOrOpenBlob as a second approach\r\n                'msSaveOrOpenBlob' in _navigator\r\n                    ? msSaveAs\r\n                    : // Fallback to using FileReader and a popup\r\n                        fileSaverSaveAs;\r\nfunction downloadSaveAs(blob, name = 'download', opts) {\r\n    const a = document.createElement('a');\r\n    a.download = name;\r\n    a.rel = 'noopener'; // tabnabbing\r\n    // TODO: detect chrome extensions & packaged apps\r\n    // a.target = '_blank'\r\n    if (typeof blob === 'string') {\r\n        // Support regular links\r\n        a.href = blob;\r\n        if (a.origin !== location.origin) {\r\n            if (corsEnabled(a.href)) {\r\n                download(blob, name, opts);\r\n            }\r\n            else {\r\n                a.target = '_blank';\r\n                click(a);\r\n            }\r\n        }\r\n        else {\r\n            click(a);\r\n        }\r\n    }\r\n    else {\r\n        // Support blobs\r\n        a.href = URL.createObjectURL(blob);\r\n        setTimeout(function () {\r\n            URL.revokeObjectURL(a.href);\r\n        }, 4e4); // 40s\r\n        setTimeout(function () {\r\n            click(a);\r\n        }, 0);\r\n    }\r\n}\r\nfunction msSaveAs(blob, name = 'download', opts) {\r\n    if (typeof blob === 'string') {\r\n        if (corsEnabled(blob)) {\r\n            download(blob, name, opts);\r\n        }\r\n        else {\r\n            const a = document.createElement('a');\r\n            a.href = blob;\r\n            a.target = '_blank';\r\n            setTimeout(function () {\r\n                click(a);\r\n            });\r\n        }\r\n    }\r\n    else {\r\n        // @ts-ignore: works on windows\r\n        navigator.msSaveOrOpenBlob(bom(blob, opts), name);\r\n    }\r\n}\r\nfunction fileSaverSaveAs(blob, name, opts, popup) {\r\n    // Open a popup immediately do go around popup blocker\r\n    // Mostly only available on user interaction and the fileReader is async so...\r\n    popup = popup || open('', '_blank');\r\n    if (popup) {\r\n        popup.document.title = popup.document.body.innerText = 'downloading...';\r\n    }\r\n    if (typeof blob === 'string')\r\n        return download(blob, name, opts);\r\n    const force = blob.type === 'application/octet-stream';\r\n    const isSafari = /constructor/i.test(String(_global.HTMLElement)) || 'safari' in _global;\r\n    const isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent);\r\n    if ((isChromeIOS || (force && isSafari) || isMacOSWebView) &&\r\n        typeof FileReader !== 'undefined') {\r\n        // Safari doesn't allow downloading of blob URLs\r\n        const reader = new FileReader();\r\n        reader.onloadend = function () {\r\n            let url = reader.result;\r\n            if (typeof url !== 'string') {\r\n                popup = null;\r\n                throw new Error('Wrong reader.result type');\r\n            }\r\n            url = isChromeIOS\r\n                ? url\r\n                : url.replace(/^data:[^;]*;/, 'data:attachment/file;');\r\n            if (popup) {\r\n                popup.location.href = url;\r\n            }\r\n            else {\r\n                location.assign(url);\r\n            }\r\n            popup = null; // reverse-tabnabbing #460\r\n        };\r\n        reader.readAsDataURL(blob);\r\n    }\r\n    else {\r\n        const url = URL.createObjectURL(blob);\r\n        if (popup)\r\n            popup.location.assign(url);\r\n        else\r\n            location.href = url;\r\n        popup = null; // reverse-tabnabbing #460\r\n        setTimeout(function () {\r\n            URL.revokeObjectURL(url);\r\n        }, 4e4); // 40s\r\n    }\r\n}\n\n/**\r\n * Shows a toast or console.log\r\n *\r\n * @param message - message to log\r\n * @param type - different color of the tooltip\r\n */\r\nfunction toastMessage(message, type) {\r\n    const piniaMessage = '🍍 ' + message;\r\n    if (typeof __VUE_DEVTOOLS_TOAST__ === 'function') {\r\n        __VUE_DEVTOOLS_TOAST__(piniaMessage, type);\r\n    }\r\n    else if (type === 'error') {\r\n        console.error(piniaMessage);\r\n    }\r\n    else if (type === 'warn') {\r\n        console.warn(piniaMessage);\r\n    }\r\n    else {\r\n        console.log(piniaMessage);\r\n    }\r\n}\r\nfunction isPinia(o) {\r\n    return '_a' in o && 'install' in o;\r\n}\n\nfunction checkClipboardAccess() {\r\n    if (!('clipboard' in navigator)) {\r\n        toastMessage(`Your browser doesn't support the Clipboard API`, 'error');\r\n        return true;\r\n    }\r\n}\r\nfunction checkNotFocusedError(error) {\r\n    if (error instanceof Error &&\r\n        error.message.toLowerCase().includes('document is not focused')) {\r\n        toastMessage('You need to activate the \"Emulate a focused page\" setting in the \"Rendering\" panel of devtools.', 'warn');\r\n        return true;\r\n    }\r\n    return false;\r\n}\r\nasync function actionGlobalCopyState(pinia) {\r\n    if (checkClipboardAccess())\r\n        return;\r\n    try {\r\n        await navigator.clipboard.writeText(JSON.stringify(pinia.state.value));\r\n        toastMessage('Global state copied to clipboard.');\r\n    }\r\n    catch (error) {\r\n        if (checkNotFocusedError(error))\r\n            return;\r\n        toastMessage(`Failed to serialize the state. Check the console for more details.`, 'error');\r\n        console.error(error);\r\n    }\r\n}\r\nasync function actionGlobalPasteState(pinia) {\r\n    if (checkClipboardAccess())\r\n        return;\r\n    try {\r\n        pinia.state.value = JSON.parse(await navigator.clipboard.readText());\r\n        toastMessage('Global state pasted from clipboard.');\r\n    }\r\n    catch (error) {\r\n        if (checkNotFocusedError(error))\r\n            return;\r\n        toastMessage(`Failed to deserialize the state from clipboard. Check the console for more details.`, 'error');\r\n        console.error(error);\r\n    }\r\n}\r\nasync function actionGlobalSaveState(pinia) {\r\n    try {\r\n        saveAs(new Blob([JSON.stringify(pinia.state.value)], {\r\n            type: 'text/plain;charset=utf-8',\r\n        }), 'pinia-state.json');\r\n    }\r\n    catch (error) {\r\n        toastMessage(`Failed to export the state as JSON. Check the console for more details.`, 'error');\r\n        console.error(error);\r\n    }\r\n}\r\nlet fileInput;\r\nfunction getFileOpener() {\r\n    if (!fileInput) {\r\n        fileInput = document.createElement('input');\r\n        fileInput.type = 'file';\r\n        fileInput.accept = '.json';\r\n    }\r\n    function openFile() {\r\n        return new Promise((resolve, reject) => {\r\n            fileInput.onchange = async () => {\r\n                const files = fileInput.files;\r\n                if (!files)\r\n                    return resolve(null);\r\n                const file = files.item(0);\r\n                if (!file)\r\n                    return resolve(null);\r\n                return resolve({ text: await file.text(), file });\r\n            };\r\n            // @ts-ignore: TODO: changed from 4.3 to 4.4\r\n            fileInput.oncancel = () => resolve(null);\r\n            fileInput.onerror = reject;\r\n            fileInput.click();\r\n        });\r\n    }\r\n    return openFile;\r\n}\r\nasync function actionGlobalOpenStateFile(pinia) {\r\n    try {\r\n        const open = await getFileOpener();\r\n        const result = await open();\r\n        if (!result)\r\n            return;\r\n        const { text, file } = result;\r\n        pinia.state.value = JSON.parse(text);\r\n        toastMessage(`Global state imported from \"${file.name}\".`);\r\n    }\r\n    catch (error) {\r\n        toastMessage(`Failed to export the state as JSON. Check the console for more details.`, 'error');\r\n        console.error(error);\r\n    }\r\n}\n\nfunction formatDisplay(display) {\r\n    return {\r\n        _custom: {\r\n            display,\r\n        },\r\n    };\r\n}\r\nconst PINIA_ROOT_LABEL = '🍍 Pinia (root)';\r\nconst PINIA_ROOT_ID = '_root';\r\nfunction formatStoreForInspectorTree(store) {\r\n    return isPinia(store)\r\n        ? {\r\n            id: PINIA_ROOT_ID,\r\n            label: PINIA_ROOT_LABEL,\r\n        }\r\n        : {\r\n            id: store.$id,\r\n            label: store.$id,\r\n        };\r\n}\r\nfunction formatStoreForInspectorState(store) {\r\n    if (isPinia(store)) {\r\n        const storeNames = Array.from(store._s.keys());\r\n        const storeMap = store._s;\r\n        const state = {\r\n            state: storeNames.map((storeId) => ({\r\n                editable: true,\r\n                key: storeId,\r\n                value: store.state.value[storeId],\r\n            })),\r\n            getters: storeNames\r\n                .filter((id) => storeMap.get(id)._getters)\r\n                .map((id) => {\r\n                const store = storeMap.get(id);\r\n                return {\r\n                    editable: false,\r\n                    key: id,\r\n                    value: store._getters.reduce((getters, key) => {\r\n                        getters[key] = store[key];\r\n                        return getters;\r\n                    }, {}),\r\n                };\r\n            }),\r\n        };\r\n        return state;\r\n    }\r\n    const state = {\r\n        state: Object.keys(store.$state).map((key) => ({\r\n            editable: true,\r\n            key,\r\n            value: store.$state[key],\r\n        })),\r\n    };\r\n    // avoid adding empty getters\r\n    if (store._getters && store._getters.length) {\r\n        state.getters = store._getters.map((getterName) => ({\r\n            editable: false,\r\n            key: getterName,\r\n            value: store[getterName],\r\n        }));\r\n    }\r\n    if (store._customProperties.size) {\r\n        state.customProperties = Array.from(store._customProperties).map((key) => ({\r\n            editable: true,\r\n            key,\r\n            value: store[key],\r\n        }));\r\n    }\r\n    return state;\r\n}\r\nfunction formatEventData(events) {\r\n    if (!events)\r\n        return {};\r\n    if (Array.isArray(events)) {\r\n        // TODO: handle add and delete for arrays and objects\r\n        return events.reduce((data, event) => {\r\n            data.keys.push(event.key);\r\n            data.operations.push(event.type);\r\n            data.oldValue[event.key] = event.oldValue;\r\n            data.newValue[event.key] = event.newValue;\r\n            return data;\r\n        }, {\r\n            oldValue: {},\r\n            keys: [],\r\n            operations: [],\r\n            newValue: {},\r\n        });\r\n    }\r\n    else {\r\n        return {\r\n            operation: formatDisplay(events.type),\r\n            key: formatDisplay(events.key),\r\n            oldValue: events.oldValue,\r\n            newValue: events.newValue,\r\n        };\r\n    }\r\n}\r\nfunction formatMutationType(type) {\r\n    switch (type) {\r\n        case MutationType.direct:\r\n            return 'mutation';\r\n        case MutationType.patchFunction:\r\n            return '$patch';\r\n        case MutationType.patchObject:\r\n            return '$patch';\r\n        default:\r\n            return 'unknown';\r\n    }\r\n}\n\n// timeline can be paused when directly changing the state\r\nlet isTimelineActive = true;\r\nconst componentStateTypes = [];\r\nconst MUTATIONS_LAYER_ID = 'pinia:mutations';\r\nconst INSPECTOR_ID = 'pinia';\r\n/**\r\n * Gets the displayed name of a store in devtools\r\n *\r\n * @param id - id of the store\r\n * @returns a formatted string\r\n */\r\nconst getStoreType = (id) => '🍍 ' + id;\r\n/**\r\n * Add the pinia plugin without any store. Allows displaying a Pinia plugin tab\r\n * as soon as it is added to the application.\r\n *\r\n * @param app - Vue application\r\n * @param pinia - pinia instance\r\n */\r\nfunction registerPiniaDevtools(app, pinia) {\r\n    setupDevtoolsPlugin({\r\n        id: 'dev.esm.pinia',\r\n        label: 'Pinia 🍍',\r\n        logo: 'https://pinia.vuejs.org/logo.svg',\r\n        packageName: 'pinia',\r\n        homepage: 'https://pinia.vuejs.org',\r\n        componentStateTypes,\r\n        app,\r\n    }, (api) => {\r\n        if (typeof api.now !== 'function') {\r\n            toastMessage('You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\r\n        }\r\n        api.addTimelineLayer({\r\n            id: MUTATIONS_LAYER_ID,\r\n            label: `Pinia 🍍`,\r\n            color: 0xe5df88,\r\n        });\r\n        api.addInspector({\r\n            id: INSPECTOR_ID,\r\n            label: 'Pinia 🍍',\r\n            icon: 'storage',\r\n            treeFilterPlaceholder: 'Search stores',\r\n            actions: [\r\n                {\r\n                    icon: 'content_copy',\r\n                    action: () => {\r\n                        actionGlobalCopyState(pinia);\r\n                    },\r\n                    tooltip: 'Serialize and copy the state',\r\n                },\r\n                {\r\n                    icon: 'content_paste',\r\n                    action: async () => {\r\n                        await actionGlobalPasteState(pinia);\r\n                        api.sendInspectorTree(INSPECTOR_ID);\r\n                        api.sendInspectorState(INSPECTOR_ID);\r\n                    },\r\n                    tooltip: 'Replace the state with the content of your clipboard',\r\n                },\r\n                {\r\n                    icon: 'save',\r\n                    action: () => {\r\n                        actionGlobalSaveState(pinia);\r\n                    },\r\n                    tooltip: 'Save the state as a JSON file',\r\n                },\r\n                {\r\n                    icon: 'folder_open',\r\n                    action: async () => {\r\n                        await actionGlobalOpenStateFile(pinia);\r\n                        api.sendInspectorTree(INSPECTOR_ID);\r\n                        api.sendInspectorState(INSPECTOR_ID);\r\n                    },\r\n                    tooltip: 'Import the state from a JSON file',\r\n                },\r\n            ],\r\n            nodeActions: [\r\n                {\r\n                    icon: 'restore',\r\n                    tooltip: 'Reset the state (option store only)',\r\n                    action: (nodeId) => {\r\n                        const store = pinia._s.get(nodeId);\r\n                        if (!store) {\r\n                            toastMessage(`Cannot reset \"${nodeId}\" store because it wasn't found.`, 'warn');\r\n                        }\r\n                        else if (!store._isOptionsAPI) {\r\n                            toastMessage(`Cannot reset \"${nodeId}\" store because it's a setup store.`, 'warn');\r\n                        }\r\n                        else {\r\n                            store.$reset();\r\n                            toastMessage(`Store \"${nodeId}\" reset.`);\r\n                        }\r\n                    },\r\n                },\r\n            ],\r\n        });\r\n        api.on.inspectComponent((payload, ctx) => {\r\n            const proxy = (payload.componentInstance &&\r\n                payload.componentInstance.proxy);\r\n            if (proxy && proxy._pStores) {\r\n                const piniaStores = payload.componentInstance.proxy._pStores;\r\n                Object.values(piniaStores).forEach((store) => {\r\n                    payload.instanceData.state.push({\r\n                        type: getStoreType(store.$id),\r\n                        key: 'state',\r\n                        editable: true,\r\n                        value: store._isOptionsAPI\r\n                            ? {\r\n                                _custom: {\r\n                                    value: toRaw(store.$state),\r\n                                    actions: [\r\n                                        {\r\n                                            icon: 'restore',\r\n                                            tooltip: 'Reset the state of this store',\r\n                                            action: () => store.$reset(),\r\n                                        },\r\n                                    ],\r\n                                },\r\n                            }\r\n                            : // NOTE: workaround to unwrap transferred refs\r\n                                Object.keys(store.$state).reduce((state, key) => {\r\n                                    state[key] = store.$state[key];\r\n                                    return state;\r\n                                }, {}),\r\n                    });\r\n                    if (store._getters && store._getters.length) {\r\n                        payload.instanceData.state.push({\r\n                            type: getStoreType(store.$id),\r\n                            key: 'getters',\r\n                            editable: false,\r\n                            value: store._getters.reduce((getters, key) => {\r\n                                try {\r\n                                    getters[key] = store[key];\r\n                                }\r\n                                catch (error) {\r\n                                    // @ts-expect-error: we just want to show it in devtools\r\n                                    getters[key] = error;\r\n                                }\r\n                                return getters;\r\n                            }, {}),\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        });\r\n        api.on.getInspectorTree((payload) => {\r\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\r\n                let stores = [pinia];\r\n                stores = stores.concat(Array.from(pinia._s.values()));\r\n                payload.rootNodes = (payload.filter\r\n                    ? stores.filter((store) => '$id' in store\r\n                        ? store.$id\r\n                            .toLowerCase()\r\n                            .includes(payload.filter.toLowerCase())\r\n                        : PINIA_ROOT_LABEL.toLowerCase().includes(payload.filter.toLowerCase()))\r\n                    : stores).map(formatStoreForInspectorTree);\r\n            }\r\n        });\r\n        api.on.getInspectorState((payload) => {\r\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\r\n                const inspectedStore = payload.nodeId === PINIA_ROOT_ID\r\n                    ? pinia\r\n                    : pinia._s.get(payload.nodeId);\r\n                if (!inspectedStore) {\r\n                    // this could be the selected store restored for a different project\r\n                    // so it's better not to say anything here\r\n                    return;\r\n                }\r\n                if (inspectedStore) {\r\n                    payload.state = formatStoreForInspectorState(inspectedStore);\r\n                }\r\n            }\r\n        });\r\n        api.on.editInspectorState((payload, ctx) => {\r\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\r\n                const inspectedStore = payload.nodeId === PINIA_ROOT_ID\r\n                    ? pinia\r\n                    : pinia._s.get(payload.nodeId);\r\n                if (!inspectedStore) {\r\n                    return toastMessage(`store \"${payload.nodeId}\" not found`, 'error');\r\n                }\r\n                const { path } = payload;\r\n                if (!isPinia(inspectedStore)) {\r\n                    // access only the state\r\n                    if (path.length !== 1 ||\r\n                        !inspectedStore._customProperties.has(path[0]) ||\r\n                        path[0] in inspectedStore.$state) {\r\n                        path.unshift('$state');\r\n                    }\r\n                }\r\n                else {\r\n                    // Root access, we can omit the `.value` because the devtools API does it for us\r\n                    path.unshift('state');\r\n                }\r\n                isTimelineActive = false;\r\n                payload.set(inspectedStore, path, payload.state.value);\r\n                isTimelineActive = true;\r\n            }\r\n        });\r\n        api.on.editComponentState((payload) => {\r\n            if (payload.type.startsWith('🍍')) {\r\n                const storeId = payload.type.replace(/^🍍\\s*/, '');\r\n                const store = pinia._s.get(storeId);\r\n                if (!store) {\r\n                    return toastMessage(`store \"${storeId}\" not found`, 'error');\r\n                }\r\n                const { path } = payload;\r\n                if (path[0] !== 'state') {\r\n                    return toastMessage(`Invalid path for store \"${storeId}\":\\n${path}\\nOnly state can be modified.`);\r\n                }\r\n                // rewrite the first entry to be able to directly set the state as\r\n                // well as any other path\r\n                path[0] = '$state';\r\n                isTimelineActive = false;\r\n                payload.set(store, path, payload.state.value);\r\n                isTimelineActive = true;\r\n            }\r\n        });\r\n    });\r\n}\r\nfunction addStoreToDevtools(app, store) {\r\n    if (!componentStateTypes.includes(getStoreType(store.$id))) {\r\n        componentStateTypes.push(getStoreType(store.$id));\r\n    }\r\n    setupDevtoolsPlugin({\r\n        id: 'dev.esm.pinia',\r\n        label: 'Pinia 🍍',\r\n        logo: 'https://pinia.vuejs.org/logo.svg',\r\n        packageName: 'pinia',\r\n        homepage: 'https://pinia.vuejs.org',\r\n        componentStateTypes,\r\n        app,\r\n        settings: {\r\n            logStoreChanges: {\r\n                label: 'Notify about new/deleted stores',\r\n                type: 'boolean',\r\n                defaultValue: true,\r\n            },\r\n            // useEmojis: {\r\n            //   label: 'Use emojis in messages ⚡️',\r\n            //   type: 'boolean',\r\n            //   defaultValue: true,\r\n            // },\r\n        },\r\n    }, (api) => {\r\n        // gracefully handle errors\r\n        const now = typeof api.now === 'function' ? api.now.bind(api) : Date.now;\r\n        store.$onAction(({ after, onError, name, args }) => {\r\n            const groupId = runningActionId++;\r\n            api.addTimelineEvent({\r\n                layerId: MUTATIONS_LAYER_ID,\r\n                event: {\r\n                    time: now(),\r\n                    title: '🛫 ' + name,\r\n                    subtitle: 'start',\r\n                    data: {\r\n                        store: formatDisplay(store.$id),\r\n                        action: formatDisplay(name),\r\n                        args,\r\n                    },\r\n                    groupId,\r\n                },\r\n            });\r\n            after((result) => {\r\n                activeAction = undefined;\r\n                api.addTimelineEvent({\r\n                    layerId: MUTATIONS_LAYER_ID,\r\n                    event: {\r\n                        time: now(),\r\n                        title: '🛬 ' + name,\r\n                        subtitle: 'end',\r\n                        data: {\r\n                            store: formatDisplay(store.$id),\r\n                            action: formatDisplay(name),\r\n                            args,\r\n                            result,\r\n                        },\r\n                        groupId,\r\n                    },\r\n                });\r\n            });\r\n            onError((error) => {\r\n                activeAction = undefined;\r\n                api.addTimelineEvent({\r\n                    layerId: MUTATIONS_LAYER_ID,\r\n                    event: {\r\n                        time: now(),\r\n                        logType: 'error',\r\n                        title: '💥 ' + name,\r\n                        subtitle: 'end',\r\n                        data: {\r\n                            store: formatDisplay(store.$id),\r\n                            action: formatDisplay(name),\r\n                            args,\r\n                            error,\r\n                        },\r\n                        groupId,\r\n                    },\r\n                });\r\n            });\r\n        }, true);\r\n        store._customProperties.forEach((name) => {\r\n            watch(() => unref(store[name]), (newValue, oldValue) => {\r\n                api.notifyComponentUpdate();\r\n                api.sendInspectorState(INSPECTOR_ID);\r\n                if (isTimelineActive) {\r\n                    api.addTimelineEvent({\r\n                        layerId: MUTATIONS_LAYER_ID,\r\n                        event: {\r\n                            time: now(),\r\n                            title: 'Change',\r\n                            subtitle: name,\r\n                            data: {\r\n                                newValue,\r\n                                oldValue,\r\n                            },\r\n                            groupId: activeAction,\r\n                        },\r\n                    });\r\n                }\r\n            }, { deep: true });\r\n        });\r\n        store.$subscribe(({ events, type }, state) => {\r\n            api.notifyComponentUpdate();\r\n            api.sendInspectorState(INSPECTOR_ID);\r\n            if (!isTimelineActive)\r\n                return;\r\n            // rootStore.state[store.id] = state\r\n            const eventData = {\r\n                time: now(),\r\n                title: formatMutationType(type),\r\n                data: {\r\n                    store: formatDisplay(store.$id),\r\n                    ...formatEventData(events),\r\n                },\r\n                groupId: activeAction,\r\n            };\r\n            // reset for the next mutation\r\n            activeAction = undefined;\r\n            if (type === MutationType.patchFunction) {\r\n                eventData.subtitle = '⤵️';\r\n            }\r\n            else if (type === MutationType.patchObject) {\r\n                eventData.subtitle = '🧩';\r\n            }\r\n            else if (events && !Array.isArray(events)) {\r\n                eventData.subtitle = events.type;\r\n            }\r\n            if (events) {\r\n                eventData.data['rawEvent(s)'] = {\r\n                    _custom: {\r\n                        display: 'DebuggerEvent',\r\n                        type: 'object',\r\n                        tooltip: 'raw DebuggerEvent[]',\r\n                        value: events,\r\n                    },\r\n                };\r\n            }\r\n            api.addTimelineEvent({\r\n                layerId: MUTATIONS_LAYER_ID,\r\n                event: eventData,\r\n            });\r\n        }, { detached: true, flush: 'sync' });\r\n        const hotUpdate = store._hotUpdate;\r\n        store._hotUpdate = markRaw((newStore) => {\r\n            hotUpdate(newStore);\r\n            api.addTimelineEvent({\r\n                layerId: MUTATIONS_LAYER_ID,\r\n                event: {\r\n                    time: now(),\r\n                    title: '🔥 ' + store.$id,\r\n                    subtitle: 'HMR update',\r\n                    data: {\r\n                        store: formatDisplay(store.$id),\r\n                        info: formatDisplay(`HMR update`),\r\n                    },\r\n                },\r\n            });\r\n            // update the devtools too\r\n            api.notifyComponentUpdate();\r\n            api.sendInspectorTree(INSPECTOR_ID);\r\n            api.sendInspectorState(INSPECTOR_ID);\r\n        });\r\n        const { $dispose } = store;\r\n        store.$dispose = () => {\r\n            $dispose();\r\n            api.notifyComponentUpdate();\r\n            api.sendInspectorTree(INSPECTOR_ID);\r\n            api.sendInspectorState(INSPECTOR_ID);\r\n            api.getSettings().logStoreChanges &&\r\n                toastMessage(`Disposed \"${store.$id}\" store 🗑`);\r\n        };\r\n        // trigger an update so it can display new registered stores\r\n        api.notifyComponentUpdate();\r\n        api.sendInspectorTree(INSPECTOR_ID);\r\n        api.sendInspectorState(INSPECTOR_ID);\r\n        api.getSettings().logStoreChanges &&\r\n            toastMessage(`\"${store.$id}\" store installed 🆕`);\r\n    });\r\n}\r\nlet runningActionId = 0;\r\nlet activeAction;\r\n/**\r\n * Patches a store to enable action grouping in devtools by wrapping the store with a Proxy that is passed as the\r\n * context of all actions, allowing us to set `runningAction` on each access and effectively associating any state\r\n * mutation to the action.\r\n *\r\n * @param store - store to patch\r\n * @param actionNames - list of actionst to patch\r\n */\r\nfunction patchActionForGrouping(store, actionNames) {\r\n    // original actions of the store as they are given by pinia. We are going to override them\r\n    const actions = actionNames.reduce((storeActions, actionName) => {\r\n        // use toRaw to avoid tracking #541\r\n        storeActions[actionName] = toRaw(store)[actionName];\r\n        return storeActions;\r\n    }, {});\r\n    for (const actionName in actions) {\r\n        store[actionName] = function () {\r\n            // setActivePinia(store._p)\r\n            // the running action id is incremented in a before action hook\r\n            const _actionId = runningActionId;\r\n            const trackedStore = new Proxy(store, {\r\n                get(...args) {\r\n                    activeAction = _actionId;\r\n                    return Reflect.get(...args);\r\n                },\r\n                set(...args) {\r\n                    activeAction = _actionId;\r\n                    return Reflect.set(...args);\r\n                },\r\n            });\r\n            return actions[actionName].apply(trackedStore, arguments);\r\n        };\r\n    }\r\n}\r\n/**\r\n * pinia.use(devtoolsPlugin)\r\n */\r\nfunction devtoolsPlugin({ app, store, options }) {\r\n    // HMR module\r\n    if (store.$id.startsWith('__hot:')) {\r\n        return;\r\n    }\r\n    // detect option api vs setup api\r\n    if (options.state) {\r\n        store._isOptionsAPI = true;\r\n    }\r\n    // only wrap actions in option-defined stores as this technique relies on\r\n    // wrapping the context of the action with a proxy\r\n    if (typeof options.state === 'function') {\r\n        patchActionForGrouping(\r\n        // @ts-expect-error: can cast the store...\r\n        store, Object.keys(options.actions));\r\n        const originalHotUpdate = store._hotUpdate;\r\n        // Upgrade the HMR to also update the new actions\r\n        toRaw(store)._hotUpdate = function (newStore) {\r\n            originalHotUpdate.apply(this, arguments);\r\n            patchActionForGrouping(store, Object.keys(newStore._hmrPayload.actions));\r\n        };\r\n    }\r\n    addStoreToDevtools(app, \r\n    // FIXME: is there a way to allow the assignment from Store<Id, S, G, A> to StoreGeneric?\r\n    store);\r\n}\n\n/**\r\n * Creates a Pinia instance to be used by the application\r\n */\r\nfunction createPinia() {\r\n    const scope = effectScope(true);\r\n    // NOTE: here we could check the window object for a state and directly set it\r\n    // if there is anything like it with Vue 3 SSR\r\n    const state = scope.run(() => ref({}));\r\n    let _p = [];\r\n    // plugins added before calling app.use(pinia)\r\n    let toBeInstalled = [];\r\n    const pinia = markRaw({\r\n        install(app) {\r\n            // this allows calling useStore() outside of a component setup after\r\n            // installing pinia's plugin\r\n            setActivePinia(pinia);\r\n            if (!isVue2) {\r\n                pinia._a = app;\r\n                app.provide(piniaSymbol, pinia);\r\n                app.config.globalProperties.$pinia = pinia;\r\n                /* istanbul ignore else */\r\n                if (USE_DEVTOOLS) {\r\n                    registerPiniaDevtools(app, pinia);\r\n                }\r\n                toBeInstalled.forEach((plugin) => _p.push(plugin));\r\n                toBeInstalled = [];\r\n            }\r\n        },\r\n        use(plugin) {\r\n            if (!this._a && !isVue2) {\r\n                toBeInstalled.push(plugin);\r\n            }\r\n            else {\r\n                _p.push(plugin);\r\n            }\r\n            return this;\r\n        },\r\n        _p,\r\n        // it's actually undefined here\r\n        // @ts-expect-error\r\n        _a: null,\r\n        _e: scope,\r\n        _s: new Map(),\r\n        state,\r\n    });\r\n    // pinia devtools rely on dev only features so they cannot be forced unless\r\n    // the dev build of Vue is used. Avoid old browsers like IE11.\r\n    if (USE_DEVTOOLS && typeof Proxy !== 'undefined') {\r\n        pinia.use(devtoolsPlugin);\r\n    }\r\n    return pinia;\r\n}\n\n/**\r\n * Checks if a function is a `StoreDefinition`.\r\n *\r\n * @param fn - object to test\r\n * @returns true if `fn` is a StoreDefinition\r\n */\r\nconst isUseStore = (fn) => {\r\n    return typeof fn === 'function' && typeof fn.$id === 'string';\r\n};\r\n/**\r\n * Mutates in place `newState` with `oldState` to _hot update_ it. It will\r\n * remove any key not existing in `newState` and recursively merge plain\r\n * objects.\r\n *\r\n * @param newState - new state object to be patched\r\n * @param oldState - old state that should be used to patch newState\r\n * @returns - newState\r\n */\r\nfunction patchObject(newState, oldState) {\r\n    // no need to go through symbols because they cannot be serialized anyway\r\n    for (const key in oldState) {\r\n        const subPatch = oldState[key];\r\n        // skip the whole sub tree\r\n        if (!(key in newState)) {\r\n            continue;\r\n        }\r\n        const targetValue = newState[key];\r\n        if (isPlainObject(targetValue) &&\r\n            isPlainObject(subPatch) &&\r\n            !isRef(subPatch) &&\r\n            !isReactive(subPatch)) {\r\n            newState[key] = patchObject(targetValue, subPatch);\r\n        }\r\n        else {\r\n            // objects are either a bit more complex (e.g. refs) or primitives, so we\r\n            // just set the whole thing\r\n            if (isVue2) {\r\n                set(newState, key, subPatch);\r\n            }\r\n            else {\r\n                newState[key] = subPatch;\r\n            }\r\n        }\r\n    }\r\n    return newState;\r\n}\r\n/**\r\n * Creates an _accept_ function to pass to `import.meta.hot` in Vite applications.\r\n *\r\n * @example\r\n * ```js\r\n * const useUser = defineStore(...)\r\n * if (import.meta.hot) {\r\n *   import.meta.hot.accept(acceptHMRUpdate(useUser, import.meta.hot))\r\n * }\r\n * ```\r\n *\r\n * @param initialUseStore - return of the defineStore to hot update\r\n * @param hot - `import.meta.hot`\r\n */\r\nfunction acceptHMRUpdate(initialUseStore, hot) {\r\n    // strip as much as possible from iife.prod\r\n    if (!(process.env.NODE_ENV !== 'production')) {\r\n        return () => { };\r\n    }\r\n    return (newModule) => {\r\n        const pinia = hot.data.pinia || initialUseStore._pinia;\r\n        if (!pinia) {\r\n            // this store is still not used\r\n            return;\r\n        }\r\n        // preserve the pinia instance across loads\r\n        hot.data.pinia = pinia;\r\n        // console.log('got data', newStore)\r\n        for (const exportName in newModule) {\r\n            const useStore = newModule[exportName];\r\n            // console.log('checking for', exportName)\r\n            if (isUseStore(useStore) && pinia._s.has(useStore.$id)) {\r\n                // console.log('Accepting update for', useStore.$id)\r\n                const id = useStore.$id;\r\n                if (id !== initialUseStore.$id) {\r\n                    console.warn(`The id of the store changed from \"${initialUseStore.$id}\" to \"${id}\". Reloading.`);\r\n                    // return import.meta.hot.invalidate()\r\n                    return hot.invalidate();\r\n                }\r\n                const existingStore = pinia._s.get(id);\r\n                if (!existingStore) {\r\n                    console.log(`[Pinia]: skipping hmr because store doesn't exist yet`);\r\n                    return;\r\n                }\r\n                useStore(pinia, existingStore);\r\n            }\r\n        }\r\n    };\r\n}\n\nconst noop = () => { };\r\nfunction addSubscription(subscriptions, callback, detached, onCleanup = noop) {\r\n    subscriptions.push(callback);\r\n    const removeSubscription = () => {\r\n        const idx = subscriptions.indexOf(callback);\r\n        if (idx > -1) {\r\n            subscriptions.splice(idx, 1);\r\n            onCleanup();\r\n        }\r\n    };\r\n    if (!detached && getCurrentScope()) {\r\n        onScopeDispose(removeSubscription);\r\n    }\r\n    return removeSubscription;\r\n}\r\nfunction triggerSubscriptions(subscriptions, ...args) {\r\n    subscriptions.slice().forEach((callback) => {\r\n        callback(...args);\r\n    });\r\n}\n\nfunction mergeReactiveObjects(target, patchToApply) {\r\n    // Handle Map instances\r\n    if (target instanceof Map && patchToApply instanceof Map) {\r\n        patchToApply.forEach((value, key) => target.set(key, value));\r\n    }\r\n    // Handle Set instances\r\n    if (target instanceof Set && patchToApply instanceof Set) {\r\n        patchToApply.forEach(target.add, target);\r\n    }\r\n    // no need to go through symbols because they cannot be serialized anyway\r\n    for (const key in patchToApply) {\r\n        if (!patchToApply.hasOwnProperty(key))\r\n            continue;\r\n        const subPatch = patchToApply[key];\r\n        const targetValue = target[key];\r\n        if (isPlainObject(targetValue) &&\r\n            isPlainObject(subPatch) &&\r\n            target.hasOwnProperty(key) &&\r\n            !isRef(subPatch) &&\r\n            !isReactive(subPatch)) {\r\n            // NOTE: here I wanted to warn about inconsistent types but it's not possible because in setup stores one might\r\n            // start the value of a property as a certain type e.g. a Map, and then for some reason, during SSR, change that\r\n            // to `undefined`. When trying to hydrate, we want to override the Map with `undefined`.\r\n            target[key] = mergeReactiveObjects(targetValue, subPatch);\r\n        }\r\n        else {\r\n            // @ts-expect-error: subPatch is a valid value\r\n            target[key] = subPatch;\r\n        }\r\n    }\r\n    return target;\r\n}\r\nconst skipHydrateSymbol = (process.env.NODE_ENV !== 'production')\r\n    ? Symbol('pinia:skipHydration')\r\n    : /* istanbul ignore next */ Symbol();\r\nconst skipHydrateMap = /*#__PURE__*/ new WeakMap();\r\n/**\r\n * Tells Pinia to skip the hydration process of a given object. This is useful in setup stores (only) when you return a\r\n * stateful object in the store but it isn't really state. e.g. returning a router instance in a setup store.\r\n *\r\n * @param obj - target object\r\n * @returns obj\r\n */\r\nfunction skipHydrate(obj) {\r\n    return isVue2\r\n        ? // in @vue/composition-api, the refs are sealed so defineProperty doesn't work...\r\n            /* istanbul ignore next */ skipHydrateMap.set(obj, 1) && obj\r\n        : Object.defineProperty(obj, skipHydrateSymbol, {});\r\n}\r\n/**\r\n * Returns whether a value should be hydrated\r\n *\r\n * @param obj - target variable\r\n * @returns true if `obj` should be hydrated\r\n */\r\nfunction shouldHydrate(obj) {\r\n    return isVue2\r\n        ? /* istanbul ignore next */ !skipHydrateMap.has(obj)\r\n        : !isPlainObject(obj) || !obj.hasOwnProperty(skipHydrateSymbol);\r\n}\r\nconst { assign } = Object;\r\nfunction isComputed(o) {\r\n    return !!(isRef(o) && o.effect);\r\n}\r\nfunction createOptionsStore(id, options, pinia, hot) {\r\n    const { state, actions, getters } = options;\r\n    const initialState = pinia.state.value[id];\r\n    let store;\r\n    function setup() {\r\n        if (!initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\r\n            /* istanbul ignore if */\r\n            if (isVue2) {\r\n                set(pinia.state.value, id, state ? state() : {});\r\n            }\r\n            else {\r\n                pinia.state.value[id] = state ? state() : {};\r\n            }\r\n        }\r\n        // avoid creating a state in pinia.state.value\r\n        const localState = (process.env.NODE_ENV !== 'production') && hot\r\n            ? // use ref() to unwrap refs inside state TODO: check if this is still necessary\r\n                toRefs(ref(state ? state() : {}).value)\r\n            : toRefs(pinia.state.value[id]);\r\n        return assign(localState, actions, Object.keys(getters || {}).reduce((computedGetters, name) => {\r\n            if ((process.env.NODE_ENV !== 'production') && name in localState) {\r\n                console.warn(`[🍍]: A getter cannot have the same name as another state property. Rename one of them. Found with \"${name}\" in store \"${id}\".`);\r\n            }\r\n            computedGetters[name] = markRaw(computed(() => {\r\n                setActivePinia(pinia);\r\n                // it was created just before\r\n                const store = pinia._s.get(id);\r\n                // allow cross using stores\r\n                /* istanbul ignore next */\r\n                if (isVue2 && !store._r)\r\n                    return;\r\n                // @ts-expect-error\r\n                // return getters![name].call(context, context)\r\n                // TODO: avoid reading the getter while assigning with a global variable\r\n                return getters[name].call(store, store);\r\n            }));\r\n            return computedGetters;\r\n        }, {}));\r\n    }\r\n    store = createSetupStore(id, setup, options, pinia, hot, true);\r\n    store.$reset = function $reset() {\r\n        const newState = state ? state() : {};\r\n        // we use a patch to group all changes into one single subscription\r\n        this.$patch(($state) => {\r\n            assign($state, newState);\r\n        });\r\n    };\r\n    return store;\r\n}\r\nfunction createSetupStore($id, setup, options = {}, pinia, hot, isOptionsStore) {\r\n    let scope;\r\n    const optionsForPlugin = assign({ actions: {} }, options);\r\n    /* istanbul ignore if */\r\n    // @ts-expect-error: active is an internal property\r\n    if ((process.env.NODE_ENV !== 'production') && !pinia._e.active) {\r\n        throw new Error('Pinia destroyed');\r\n    }\r\n    // watcher options for $subscribe\r\n    const $subscribeOptions = {\r\n        deep: true,\r\n        // flush: 'post',\r\n    };\r\n    /* istanbul ignore else */\r\n    if ((process.env.NODE_ENV !== 'production') && !isVue2) {\r\n        $subscribeOptions.onTrigger = (event) => {\r\n            /* istanbul ignore else */\r\n            if (isListening) {\r\n                debuggerEvents = event;\r\n                // avoid triggering this while the store is being built and the state is being set in pinia\r\n            }\r\n            else if (isListening == false && !store._hotUpdating) {\r\n                // let patch send all the events together later\r\n                /* istanbul ignore else */\r\n                if (Array.isArray(debuggerEvents)) {\r\n                    debuggerEvents.push(event);\r\n                }\r\n                else {\r\n                    console.error('🍍 debuggerEvents should be an array. This is most likely an internal Pinia bug.');\r\n                }\r\n            }\r\n        };\r\n    }\r\n    // internal state\r\n    let isListening; // set to true at the end\r\n    let isSyncListening; // set to true at the end\r\n    let subscriptions = markRaw([]);\r\n    let actionSubscriptions = markRaw([]);\r\n    let debuggerEvents;\r\n    const initialState = pinia.state.value[$id];\r\n    // avoid setting the state for option stores if it is set\r\n    // by the setup\r\n    if (!isOptionsStore && !initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\r\n        /* istanbul ignore if */\r\n        if (isVue2) {\r\n            set(pinia.state.value, $id, {});\r\n        }\r\n        else {\r\n            pinia.state.value[$id] = {};\r\n        }\r\n    }\r\n    const hotState = ref({});\r\n    // avoid triggering too many listeners\r\n    // https://github.com/vuejs/pinia/issues/1129\r\n    let activeListener;\r\n    function $patch(partialStateOrMutator) {\r\n        let subscriptionMutation;\r\n        isListening = isSyncListening = false;\r\n        // reset the debugger events since patches are sync\r\n        /* istanbul ignore else */\r\n        if ((process.env.NODE_ENV !== 'production')) {\r\n            debuggerEvents = [];\r\n        }\r\n        if (typeof partialStateOrMutator === 'function') {\r\n            partialStateOrMutator(pinia.state.value[$id]);\r\n            subscriptionMutation = {\r\n                type: MutationType.patchFunction,\r\n                storeId: $id,\r\n                events: debuggerEvents,\r\n            };\r\n        }\r\n        else {\r\n            mergeReactiveObjects(pinia.state.value[$id], partialStateOrMutator);\r\n            subscriptionMutation = {\r\n                type: MutationType.patchObject,\r\n                payload: partialStateOrMutator,\r\n                storeId: $id,\r\n                events: debuggerEvents,\r\n            };\r\n        }\r\n        const myListenerId = (activeListener = Symbol());\r\n        nextTick().then(() => {\r\n            if (activeListener === myListenerId) {\r\n                isListening = true;\r\n            }\r\n        });\r\n        isSyncListening = true;\r\n        // because we paused the watcher, we need to manually call the subscriptions\r\n        triggerSubscriptions(subscriptions, subscriptionMutation, pinia.state.value[$id]);\r\n    }\r\n    /* istanbul ignore next */\r\n    const $reset = (process.env.NODE_ENV !== 'production')\r\n        ? () => {\r\n            throw new Error(`🍍: Store \"${$id}\" is built using the setup syntax and does not implement $reset().`);\r\n        }\r\n        : noop;\r\n    function $dispose() {\r\n        scope.stop();\r\n        subscriptions = [];\r\n        actionSubscriptions = [];\r\n        pinia._s.delete($id);\r\n    }\r\n    /**\r\n     * Wraps an action to handle subscriptions.\r\n     *\r\n     * @param name - name of the action\r\n     * @param action - action to wrap\r\n     * @returns a wrapped action to handle subscriptions\r\n     */\r\n    function wrapAction(name, action) {\r\n        return function () {\r\n            setActivePinia(pinia);\r\n            const args = Array.from(arguments);\r\n            const afterCallbackList = [];\r\n            const onErrorCallbackList = [];\r\n            function after(callback) {\r\n                afterCallbackList.push(callback);\r\n            }\r\n            function onError(callback) {\r\n                onErrorCallbackList.push(callback);\r\n            }\r\n            // @ts-expect-error\r\n            triggerSubscriptions(actionSubscriptions, {\r\n                args,\r\n                name,\r\n                store,\r\n                after,\r\n                onError,\r\n            });\r\n            let ret;\r\n            try {\r\n                ret = action.apply(this && this.$id === $id ? this : store, args);\r\n                // handle sync errors\r\n            }\r\n            catch (error) {\r\n                triggerSubscriptions(onErrorCallbackList, error);\r\n                throw error;\r\n            }\r\n            if (ret instanceof Promise) {\r\n                return ret\r\n                    .then((value) => {\r\n                    triggerSubscriptions(afterCallbackList, value);\r\n                    return value;\r\n                })\r\n                    .catch((error) => {\r\n                    triggerSubscriptions(onErrorCallbackList, error);\r\n                    return Promise.reject(error);\r\n                });\r\n            }\r\n            // allow the afterCallback to override the return value\r\n            triggerSubscriptions(afterCallbackList, ret);\r\n            return ret;\r\n        };\r\n    }\r\n    const _hmrPayload = /*#__PURE__*/ markRaw({\r\n        actions: {},\r\n        getters: {},\r\n        state: [],\r\n        hotState,\r\n    });\r\n    const partialStore = {\r\n        _p: pinia,\r\n        // _s: scope,\r\n        $id,\r\n        $onAction: addSubscription.bind(null, actionSubscriptions),\r\n        $patch,\r\n        $reset,\r\n        $subscribe(callback, options = {}) {\r\n            const removeSubscription = addSubscription(subscriptions, callback, options.detached, () => stopWatcher());\r\n            const stopWatcher = scope.run(() => watch(() => pinia.state.value[$id], (state) => {\r\n                if (options.flush === 'sync' ? isSyncListening : isListening) {\r\n                    callback({\r\n                        storeId: $id,\r\n                        type: MutationType.direct,\r\n                        events: debuggerEvents,\r\n                    }, state);\r\n                }\r\n            }, assign({}, $subscribeOptions, options)));\r\n            return removeSubscription;\r\n        },\r\n        $dispose,\r\n    };\r\n    /* istanbul ignore if */\r\n    if (isVue2) {\r\n        // start as non ready\r\n        partialStore._r = false;\r\n    }\r\n    const store = reactive((process.env.NODE_ENV !== 'production') || USE_DEVTOOLS\r\n        ? assign({\r\n            _hmrPayload,\r\n            _customProperties: markRaw(new Set()), // devtools custom properties\r\n        }, partialStore\r\n        // must be added later\r\n        // setupStore\r\n        )\r\n        : partialStore);\r\n    // store the partial store now so the setup of stores can instantiate each other before they are finished without\r\n    // creating infinite loops.\r\n    pinia._s.set($id, store);\r\n    // TODO: idea create skipSerialize that marks properties as non serializable and they are skipped\r\n    const setupStore = pinia._e.run(() => {\r\n        scope = effectScope();\r\n        return scope.run(() => setup());\r\n    });\r\n    // overwrite existing actions to support $onAction\r\n    for (const key in setupStore) {\r\n        const prop = setupStore[key];\r\n        if ((isRef(prop) && !isComputed(prop)) || isReactive(prop)) {\r\n            // mark it as a piece of state to be serialized\r\n            if ((process.env.NODE_ENV !== 'production') && hot) {\r\n                set(hotState.value, key, toRef(setupStore, key));\r\n                // createOptionStore directly sets the state in pinia.state.value so we\r\n                // can just skip that\r\n            }\r\n            else if (!isOptionsStore) {\r\n                // in setup stores we must hydrate the state and sync pinia state tree with the refs the user just created\r\n                if (initialState && shouldHydrate(prop)) {\r\n                    if (isRef(prop)) {\r\n                        prop.value = initialState[key];\r\n                    }\r\n                    else {\r\n                        // probably a reactive object, lets recursively assign\r\n                        // @ts-expect-error: prop is unknown\r\n                        mergeReactiveObjects(prop, initialState[key]);\r\n                    }\r\n                }\r\n                // transfer the ref to the pinia state to keep everything in sync\r\n                /* istanbul ignore if */\r\n                if (isVue2) {\r\n                    set(pinia.state.value[$id], key, prop);\r\n                }\r\n                else {\r\n                    pinia.state.value[$id][key] = prop;\r\n                }\r\n            }\r\n            /* istanbul ignore else */\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                _hmrPayload.state.push(key);\r\n            }\r\n            // action\r\n        }\r\n        else if (typeof prop === 'function') {\r\n            // @ts-expect-error: we are overriding the function we avoid wrapping if\r\n            const actionValue = (process.env.NODE_ENV !== 'production') && hot ? prop : wrapAction(key, prop);\r\n            // this a hot module replacement store because the hotUpdate method needs\r\n            // to do it with the right context\r\n            /* istanbul ignore if */\r\n            if (isVue2) {\r\n                set(setupStore, key, actionValue);\r\n            }\r\n            else {\r\n                // @ts-expect-error\r\n                setupStore[key] = actionValue;\r\n            }\r\n            /* istanbul ignore else */\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                _hmrPayload.actions[key] = prop;\r\n            }\r\n            // list actions so they can be used in plugins\r\n            // @ts-expect-error\r\n            optionsForPlugin.actions[key] = prop;\r\n        }\r\n        else if ((process.env.NODE_ENV !== 'production')) {\r\n            // add getters for devtools\r\n            if (isComputed(prop)) {\r\n                _hmrPayload.getters[key] = isOptionsStore\r\n                    ? // @ts-expect-error\r\n                        options.getters[key]\r\n                    : prop;\r\n                if (IS_CLIENT) {\r\n                    const getters = setupStore._getters ||\r\n                        // @ts-expect-error: same\r\n                        (setupStore._getters = markRaw([]));\r\n                    getters.push(key);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // add the state, getters, and action properties\r\n    /* istanbul ignore if */\r\n    if (isVue2) {\r\n        Object.keys(setupStore).forEach((key) => {\r\n            set(store, key, setupStore[key]);\r\n        });\r\n    }\r\n    else {\r\n        assign(store, setupStore);\r\n        // allows retrieving reactive objects with `storeToRefs()`. Must be called after assigning to the reactive object.\r\n        // Make `storeToRefs()` work with `reactive()` #799\r\n        assign(toRaw(store), setupStore);\r\n    }\r\n    // use this instead of a computed with setter to be able to create it anywhere\r\n    // without linking the computed lifespan to wherever the store is first\r\n    // created.\r\n    Object.defineProperty(store, '$state', {\r\n        get: () => ((process.env.NODE_ENV !== 'production') && hot ? hotState.value : pinia.state.value[$id]),\r\n        set: (state) => {\r\n            /* istanbul ignore if */\r\n            if ((process.env.NODE_ENV !== 'production') && hot) {\r\n                throw new Error('cannot set hotState');\r\n            }\r\n            $patch(($state) => {\r\n                assign($state, state);\r\n            });\r\n        },\r\n    });\r\n    // add the hotUpdate before plugins to allow them to override it\r\n    /* istanbul ignore else */\r\n    if ((process.env.NODE_ENV !== 'production')) {\r\n        store._hotUpdate = markRaw((newStore) => {\r\n            store._hotUpdating = true;\r\n            newStore._hmrPayload.state.forEach((stateKey) => {\r\n                if (stateKey in store.$state) {\r\n                    const newStateTarget = newStore.$state[stateKey];\r\n                    const oldStateSource = store.$state[stateKey];\r\n                    if (typeof newStateTarget === 'object' &&\r\n                        isPlainObject(newStateTarget) &&\r\n                        isPlainObject(oldStateSource)) {\r\n                        patchObject(newStateTarget, oldStateSource);\r\n                    }\r\n                    else {\r\n                        // transfer the ref\r\n                        newStore.$state[stateKey] = oldStateSource;\r\n                    }\r\n                }\r\n                // patch direct access properties to allow store.stateProperty to work as\r\n                // store.$state.stateProperty\r\n                set(store, stateKey, toRef(newStore.$state, stateKey));\r\n            });\r\n            // remove deleted state properties\r\n            Object.keys(store.$state).forEach((stateKey) => {\r\n                if (!(stateKey in newStore.$state)) {\r\n                    del(store, stateKey);\r\n                }\r\n            });\r\n            // avoid devtools logging this as a mutation\r\n            isListening = false;\r\n            isSyncListening = false;\r\n            pinia.state.value[$id] = toRef(newStore._hmrPayload, 'hotState');\r\n            isSyncListening = true;\r\n            nextTick().then(() => {\r\n                isListening = true;\r\n            });\r\n            for (const actionName in newStore._hmrPayload.actions) {\r\n                const action = newStore[actionName];\r\n                set(store, actionName, wrapAction(actionName, action));\r\n            }\r\n            // TODO: does this work in both setup and option store?\r\n            for (const getterName in newStore._hmrPayload.getters) {\r\n                const getter = newStore._hmrPayload.getters[getterName];\r\n                const getterValue = isOptionsStore\r\n                    ? // special handling of options api\r\n                        computed(() => {\r\n                            setActivePinia(pinia);\r\n                            return getter.call(store, store);\r\n                        })\r\n                    : getter;\r\n                set(store, getterName, getterValue);\r\n            }\r\n            // remove deleted getters\r\n            Object.keys(store._hmrPayload.getters).forEach((key) => {\r\n                if (!(key in newStore._hmrPayload.getters)) {\r\n                    del(store, key);\r\n                }\r\n            });\r\n            // remove old actions\r\n            Object.keys(store._hmrPayload.actions).forEach((key) => {\r\n                if (!(key in newStore._hmrPayload.actions)) {\r\n                    del(store, key);\r\n                }\r\n            });\r\n            // update the values used in devtools and to allow deleting new properties later on\r\n            store._hmrPayload = newStore._hmrPayload;\r\n            store._getters = newStore._getters;\r\n            store._hotUpdating = false;\r\n        });\r\n    }\r\n    if (USE_DEVTOOLS) {\r\n        const nonEnumerable = {\r\n            writable: true,\r\n            configurable: true,\r\n            // avoid warning on devtools trying to display this property\r\n            enumerable: false,\r\n        };\r\n        ['_p', '_hmrPayload', '_getters', '_customProperties'].forEach((p) => {\r\n            Object.defineProperty(store, p, {\r\n                value: store[p],\r\n                ...nonEnumerable,\r\n            });\r\n        });\r\n    }\r\n    /* istanbul ignore if */\r\n    if (isVue2) {\r\n        // mark the store as ready before plugins\r\n        store._r = true;\r\n    }\r\n    // apply all plugins\r\n    pinia._p.forEach((extender) => {\r\n        /* istanbul ignore else */\r\n        if (USE_DEVTOOLS) {\r\n            const extensions = scope.run(() => extender({\r\n                store,\r\n                app: pinia._a,\r\n                pinia,\r\n                options: optionsForPlugin,\r\n            }));\r\n            Object.keys(extensions || {}).forEach((key) => store._customProperties.add(key));\r\n            assign(store, extensions);\r\n        }\r\n        else {\r\n            assign(store, scope.run(() => extender({\r\n                store,\r\n                app: pinia._a,\r\n                pinia,\r\n                options: optionsForPlugin,\r\n            })));\r\n        }\r\n    });\r\n    if ((process.env.NODE_ENV !== 'production') &&\r\n        store.$state &&\r\n        typeof store.$state === 'object' &&\r\n        typeof store.$state.constructor === 'function' &&\r\n        !store.$state.constructor.toString().includes('[native code]')) {\r\n        console.warn(`[🍍]: The \"state\" must be a plain object. It cannot be\\n` +\r\n            `\\tstate: () => new MyClass()\\n` +\r\n            `Found in store \"${store.$id}\".`);\r\n    }\r\n    // only apply hydrate to option stores with an initial state in pinia\r\n    if (initialState &&\r\n        isOptionsStore &&\r\n        options.hydrate) {\r\n        options.hydrate(store.$state, initialState);\r\n    }\r\n    isListening = true;\r\n    isSyncListening = true;\r\n    return store;\r\n}\r\nfunction defineStore(\r\n// TODO: add proper types from above\r\nidOrOptions, setup, setupOptions) {\r\n    let id;\r\n    let options;\r\n    const isSetupStore = typeof setup === 'function';\r\n    if (typeof idOrOptions === 'string') {\r\n        id = idOrOptions;\r\n        // the option store setup will contain the actual options in this case\r\n        options = isSetupStore ? setupOptions : setup;\r\n    }\r\n    else {\r\n        options = idOrOptions;\r\n        id = idOrOptions.id;\r\n    }\r\n    function useStore(pinia, hot) {\r\n        const currentInstance = getCurrentInstance();\r\n        pinia =\r\n            // in test mode, ignore the argument provided as we can always retrieve a\r\n            // pinia instance with getActivePinia()\r\n            ((process.env.NODE_ENV === 'test') && activePinia && activePinia._testing ? null : pinia) ||\r\n                (currentInstance && inject(piniaSymbol));\r\n        if (pinia)\r\n            setActivePinia(pinia);\r\n        if ((process.env.NODE_ENV !== 'production') && !activePinia) {\r\n            throw new Error(`[🍍]: getActivePinia was called with no active Pinia. Did you forget to install pinia?\\n` +\r\n                `\\tconst pinia = createPinia()\\n` +\r\n                `\\tapp.use(pinia)\\n` +\r\n                `This will fail in production.`);\r\n        }\r\n        pinia = activePinia;\r\n        if (!pinia._s.has(id)) {\r\n            // creating the store registers it in `pinia._s`\r\n            if (isSetupStore) {\r\n                createSetupStore(id, setup, options, pinia);\r\n            }\r\n            else {\r\n                createOptionsStore(id, options, pinia);\r\n            }\r\n            /* istanbul ignore else */\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                // @ts-expect-error: not the right inferred type\r\n                useStore._pinia = pinia;\r\n            }\r\n        }\r\n        const store = pinia._s.get(id);\r\n        if ((process.env.NODE_ENV !== 'production') && hot) {\r\n            const hotId = '__hot:' + id;\r\n            const newStore = isSetupStore\r\n                ? createSetupStore(hotId, setup, options, pinia, true)\r\n                : createOptionsStore(hotId, assign({}, options), pinia, true);\r\n            hot._hotUpdate(newStore);\r\n            // cleanup the state properties and the store from the cache\r\n            delete pinia.state.value[hotId];\r\n            pinia._s.delete(hotId);\r\n        }\r\n        // save stores in instances to access them devtools\r\n        if ((process.env.NODE_ENV !== 'production') &&\r\n            IS_CLIENT &&\r\n            currentInstance &&\r\n            currentInstance.proxy &&\r\n            // avoid adding stores that are just built for hot module replacement\r\n            !hot) {\r\n            const vm = currentInstance.proxy;\r\n            const cache = '_pStores' in vm ? vm._pStores : (vm._pStores = {});\r\n            cache[id] = store;\r\n        }\r\n        // StoreGeneric cannot be casted towards Store\r\n        return store;\r\n    }\r\n    useStore.$id = id;\r\n    return useStore;\r\n}\n\nlet mapStoreSuffix = 'Store';\r\n/**\r\n * Changes the suffix added by `mapStores()`. Can be set to an empty string.\r\n * Defaults to `\"Store\"`. Make sure to extend the MapStoresCustomization\r\n * interface if you are using TypeScript.\r\n *\r\n * @param suffix - new suffix\r\n */\r\nfunction setMapStoreSuffix(suffix // could be 'Store' but that would be annoying for JS\r\n) {\r\n    mapStoreSuffix = suffix;\r\n}\r\n/**\r\n * Allows using stores without the composition API (`setup()`) by generating an\r\n * object to be spread in the `computed` field of a component. It accepts a list\r\n * of store definitions.\r\n *\r\n * @example\r\n * ```js\r\n * export default {\r\n *   computed: {\r\n *     // other computed properties\r\n *     ...mapStores(useUserStore, useCartStore)\r\n *   },\r\n *\r\n *   created() {\r\n *     this.userStore // store with id \"user\"\r\n *     this.cartStore // store with id \"cart\"\r\n *   }\r\n * }\r\n * ```\r\n *\r\n * @param stores - list of stores to map to an object\r\n */\r\nfunction mapStores(...stores) {\r\n    if ((process.env.NODE_ENV !== 'production') && Array.isArray(stores[0])) {\r\n        console.warn(`[🍍]: Directly pass all stores to \"mapStores()\" without putting them in an array:\\n` +\r\n            `Replace\\n` +\r\n            `\\tmapStores([useAuthStore, useCartStore])\\n` +\r\n            `with\\n` +\r\n            `\\tmapStores(useAuthStore, useCartStore)\\n` +\r\n            `This will fail in production if not fixed.`);\r\n        stores = stores[0];\r\n    }\r\n    return stores.reduce((reduced, useStore) => {\r\n        // @ts-expect-error: $id is added by defineStore\r\n        reduced[useStore.$id + mapStoreSuffix] = function () {\r\n            return useStore(this.$pinia);\r\n        };\r\n        return reduced;\r\n    }, {});\r\n}\r\n/**\r\n * Allows using state and getters from one store without using the composition\r\n * API (`setup()`) by generating an object to be spread in the `computed` field\r\n * of a component.\r\n *\r\n * @param useStore - store to map from\r\n * @param keysOrMapper - array or object\r\n */\r\nfunction mapState(useStore, keysOrMapper) {\r\n    return Array.isArray(keysOrMapper)\r\n        ? keysOrMapper.reduce((reduced, key) => {\r\n            reduced[key] = function () {\r\n                return useStore(this.$pinia)[key];\r\n            };\r\n            return reduced;\r\n        }, {})\r\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\r\n            // @ts-expect-error\r\n            reduced[key] = function () {\r\n                const store = useStore(this.$pinia);\r\n                const storeKey = keysOrMapper[key];\r\n                // for some reason TS is unable to infer the type of storeKey to be a\r\n                // function\r\n                return typeof storeKey === 'function'\r\n                    ? storeKey.call(this, store)\r\n                    : store[storeKey];\r\n            };\r\n            return reduced;\r\n        }, {});\r\n}\r\n/**\r\n * Alias for `mapState()`. You should use `mapState()` instead.\r\n * @deprecated use `mapState()` instead.\r\n */\r\nconst mapGetters = mapState;\r\n/**\r\n * Allows directly using actions from your store without using the composition\r\n * API (`setup()`) by generating an object to be spread in the `methods` field\r\n * of a component.\r\n *\r\n * @param useStore - store to map from\r\n * @param keysOrMapper - array or object\r\n */\r\nfunction mapActions(useStore, keysOrMapper) {\r\n    return Array.isArray(keysOrMapper)\r\n        ? keysOrMapper.reduce((reduced, key) => {\r\n            // @ts-expect-error\r\n            reduced[key] = function (...args) {\r\n                return useStore(this.$pinia)[key](...args);\r\n            };\r\n            return reduced;\r\n        }, {})\r\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\r\n            // @ts-expect-error\r\n            reduced[key] = function (...args) {\r\n                return useStore(this.$pinia)[keysOrMapper[key]](...args);\r\n            };\r\n            return reduced;\r\n        }, {});\r\n}\r\n/**\r\n * Allows using state and getters from one store without using the composition\r\n * API (`setup()`) by generating an object to be spread in the `computed` field\r\n * of a component.\r\n *\r\n * @param useStore - store to map from\r\n * @param keysOrMapper - array or object\r\n */\r\nfunction mapWritableState(useStore, keysOrMapper) {\r\n    return Array.isArray(keysOrMapper)\r\n        ? keysOrMapper.reduce((reduced, key) => {\r\n            // @ts-ignore\r\n            reduced[key] = {\r\n                get() {\r\n                    return useStore(this.$pinia)[key];\r\n                },\r\n                set(value) {\r\n                    // it's easier to type it here as any\r\n                    return (useStore(this.$pinia)[key] = value);\r\n                },\r\n            };\r\n            return reduced;\r\n        }, {})\r\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\r\n            // @ts-ignore\r\n            reduced[key] = {\r\n                get() {\r\n                    return useStore(this.$pinia)[keysOrMapper[key]];\r\n                },\r\n                set(value) {\r\n                    // it's easier to type it here as any\r\n                    return (useStore(this.$pinia)[keysOrMapper[key]] = value);\r\n                },\r\n            };\r\n            return reduced;\r\n        }, {});\r\n}\n\n/**\r\n * Creates an object of references with all the state, getters, and plugin-added\r\n * state properties of the store. Similar to `toRefs()` but specifically\r\n * designed for Pinia stores so methods and non reactive properties are\r\n * completely ignored.\r\n *\r\n * @param store - store to extract the refs from\r\n */\r\nfunction storeToRefs(store) {\r\n    // See https://github.com/vuejs/pinia/issues/852\r\n    // It's easier to just use toRefs() even if it includes more stuff\r\n    if (isVue2) {\r\n        // @ts-expect-error: toRefs include methods and others\r\n        return toRefs(store);\r\n    }\r\n    else {\r\n        store = toRaw(store);\r\n        const refs = {};\r\n        for (const key in store) {\r\n            const value = store[key];\r\n            if (isRef(value) || isReactive(value)) {\r\n                // @ts-expect-error: the key is state or getter\r\n                refs[key] =\r\n                    // ---\r\n                    toRef(store, key);\r\n            }\r\n        }\r\n        return refs;\r\n    }\r\n}\n\n/**\r\n * Vue 2 Plugin that must be installed for pinia to work. Note **you don't need\r\n * this plugin if you are using Nuxt.js**. Use the `buildModule` instead:\r\n * https://pinia.vuejs.org/ssr/nuxt.html.\r\n *\r\n * @example\r\n * ```js\r\n * import Vue from 'vue'\r\n * import { PiniaVuePlugin, createPinia } from 'pinia'\r\n *\r\n * Vue.use(PiniaVuePlugin)\r\n * const pinia = createPinia()\r\n *\r\n * new Vue({\r\n *   el: '#app',\r\n *   // ...\r\n *   pinia,\r\n * })\r\n * ```\r\n *\r\n * @param _Vue - `Vue` imported from 'vue'.\r\n */\r\nconst PiniaVuePlugin = function (_Vue) {\r\n    // Equivalent of\r\n    // app.config.globalProperties.$pinia = pinia\r\n    _Vue.mixin({\r\n        beforeCreate() {\r\n            const options = this.$options;\r\n            if (options.pinia) {\r\n                const pinia = options.pinia;\r\n                // HACK: taken from provide(): https://github.com/vuejs/composition-api/blob/main/src/apis/inject.ts#L31\r\n                /* istanbul ignore else */\r\n                if (!this._provided) {\r\n                    const provideCache = {};\r\n                    Object.defineProperty(this, '_provided', {\r\n                        get: () => provideCache,\r\n                        set: (v) => Object.assign(provideCache, v),\r\n                    });\r\n                }\r\n                this._provided[piniaSymbol] = pinia;\r\n                // propagate the pinia instance in an SSR friendly way\r\n                // avoid adding it to nuxt twice\r\n                /* istanbul ignore else */\r\n                if (!this.$pinia) {\r\n                    this.$pinia = pinia;\r\n                }\r\n                pinia._a = this;\r\n                if (IS_CLIENT) {\r\n                    // this allows calling useStore() outside of a component setup after\r\n                    // installing pinia's plugin\r\n                    setActivePinia(pinia);\r\n                }\r\n                if (USE_DEVTOOLS) {\r\n                    registerPiniaDevtools(pinia._a, pinia);\r\n                }\r\n            }\r\n            else if (!this.$pinia && options.parent && options.parent.$pinia) {\r\n                this.$pinia = options.parent.$pinia;\r\n            }\r\n        },\r\n        destroyed() {\r\n            delete this._pStores;\r\n        },\r\n    });\r\n};\n\nexport { MutationType, PiniaVuePlugin, acceptHMRUpdate, createPinia, defineStore, getActivePinia, mapActions, mapGetters, mapState, mapStores, mapWritableState, setActivePinia, setMapStoreSuffix, skipHydrate, storeToRefs };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAI;AAOJ,IAAM,iBAAiB,CAAC,UAAW,cAAc;AAIjD,IAAM,iBAAiB,MAAO,mBAAmB,KAAK,OAAO,WAAW,KAAM;AAC9E,IAAM,cAAgB,OAAyC,OAAO,OAAO,IAA+B,OAAO;AAEnH,SAAS,cAET,GAAG;AACC,SAAQ,KACJ,OAAO,MAAM,YACb,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM,qBACtC,OAAO,EAAE,WAAW;AAC5B;AAMA,IAAI;AAAA,CACH,SAAUA,eAAc;AAQrB,EAAAA,cAAa,YAAY;AAMzB,EAAAA,cAAa,iBAAiB;AAM9B,EAAAA,cAAa,mBAAmB;AAEpC,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAEtC,IAAM,YAAY,OAAO,WAAW;AAOpC,IAAM,eAA6K;AAYnL,IAAM,WAAyB,MAAM,OAAO,WAAW,YAAY,OAAO,WAAW,SAC/E,SACA,OAAO,SAAS,YAAY,KAAK,SAAS,OACtC,OACA,OAAO,WAAW,YAAY,OAAO,WAAW,SAC5C,SACA,OAAO,eAAe,WAClB,aACA,EAAE,aAAa,KAAK,GAAG;AACzC,SAAS,IAAI,MAAM,EAAE,UAAU,MAAM,IAAI,CAAC,GAAG;AAGzC,MAAI,WACA,6EAA6E,KAAK,KAAK,IAAI,GAAG;AAC9F,WAAO,IAAI,KAAK,CAAC,OAAO,aAAa,KAAM,GAAG,IAAI,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC;AAAA,EAC5E;AACA,SAAO;AACX;AACA,SAAS,SAAS,KAAK,MAAM,MAAM;AAC/B,QAAM,MAAM,IAAI,eAAe;AAC/B,MAAI,KAAK,OAAO,GAAG;AACnB,MAAI,eAAe;AACnB,MAAI,SAAS,WAAY;AACrB,WAAO,IAAI,UAAU,MAAM,IAAI;AAAA,EACnC;AACA,MAAI,UAAU,WAAY;AACtB,YAAQ,MAAM,yBAAyB;AAAA,EAC3C;AACA,MAAI,KAAK;AACb;AACA,SAAS,YAAY,KAAK;AACtB,QAAM,MAAM,IAAI,eAAe;AAE/B,MAAI,KAAK,QAAQ,KAAK,KAAK;AAC3B,MAAI;AACA,QAAI,KAAK;AAAA,EACb,SACO,GAAP;AAAA,EAAY;AACZ,SAAO,IAAI,UAAU,OAAO,IAAI,UAAU;AAC9C;AAEA,SAAS,MAAM,MAAM;AACjB,MAAI;AACA,SAAK,cAAc,IAAI,WAAW,OAAO,CAAC;AAAA,EAC9C,SACO,GAAP;AACI,UAAM,MAAM,SAAS,YAAY,aAAa;AAC9C,QAAI,eAAe,SAAS,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,IAAI,OAAO,OAAO,OAAO,OAAO,GAAG,IAAI;AACpG,SAAK,cAAc,GAAG;AAAA,EAC1B;AACJ;AACA,IAAM,aACL,OAAO,cAAc,WAAW,YAAY,EAAE,WAAW,GAAG;AAI7D,IAAM,kBAAgC,MAAM,YAAY,KAAK,WAAW,SAAS,KAC7E,cAAc,KAAK,WAAW,SAAS,KACvC,CAAC,SAAS,KAAK,WAAW,SAAS,GAAG;AAC1C,IAAM,SAAS,CAAC,YACV,MAAM;AAAE,IAEN,OAAO,sBAAsB,eACzB,cAAc,kBAAkB,aAChC,CAAC,iBACC,iBAEE,sBAAsB,aAChB,WAEE;AACxB,SAAS,eAAe,MAAM,OAAO,YAAY,MAAM;AACnD,QAAM,IAAI,SAAS,cAAc,GAAG;AACpC,IAAE,WAAW;AACb,IAAE,MAAM;AAGR,MAAI,OAAO,SAAS,UAAU;AAE1B,MAAE,OAAO;AACT,QAAI,EAAE,WAAW,SAAS,QAAQ;AAC9B,UAAI,YAAY,EAAE,IAAI,GAAG;AACrB,iBAAS,MAAM,MAAM,IAAI;AAAA,MAC7B,OACK;AACD,UAAE,SAAS;AACX,cAAM,CAAC;AAAA,MACX;AAAA,IACJ,OACK;AACD,YAAM,CAAC;AAAA,IACX;AAAA,EACJ,OACK;AAED,MAAE,OAAO,IAAI,gBAAgB,IAAI;AACjC,eAAW,WAAY;AACnB,UAAI,gBAAgB,EAAE,IAAI;AAAA,IAC9B,GAAG,GAAG;AACN,eAAW,WAAY;AACnB,YAAM,CAAC;AAAA,IACX,GAAG,CAAC;AAAA,EACR;AACJ;AACA,SAAS,SAAS,MAAM,OAAO,YAAY,MAAM;AAC7C,MAAI,OAAO,SAAS,UAAU;AAC1B,QAAI,YAAY,IAAI,GAAG;AACnB,eAAS,MAAM,MAAM,IAAI;AAAA,IAC7B,OACK;AACD,YAAM,IAAI,SAAS,cAAc,GAAG;AACpC,QAAE,OAAO;AACT,QAAE,SAAS;AACX,iBAAW,WAAY;AACnB,cAAM,CAAC;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,OACK;AAED,cAAU,iBAAiB,IAAI,MAAM,IAAI,GAAG,IAAI;AAAA,EACpD;AACJ;AACA,SAAS,gBAAgB,MAAM,MAAM,MAAM,OAAO;AAG9C,UAAQ,SAAS,KAAK,IAAI,QAAQ;AAClC,MAAI,OAAO;AACP,UAAM,SAAS,QAAQ,MAAM,SAAS,KAAK,YAAY;AAAA,EAC3D;AACA,MAAI,OAAO,SAAS;AAChB,WAAO,SAAS,MAAM,MAAM,IAAI;AACpC,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,WAAW,eAAe,KAAK,OAAO,QAAQ,WAAW,CAAC,KAAK,YAAY;AACjF,QAAM,cAAc,eAAe,KAAK,UAAU,SAAS;AAC3D,OAAK,eAAgB,SAAS,YAAa,mBACvC,OAAO,eAAe,aAAa;AAEnC,UAAM,SAAS,IAAI,WAAW;AAC9B,WAAO,YAAY,WAAY;AAC3B,UAAI,MAAM,OAAO;AACjB,UAAI,OAAO,QAAQ,UAAU;AACzB,gBAAQ;AACR,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC9C;AACA,YAAM,cACA,MACA,IAAI,QAAQ,gBAAgB,uBAAuB;AACzD,UAAI,OAAO;AACP,cAAM,SAAS,OAAO;AAAA,MAC1B,OACK;AACD,iBAAS,OAAO,GAAG;AAAA,MACvB;AACA,cAAQ;AAAA,IACZ;AACA,WAAO,cAAc,IAAI;AAAA,EAC7B,OACK;AACD,UAAM,MAAM,IAAI,gBAAgB,IAAI;AACpC,QAAI;AACA,YAAM,SAAS,OAAO,GAAG;AAAA;AAEzB,eAAS,OAAO;AACpB,YAAQ;AACR,eAAW,WAAY;AACnB,UAAI,gBAAgB,GAAG;AAAA,IAC3B,GAAG,GAAG;AAAA,EACV;AACJ;AAQA,SAAS,aAAa,SAAS,MAAM;AACjC,QAAM,eAAe,eAAQ;AAC7B,MAAI,OAAO,2BAA2B,YAAY;AAC9C,2BAAuB,cAAc,IAAI;AAAA,EAC7C,WACS,SAAS,SAAS;AACvB,YAAQ,MAAM,YAAY;AAAA,EAC9B,WACS,SAAS,QAAQ;AACtB,YAAQ,KAAK,YAAY;AAAA,EAC7B,OACK;AACD,YAAQ,IAAI,YAAY;AAAA,EAC5B;AACJ;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,QAAQ,KAAK,aAAa;AACrC;AAEA,SAAS,uBAAuB;AAC5B,MAAI,EAAE,eAAe,YAAY;AAC7B,iBAAa,kDAAkD,OAAO;AACtE,WAAO;AAAA,EACX;AACJ;AACA,SAAS,qBAAqB,OAAO;AACjC,MAAI,iBAAiB,SACjB,MAAM,QAAQ,YAAY,EAAE,SAAS,yBAAyB,GAAG;AACjE,iBAAa,mGAAmG,MAAM;AACtH,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,eAAe,sBAAsB,OAAO;AACxC,MAAI,qBAAqB;AACrB;AACJ,MAAI;AACA,UAAM,UAAU,UAAU,UAAU,KAAK,UAAU,MAAM,MAAM,KAAK,CAAC;AACrE,iBAAa,mCAAmC;AAAA,EACpD,SACO,OAAP;AACI,QAAI,qBAAqB,KAAK;AAC1B;AACJ,iBAAa,sEAAsE,OAAO;AAC1F,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,eAAe,uBAAuB,OAAO;AACzC,MAAI,qBAAqB;AACrB;AACJ,MAAI;AACA,UAAM,MAAM,QAAQ,KAAK,MAAM,MAAM,UAAU,UAAU,SAAS,CAAC;AACnE,iBAAa,qCAAqC;AAAA,EACtD,SACO,OAAP;AACI,QAAI,qBAAqB,KAAK;AAC1B;AACJ,iBAAa,uFAAuF,OAAO;AAC3G,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,eAAe,sBAAsB,OAAO;AACxC,MAAI;AACA,WAAO,IAAI,KAAK,CAAC,KAAK,UAAU,MAAM,MAAM,KAAK,CAAC,GAAG;AAAA,MACjD,MAAM;AAAA,IACV,CAAC,GAAG,kBAAkB;AAAA,EAC1B,SACO,OAAP;AACI,iBAAa,2EAA2E,OAAO;AAC/F,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,IAAI;AACJ,SAAS,gBAAgB;AACrB,MAAI,CAAC,WAAW;AACZ,gBAAY,SAAS,cAAc,OAAO;AAC1C,cAAU,OAAO;AACjB,cAAU,SAAS;AAAA,EACvB;AACA,WAAS,WAAW;AAChB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,gBAAU,WAAW,YAAY;AAC7B,cAAM,QAAQ,UAAU;AACxB,YAAI,CAAC;AACD,iBAAO,QAAQ,IAAI;AACvB,cAAM,OAAO,MAAM,KAAK,CAAC;AACzB,YAAI,CAAC;AACD,iBAAO,QAAQ,IAAI;AACvB,eAAO,QAAQ,EAAE,MAAM,MAAM,KAAK,KAAK,GAAG,KAAK,CAAC;AAAA,MACpD;AAEA,gBAAU,WAAW,MAAM,QAAQ,IAAI;AACvC,gBAAU,UAAU;AACpB,gBAAU,MAAM;AAAA,IACpB,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,eAAe,0BAA0B,OAAO;AAC5C,MAAI;AACA,UAAMC,QAAO,MAAM,cAAc;AACjC,UAAM,SAAS,MAAMA,MAAK;AAC1B,QAAI,CAAC;AACD;AACJ,UAAM,EAAE,MAAM,KAAK,IAAI;AACvB,UAAM,MAAM,QAAQ,KAAK,MAAM,IAAI;AACnC,iBAAa,+BAA+B,KAAK,QAAQ;AAAA,EAC7D,SACO,OAAP;AACI,iBAAa,2EAA2E,OAAO;AAC/F,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AAEA,SAAS,cAAc,SAAS;AAC5B,SAAO;AAAA,IACH,SAAS;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,mBAAmB;AACzB,IAAM,gBAAgB;AACtB,SAAS,4BAA4B,OAAO;AACxC,SAAO,QAAQ,KAAK,IACd;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,EACX,IACE;AAAA,IACE,IAAI,MAAM;AAAA,IACV,OAAO,MAAM;AAAA,EACjB;AACR;AACA,SAAS,6BAA6B,OAAO;AACzC,MAAI,QAAQ,KAAK,GAAG;AAChB,UAAM,aAAa,MAAM,KAAK,MAAM,GAAG,KAAK,CAAC;AAC7C,UAAM,WAAW,MAAM;AACvB,UAAMC,SAAQ;AAAA,MACV,OAAO,WAAW,IAAI,CAAC,aAAa;AAAA,QAChC,UAAU;AAAA,QACV,KAAK;AAAA,QACL,OAAO,MAAM,MAAM,MAAM;AAAA,MAC7B,EAAE;AAAA,MACF,SAAS,WACJ,OAAO,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,QAAQ,EACxC,IAAI,CAAC,OAAO;AACb,cAAMC,SAAQ,SAAS,IAAI,EAAE;AAC7B,eAAO;AAAA,UACH,UAAU;AAAA,UACV,KAAK;AAAA,UACL,OAAOA,OAAM,SAAS,OAAO,CAAC,SAAS,QAAQ;AAC3C,oBAAQ,OAAOA,OAAM;AACrB,mBAAO;AAAA,UACX,GAAG,CAAC,CAAC;AAAA,QACT;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAOD;AAAA,EACX;AACA,QAAM,QAAQ;AAAA,IACV,OAAO,OAAO,KAAK,MAAM,MAAM,EAAE,IAAI,CAAC,SAAS;AAAA,MAC3C,UAAU;AAAA,MACV;AAAA,MACA,OAAO,MAAM,OAAO;AAAA,IACxB,EAAE;AAAA,EACN;AAEA,MAAI,MAAM,YAAY,MAAM,SAAS,QAAQ;AACzC,UAAM,UAAU,MAAM,SAAS,IAAI,CAAC,gBAAgB;AAAA,MAChD,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,MAAM;AAAA,IACjB,EAAE;AAAA,EACN;AACA,MAAI,MAAM,kBAAkB,MAAM;AAC9B,UAAM,mBAAmB,MAAM,KAAK,MAAM,iBAAiB,EAAE,IAAI,CAAC,SAAS;AAAA,MACvE,UAAU;AAAA,MACV;AAAA,MACA,OAAO,MAAM;AAAA,IACjB,EAAE;AAAA,EACN;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,QAAQ;AAC7B,MAAI,CAAC;AACD,WAAO,CAAC;AACZ,MAAI,MAAM,QAAQ,MAAM,GAAG;AAEvB,WAAO,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,WAAK,KAAK,KAAK,MAAM,GAAG;AACxB,WAAK,WAAW,KAAK,MAAM,IAAI;AAC/B,WAAK,SAAS,MAAM,OAAO,MAAM;AACjC,WAAK,SAAS,MAAM,OAAO,MAAM;AACjC,aAAO;AAAA,IACX,GAAG;AAAA,MACC,UAAU,CAAC;AAAA,MACX,MAAM,CAAC;AAAA,MACP,YAAY,CAAC;AAAA,MACb,UAAU,CAAC;AAAA,IACf,CAAC;AAAA,EACL,OACK;AACD,WAAO;AAAA,MACH,WAAW,cAAc,OAAO,IAAI;AAAA,MACpC,KAAK,cAAc,OAAO,GAAG;AAAA,MAC7B,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,IACrB;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,MAAM;AAC9B,UAAQ,MAAM;AAAA,IACV,KAAK,aAAa;AACd,aAAO;AAAA,IACX,KAAK,aAAa;AACd,aAAO;AAAA,IACX,KAAK,aAAa;AACd,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AAGA,IAAI,mBAAmB;AACvB,IAAM,sBAAsB,CAAC;AAC7B,IAAM,qBAAqB;AAC3B,IAAM,eAAe;AAOrB,IAAM,eAAe,CAAC,OAAO,eAAQ;AAQrC,SAAS,sBAAsB,KAAK,OAAO;AACvC,sBAAoB;AAAA,IAChB,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACJ,GAAG,CAAC,QAAQ;AACR,QAAI,OAAO,IAAI,QAAQ,YAAY;AAC/B,mBAAa,yMAAyM;AAAA,IAC1N;AACA,QAAI,iBAAiB;AAAA,MACjB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,IACX,CAAC;AACD,QAAI,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM;AAAA,UACN,QAAQ,MAAM;AACV,kCAAsB,KAAK;AAAA,UAC/B;AAAA,UACA,SAAS;AAAA,QACb;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,QAAQ,YAAY;AAChB,kBAAM,uBAAuB,KAAK;AAClC,gBAAI,kBAAkB,YAAY;AAClC,gBAAI,mBAAmB,YAAY;AAAA,UACvC;AAAA,UACA,SAAS;AAAA,QACb;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,QAAQ,MAAM;AACV,kCAAsB,KAAK;AAAA,UAC/B;AAAA,UACA,SAAS;AAAA,QACb;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,QAAQ,YAAY;AAChB,kBAAM,0BAA0B,KAAK;AACrC,gBAAI,kBAAkB,YAAY;AAClC,gBAAI,mBAAmB,YAAY;AAAA,UACvC;AAAA,UACA,SAAS;AAAA,QACb;AAAA,MACJ;AAAA,MACA,aAAa;AAAA,QACT;AAAA,UACI,MAAM;AAAA,UACN,SAAS;AAAA,UACT,QAAQ,CAAC,WAAW;AAChB,kBAAM,QAAQ,MAAM,GAAG,IAAI,MAAM;AACjC,gBAAI,CAAC,OAAO;AACR,2BAAa,iBAAiB,0CAA0C,MAAM;AAAA,YAClF,WACS,CAAC,MAAM,eAAe;AAC3B,2BAAa,iBAAiB,6CAA6C,MAAM;AAAA,YACrF,OACK;AACD,oBAAM,OAAO;AACb,2BAAa,UAAU,gBAAgB;AAAA,YAC3C;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,iBAAiB,CAAC,SAAS,QAAQ;AACtC,YAAM,QAAS,QAAQ,qBACnB,QAAQ,kBAAkB;AAC9B,UAAI,SAAS,MAAM,UAAU;AACzB,cAAM,cAAc,QAAQ,kBAAkB,MAAM;AACpD,eAAO,OAAO,WAAW,EAAE,QAAQ,CAAC,UAAU;AAC1C,kBAAQ,aAAa,MAAM,KAAK;AAAA,YAC5B,MAAM,aAAa,MAAM,GAAG;AAAA,YAC5B,KAAK;AAAA,YACL,UAAU;AAAA,YACV,OAAO,MAAM,gBACP;AAAA,cACE,SAAS;AAAA,gBACL,OAAO,MAAM,MAAM,MAAM;AAAA,gBACzB,SAAS;AAAA,kBACL;AAAA,oBACI,MAAM;AAAA,oBACN,SAAS;AAAA,oBACT,QAAQ,MAAM,MAAM,OAAO;AAAA,kBAC/B;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,IAEI,OAAO,KAAK,MAAM,MAAM,EAAE,OAAO,CAAC,OAAO,QAAQ;AAC7C,oBAAM,OAAO,MAAM,OAAO;AAC1B,qBAAO;AAAA,YACX,GAAG,CAAC,CAAC;AAAA,UACjB,CAAC;AACD,cAAI,MAAM,YAAY,MAAM,SAAS,QAAQ;AACzC,oBAAQ,aAAa,MAAM,KAAK;AAAA,cAC5B,MAAM,aAAa,MAAM,GAAG;AAAA,cAC5B,KAAK;AAAA,cACL,UAAU;AAAA,cACV,OAAO,MAAM,SAAS,OAAO,CAAC,SAAS,QAAQ;AAC3C,oBAAI;AACA,0BAAQ,OAAO,MAAM;AAAA,gBACzB,SACO,OAAP;AAEI,0BAAQ,OAAO;AAAA,gBACnB;AACA,uBAAO;AAAA,cACX,GAAG,CAAC,CAAC;AAAA,YACT,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,iBAAiB,CAAC,YAAY;AACjC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,cAAc;AAC7D,YAAI,SAAS,CAAC,KAAK;AACnB,iBAAS,OAAO,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO,CAAC,CAAC;AACpD,gBAAQ,aAAa,QAAQ,SACvB,OAAO,OAAO,CAAC,UAAU,SAAS,QAC9B,MAAM,IACH,YAAY,EACZ,SAAS,QAAQ,OAAO,YAAY,CAAC,IACxC,iBAAiB,YAAY,EAAE,SAAS,QAAQ,OAAO,YAAY,CAAC,CAAC,IACzE,QAAQ,IAAI,2BAA2B;AAAA,MACjD;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,kBAAkB,CAAC,YAAY;AAClC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,cAAc;AAC7D,cAAM,iBAAiB,QAAQ,WAAW,gBACpC,QACA,MAAM,GAAG,IAAI,QAAQ,MAAM;AACjC,YAAI,CAAC,gBAAgB;AAGjB;AAAA,QACJ;AACA,YAAI,gBAAgB;AAChB,kBAAQ,QAAQ,6BAA6B,cAAc;AAAA,QAC/D;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,mBAAmB,CAAC,SAAS,QAAQ;AACxC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,cAAc;AAC7D,cAAM,iBAAiB,QAAQ,WAAW,gBACpC,QACA,MAAM,GAAG,IAAI,QAAQ,MAAM;AACjC,YAAI,CAAC,gBAAgB;AACjB,iBAAO,aAAa,UAAU,QAAQ,qBAAqB,OAAO;AAAA,QACtE;AACA,cAAM,EAAE,KAAK,IAAI;AACjB,YAAI,CAAC,QAAQ,cAAc,GAAG;AAE1B,cAAI,KAAK,WAAW,KAChB,CAAC,eAAe,kBAAkB,IAAI,KAAK,EAAE,KAC7C,KAAK,MAAM,eAAe,QAAQ;AAClC,iBAAK,QAAQ,QAAQ;AAAA,UACzB;AAAA,QACJ,OACK;AAED,eAAK,QAAQ,OAAO;AAAA,QACxB;AACA,2BAAmB;AACnB,gBAAQ,IAAI,gBAAgB,MAAM,QAAQ,MAAM,KAAK;AACrD,2BAAmB;AAAA,MACvB;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,mBAAmB,CAAC,YAAY;AACnC,UAAI,QAAQ,KAAK,WAAW,WAAI,GAAG;AAC/B,cAAM,UAAU,QAAQ,KAAK,QAAQ,UAAU,EAAE;AACjD,cAAM,QAAQ,MAAM,GAAG,IAAI,OAAO;AAClC,YAAI,CAAC,OAAO;AACR,iBAAO,aAAa,UAAU,sBAAsB,OAAO;AAAA,QAC/D;AACA,cAAM,EAAE,KAAK,IAAI;AACjB,YAAI,KAAK,OAAO,SAAS;AACrB,iBAAO,aAAa,2BAA2B;AAAA,EAAc;AAAA,4BAAmC;AAAA,QACpG;AAGA,aAAK,KAAK;AACV,2BAAmB;AACnB,gBAAQ,IAAI,OAAO,MAAM,QAAQ,MAAM,KAAK;AAC5C,2BAAmB;AAAA,MACvB;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACA,SAAS,mBAAmB,KAAK,OAAO;AACpC,MAAI,CAAC,oBAAoB,SAAS,aAAa,MAAM,GAAG,CAAC,GAAG;AACxD,wBAAoB,KAAK,aAAa,MAAM,GAAG,CAAC;AAAA,EACpD;AACA,sBAAoB;AAAA,IAChB,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACN,iBAAiB;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,cAAc;AAAA,MAClB;AAAA,IAMJ;AAAA,EACJ,GAAG,CAAC,QAAQ;AAER,UAAM,MAAM,OAAO,IAAI,QAAQ,aAAa,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK;AACrE,UAAM,UAAU,CAAC,EAAE,OAAO,SAAS,MAAM,KAAK,MAAM;AAChD,YAAM,UAAU;AAChB,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,MAAM,IAAI;AAAA,UACV,OAAO,eAAQ;AAAA,UACf,UAAU;AAAA,UACV,MAAM;AAAA,YACF,OAAO,cAAc,MAAM,GAAG;AAAA,YAC9B,QAAQ,cAAc,IAAI;AAAA,YAC1B;AAAA,UACJ;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,YAAM,CAAC,WAAW;AACd,uBAAe;AACf,YAAI,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,YACH,MAAM,IAAI;AAAA,YACV,OAAO,eAAQ;AAAA,YACf,UAAU;AAAA,YACV,MAAM;AAAA,cACF,OAAO,cAAc,MAAM,GAAG;AAAA,cAC9B,QAAQ,cAAc,IAAI;AAAA,cAC1B;AAAA,cACA;AAAA,YACJ;AAAA,YACA;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AACD,cAAQ,CAAC,UAAU;AACf,uBAAe;AACf,YAAI,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,YACH,MAAM,IAAI;AAAA,YACV,SAAS;AAAA,YACT,OAAO,eAAQ;AAAA,YACf,UAAU;AAAA,YACV,MAAM;AAAA,cACF,OAAO,cAAc,MAAM,GAAG;AAAA,cAC9B,QAAQ,cAAc,IAAI;AAAA,cAC1B;AAAA,cACA;AAAA,YACJ;AAAA,YACA;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL,GAAG,IAAI;AACP,UAAM,kBAAkB,QAAQ,CAAC,SAAS;AACtC,YAAM,MAAM,MAAM,MAAM,KAAK,GAAG,CAAC,UAAU,aAAa;AACpD,YAAI,sBAAsB;AAC1B,YAAI,mBAAmB,YAAY;AACnC,YAAI,kBAAkB;AAClB,cAAI,iBAAiB;AAAA,YACjB,SAAS;AAAA,YACT,OAAO;AAAA,cACH,MAAM,IAAI;AAAA,cACV,OAAO;AAAA,cACP,UAAU;AAAA,cACV,MAAM;AAAA,gBACF;AAAA,gBACA;AAAA,cACJ;AAAA,cACA,SAAS;AAAA,YACb;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ,GAAG,EAAE,MAAM,KAAK,CAAC;AAAA,IACrB,CAAC;AACD,UAAM,WAAW,CAAC,EAAE,QAAQ,KAAK,GAAG,UAAU;AAC1C,UAAI,sBAAsB;AAC1B,UAAI,mBAAmB,YAAY;AACnC,UAAI,CAAC;AACD;AAEJ,YAAM,YAAY;AAAA,QACd,MAAM,IAAI;AAAA,QACV,OAAO,mBAAmB,IAAI;AAAA,QAC9B,MAAM;AAAA,UACF,OAAO,cAAc,MAAM,GAAG;AAAA,UAC9B,GAAG,gBAAgB,MAAM;AAAA,QAC7B;AAAA,QACA,SAAS;AAAA,MACb;AAEA,qBAAe;AACf,UAAI,SAAS,aAAa,eAAe;AACrC,kBAAU,WAAW;AAAA,MACzB,WACS,SAAS,aAAa,aAAa;AACxC,kBAAU,WAAW;AAAA,MACzB,WACS,UAAU,CAAC,MAAM,QAAQ,MAAM,GAAG;AACvC,kBAAU,WAAW,OAAO;AAAA,MAChC;AACA,UAAI,QAAQ;AACR,kBAAU,KAAK,iBAAiB;AAAA,UAC5B,SAAS;AAAA,YACL,SAAS;AAAA,YACT,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,MACX,CAAC;AAAA,IACL,GAAG,EAAE,UAAU,MAAM,OAAO,OAAO,CAAC;AACpC,UAAM,YAAY,MAAM;AACxB,UAAM,aAAa,QAAQ,CAAC,aAAa;AACrC,gBAAU,QAAQ;AAClB,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,MAAM,IAAI;AAAA,UACV,OAAO,eAAQ,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,MAAM;AAAA,YACF,OAAO,cAAc,MAAM,GAAG;AAAA,YAC9B,MAAM,cAAc,YAAY;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,UAAI,sBAAsB;AAC1B,UAAI,kBAAkB,YAAY;AAClC,UAAI,mBAAmB,YAAY;AAAA,IACvC,CAAC;AACD,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,WAAW,MAAM;AACnB,eAAS;AACT,UAAI,sBAAsB;AAC1B,UAAI,kBAAkB,YAAY;AAClC,UAAI,mBAAmB,YAAY;AACnC,UAAI,YAAY,EAAE,mBACd,aAAa,aAAa,MAAM,sBAAe;AAAA,IACvD;AAEA,QAAI,sBAAsB;AAC1B,QAAI,kBAAkB,YAAY;AAClC,QAAI,mBAAmB,YAAY;AACnC,QAAI,YAAY,EAAE,mBACd,aAAa,IAAI,MAAM,gCAAyB;AAAA,EACxD,CAAC;AACL;AACA,IAAI,kBAAkB;AACtB,IAAI;AASJ,SAAS,uBAAuB,OAAO,aAAa;AAEhD,QAAM,UAAU,YAAY,OAAO,CAAC,cAAc,eAAe;AAE7D,iBAAa,cAAc,MAAM,KAAK,EAAE;AACxC,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,aAAW,cAAc,SAAS;AAC9B,UAAM,cAAc,WAAY;AAG5B,YAAM,YAAY;AAClB,YAAM,eAAe,IAAI,MAAM,OAAO;AAAA,QAClC,OAAO,MAAM;AACT,yBAAe;AACf,iBAAO,QAAQ,IAAI,GAAG,IAAI;AAAA,QAC9B;AAAA,QACA,OAAO,MAAM;AACT,yBAAe;AACf,iBAAO,QAAQ,IAAI,GAAG,IAAI;AAAA,QAC9B;AAAA,MACJ,CAAC;AACD,aAAO,QAAQ,YAAY,MAAM,cAAc,SAAS;AAAA,IAC5D;AAAA,EACJ;AACJ;AAIA,SAAS,eAAe,EAAE,KAAK,OAAO,QAAQ,GAAG;AAE7C,MAAI,MAAM,IAAI,WAAW,QAAQ,GAAG;AAChC;AAAA,EACJ;AAEA,MAAI,QAAQ,OAAO;AACf,UAAM,gBAAgB;AAAA,EAC1B;AAGA,MAAI,OAAO,QAAQ,UAAU,YAAY;AACrC;AAAA,MAEA;AAAA,MAAO,OAAO,KAAK,QAAQ,OAAO;AAAA,IAAC;AACnC,UAAM,oBAAoB,MAAM;AAEhC,UAAM,KAAK,EAAE,aAAa,SAAU,UAAU;AAC1C,wBAAkB,MAAM,MAAM,SAAS;AACvC,6BAAuB,OAAO,OAAO,KAAK,SAAS,YAAY,OAAO,CAAC;AAAA,IAC3E;AAAA,EACJ;AACA;AAAA,IAAmB;AAAA,IAEnB;AAAA,EAAK;AACT;AAKA,SAAS,cAAc;AACnB,QAAM,QAAQ,YAAY,IAAI;AAG9B,QAAM,QAAQ,MAAM,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC;AACrC,MAAI,KAAK,CAAC;AAEV,MAAI,gBAAgB,CAAC;AACrB,QAAM,QAAQ,QAAQ;AAAA,IAClB,QAAQ,KAAK;AAGT,qBAAe,KAAK;AACpB,UAAI,CAAC,QAAQ;AACT,cAAM,KAAK;AACX,YAAI,QAAQ,aAAa,KAAK;AAC9B,YAAI,OAAO,iBAAiB,SAAS;AAErC,YAAI,cAAc;AACd,gCAAsB,KAAK,KAAK;AAAA,QACpC;AACA,sBAAc,QAAQ,CAAC,WAAW,GAAG,KAAK,MAAM,CAAC;AACjD,wBAAgB,CAAC;AAAA,MACrB;AAAA,IACJ;AAAA,IACA,IAAI,QAAQ;AACR,UAAI,CAAC,KAAK,MAAM,CAAC,QAAQ;AACrB,sBAAc,KAAK,MAAM;AAAA,MAC7B,OACK;AACD,WAAG,KAAK,MAAM;AAAA,MAClB;AACA,aAAO;AAAA,IACX;AAAA,IACA;AAAA,IAGA,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI,oBAAI,IAAI;AAAA,IACZ;AAAA,EACJ,CAAC;AAGD,MAAI,gBAAgB,OAAO,UAAU,aAAa;AAC9C,UAAM,IAAI,cAAc;AAAA,EAC5B;AACA,SAAO;AACX;AAQA,IAAM,aAAa,CAAC,OAAO;AACvB,SAAO,OAAO,OAAO,cAAc,OAAO,GAAG,QAAQ;AACzD;AAUA,SAAS,YAAY,UAAU,UAAU;AAErC,aAAW,OAAO,UAAU;AACxB,UAAM,WAAW,SAAS;AAE1B,QAAI,EAAE,OAAO,WAAW;AACpB;AAAA,IACJ;AACA,UAAM,cAAc,SAAS;AAC7B,QAAI,cAAc,WAAW,KACzB,cAAc,QAAQ,KACtB,CAAC,MAAM,QAAQ,KACf,CAAC,WAAW,QAAQ,GAAG;AACvB,eAAS,OAAO,YAAY,aAAa,QAAQ;AAAA,IACrD,OACK;AAGD,UAAI,QAAQ;AACR,YAAI,UAAU,KAAK,QAAQ;AAAA,MAC/B,OACK;AACD,iBAAS,OAAO;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAeA,SAAS,gBAAgB,iBAAiB,KAAK;AAE3C,MAAI,OAA0C;AAC1C,WAAO,MAAM;AAAA,IAAE;AAAA,EACnB;AACA,SAAO,CAAC,cAAc;AAClB,UAAM,QAAQ,IAAI,KAAK,SAAS,gBAAgB;AAChD,QAAI,CAAC,OAAO;AAER;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ;AAEjB,eAAW,cAAc,WAAW;AAChC,YAAM,WAAW,UAAU;AAE3B,UAAI,WAAW,QAAQ,KAAK,MAAM,GAAG,IAAI,SAAS,GAAG,GAAG;AAEpD,cAAM,KAAK,SAAS;AACpB,YAAI,OAAO,gBAAgB,KAAK;AAC5B,kBAAQ,KAAK,qCAAqC,gBAAgB,YAAY,iBAAiB;AAE/F,iBAAO,IAAI,WAAW;AAAA,QAC1B;AACA,cAAM,gBAAgB,MAAM,GAAG,IAAI,EAAE;AACrC,YAAI,CAAC,eAAe;AAChB,kBAAQ,IAAI,uDAAuD;AACnE;AAAA,QACJ;AACA,iBAAS,OAAO,aAAa;AAAA,MACjC;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAM,OAAO,MAAM;AAAE;AACrB,SAAS,gBAAgB,eAAe,UAAU,UAAU,YAAY,MAAM;AAC1E,gBAAc,KAAK,QAAQ;AAC3B,QAAM,qBAAqB,MAAM;AAC7B,UAAM,MAAM,cAAc,QAAQ,QAAQ;AAC1C,QAAI,MAAM,IAAI;AACV,oBAAc,OAAO,KAAK,CAAC;AAC3B,gBAAU;AAAA,IACd;AAAA,EACJ;AACA,MAAI,CAAC,YAAY,gBAAgB,GAAG;AAChC,mBAAe,kBAAkB;AAAA,EACrC;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,kBAAkB,MAAM;AAClD,gBAAc,MAAM,EAAE,QAAQ,CAAC,aAAa;AACxC,aAAS,GAAG,IAAI;AAAA,EACpB,CAAC;AACL;AAEA,SAAS,qBAAqB,QAAQ,cAAc;AAEhD,MAAI,kBAAkB,OAAO,wBAAwB,KAAK;AACtD,iBAAa,QAAQ,CAAC,OAAO,QAAQ,OAAO,IAAI,KAAK,KAAK,CAAC;AAAA,EAC/D;AAEA,MAAI,kBAAkB,OAAO,wBAAwB,KAAK;AACtD,iBAAa,QAAQ,OAAO,KAAK,MAAM;AAAA,EAC3C;AAEA,aAAW,OAAO,cAAc;AAC5B,QAAI,CAAC,aAAa,eAAe,GAAG;AAChC;AACJ,UAAM,WAAW,aAAa;AAC9B,UAAM,cAAc,OAAO;AAC3B,QAAI,cAAc,WAAW,KACzB,cAAc,QAAQ,KACtB,OAAO,eAAe,GAAG,KACzB,CAAC,MAAM,QAAQ,KACf,CAAC,WAAW,QAAQ,GAAG;AAIvB,aAAO,OAAO,qBAAqB,aAAa,QAAQ;AAAA,IAC5D,OACK;AAED,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,oBAAqB,OACrB,OAAO,qBAAqB,IACD,OAAO;AACxC,IAAM,iBAA+B,oBAAI,QAAQ;AAQjD,SAAS,YAAY,KAAK;AACtB,SAAO,SAE4B,eAAe,IAAI,KAAK,CAAC,KAAK,MAC3D,OAAO,eAAe,KAAK,mBAAmB,CAAC,CAAC;AAC1D;AAOA,SAAS,cAAc,KAAK;AACxB,SAAO,SAC0B,CAAC,eAAe,IAAI,GAAG,IAClD,CAAC,cAAc,GAAG,KAAK,CAAC,IAAI,eAAe,iBAAiB;AACtE;AACA,IAAM,EAAE,OAAO,IAAI;AACnB,SAAS,WAAW,GAAG;AACnB,SAAO,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE;AAC5B;AACA,SAAS,mBAAmB,IAAI,SAAS,OAAO,KAAK;AACjD,QAAM,EAAE,OAAO,SAAS,QAAQ,IAAI;AACpC,QAAM,eAAe,MAAM,MAAM,MAAM;AACvC,MAAI;AACJ,WAAS,QAAQ;AACb,QAAI,CAAC,gBAA6D,CAAC,KAAM;AAErE,UAAI,QAAQ;AACR,YAAI,MAAM,MAAM,OAAO,IAAI,QAAQ,MAAM,IAAI,CAAC,CAAC;AAAA,MACnD,OACK;AACD,cAAM,MAAM,MAAM,MAAM,QAAQ,MAAM,IAAI,CAAC;AAAA,MAC/C;AAAA,IACJ;AAEA,UAAM,aAAwD,MAEtD,OAAO,IAAI,QAAQ,MAAM,IAAI,CAAC,CAAC,EAAE,KAAK,IACxC,OAAO,MAAM,MAAM,MAAM,GAAG;AAClC,WAAO,OAAO,YAAY,SAAS,OAAO,KAAK,WAAW,CAAC,CAAC,EAAE,OAAO,CAAC,iBAAiB,SAAS;AAC5F,UAA+C,QAAQ,YAAY;AAC/D,gBAAQ,KAAK,8GAAuG,mBAAmB,MAAM;AAAA,MACjJ;AACA,sBAAgB,QAAQ,QAAQ,SAAS,MAAM;AAC3C,uBAAe,KAAK;AAEpB,cAAMC,SAAQ,MAAM,GAAG,IAAI,EAAE;AAG7B,YAAI,UAAU,CAACA,OAAM;AACjB;AAIJ,eAAO,QAAQ,MAAM,KAAKA,QAAOA,MAAK;AAAA,MAC1C,CAAC,CAAC;AACF,aAAO;AAAA,IACX,GAAG,CAAC,CAAC,CAAC;AAAA,EACV;AACA,UAAQ,iBAAiB,IAAI,OAAO,SAAS,OAAO,KAAK,IAAI;AAC7D,QAAM,SAAS,SAAS,SAAS;AAC7B,UAAM,WAAW,QAAQ,MAAM,IAAI,CAAC;AAEpC,SAAK,OAAO,CAAC,WAAW;AACpB,aAAO,QAAQ,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,KAAK,OAAO,UAAU,CAAC,GAAG,OAAO,KAAK,gBAAgB;AAC5E,MAAI;AACJ,QAAM,mBAAmB,OAAO,EAAE,SAAS,CAAC,EAAE,GAAG,OAAO;AAGxD,MAA+C,CAAC,MAAM,GAAG,QAAQ;AAC7D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACrC;AAEA,QAAM,oBAAoB;AAAA,IACtB,MAAM;AAAA,EAEV;AAEA,MAA+C,CAAC,QAAQ;AACpD,sBAAkB,YAAY,CAAC,UAAU;AAErC,UAAI,aAAa;AACb,yBAAiB;AAAA,MAErB,WACS,eAAe,SAAS,CAAC,MAAM,cAAc;AAGlD,YAAI,MAAM,QAAQ,cAAc,GAAG;AAC/B,yBAAe,KAAK,KAAK;AAAA,QAC7B,OACK;AACD,kBAAQ,MAAM,yFAAkF;AAAA,QACpG;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI;AACJ,MAAI;AACJ,MAAI,gBAAgB,QAAQ,CAAC,CAAC;AAC9B,MAAI,sBAAsB,QAAQ,CAAC,CAAC;AACpC,MAAI;AACJ,QAAM,eAAe,MAAM,MAAM,MAAM;AAGvC,MAAI,CAAC,kBAAkB,CAAC,gBAA6D,CAAC,KAAM;AAExF,QAAI,QAAQ;AACR,UAAI,MAAM,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,IAClC,OACK;AACD,YAAM,MAAM,MAAM,OAAO,CAAC;AAAA,IAC9B;AAAA,EACJ;AACA,QAAM,WAAW,IAAI,CAAC,CAAC;AAGvB,MAAI;AACJ,WAAS,OAAO,uBAAuB;AACnC,QAAI;AACJ,kBAAc,kBAAkB;AAGhC,QAAK,MAAwC;AACzC,uBAAiB,CAAC;AAAA,IACtB;AACA,QAAI,OAAO,0BAA0B,YAAY;AAC7C,4BAAsB,MAAM,MAAM,MAAM,IAAI;AAC5C,6BAAuB;AAAA,QACnB,MAAM,aAAa;AAAA,QACnB,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ;AAAA,IACJ,OACK;AACD,2BAAqB,MAAM,MAAM,MAAM,MAAM,qBAAqB;AAClE,6BAAuB;AAAA,QACnB,MAAM,aAAa;AAAA,QACnB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ;AAAA,IACJ;AACA,UAAM,eAAgB,iBAAiB,OAAO;AAC9C,aAAS,EAAE,KAAK,MAAM;AAClB,UAAI,mBAAmB,cAAc;AACjC,sBAAc;AAAA,MAClB;AAAA,IACJ,CAAC;AACD,sBAAkB;AAElB,yBAAqB,eAAe,sBAAsB,MAAM,MAAM,MAAM,IAAI;AAAA,EACpF;AAEA,QAAM,SAAU,OACV,MAAM;AACJ,UAAM,IAAI,MAAM,qBAAc,uEAAuE;AAAA,EACzG,IACE;AACN,WAAS,WAAW;AAChB,UAAM,KAAK;AACX,oBAAgB,CAAC;AACjB,0BAAsB,CAAC;AACvB,UAAM,GAAG,OAAO,GAAG;AAAA,EACvB;AAQA,WAAS,WAAW,MAAM,QAAQ;AAC9B,WAAO,WAAY;AACf,qBAAe,KAAK;AACpB,YAAM,OAAO,MAAM,KAAK,SAAS;AACjC,YAAM,oBAAoB,CAAC;AAC3B,YAAM,sBAAsB,CAAC;AAC7B,eAAS,MAAM,UAAU;AACrB,0BAAkB,KAAK,QAAQ;AAAA,MACnC;AACA,eAAS,QAAQ,UAAU;AACvB,4BAAoB,KAAK,QAAQ;AAAA,MACrC;AAEA,2BAAqB,qBAAqB;AAAA,QACtC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,UAAI;AACJ,UAAI;AACA,cAAM,OAAO,MAAM,QAAQ,KAAK,QAAQ,MAAM,OAAO,OAAO,IAAI;AAAA,MAEpE,SACO,OAAP;AACI,6BAAqB,qBAAqB,KAAK;AAC/C,cAAM;AAAA,MACV;AACA,UAAI,eAAe,SAAS;AACxB,eAAO,IACF,KAAK,CAAC,UAAU;AACjB,+BAAqB,mBAAmB,KAAK;AAC7C,iBAAO;AAAA,QACX,CAAC,EACI,MAAM,CAAC,UAAU;AAClB,+BAAqB,qBAAqB,KAAK;AAC/C,iBAAO,QAAQ,OAAO,KAAK;AAAA,QAC/B,CAAC;AAAA,MACL;AAEA,2BAAqB,mBAAmB,GAAG;AAC3C,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,cAA4B,QAAQ;AAAA,IACtC,SAAS,CAAC;AAAA,IACV,SAAS,CAAC;AAAA,IACV,OAAO,CAAC;AAAA,IACR;AAAA,EACJ,CAAC;AACD,QAAM,eAAe;AAAA,IACjB,IAAI;AAAA,IAEJ;AAAA,IACA,WAAW,gBAAgB,KAAK,MAAM,mBAAmB;AAAA,IACzD;AAAA,IACA;AAAA,IACA,WAAW,UAAUC,WAAU,CAAC,GAAG;AAC/B,YAAM,qBAAqB,gBAAgB,eAAe,UAAUA,SAAQ,UAAU,MAAM,YAAY,CAAC;AACzG,YAAM,cAAc,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,CAAC,UAAU;AAC/E,YAAIA,SAAQ,UAAU,SAAS,kBAAkB,aAAa;AAC1D,mBAAS;AAAA,YACL,SAAS;AAAA,YACT,MAAM,aAAa;AAAA,YACnB,QAAQ;AAAA,UACZ,GAAG,KAAK;AAAA,QACZ;AAAA,MACJ,GAAG,OAAO,CAAC,GAAG,mBAAmBA,QAAO,CAAC,CAAC;AAC1C,aAAO;AAAA,IACX;AAAA,IACA;AAAA,EACJ;AAEA,MAAI,QAAQ;AAER,iBAAa,KAAK;AAAA,EACtB;AACA,QAAM,QAAQ,SAAU,OAClB;AAAA,IAAO;AAAA,MACL;AAAA,MACA,mBAAmB,QAAQ,oBAAI,IAAI,CAAC;AAAA,IACxC;AAAA,IAAG;AAAA,EAGH,IACE,YAAY;AAGlB,QAAM,GAAG,IAAI,KAAK,KAAK;AAEvB,QAAM,aAAa,MAAM,GAAG,IAAI,MAAM;AAClC,YAAQ,YAAY;AACpB,WAAO,MAAM,IAAI,MAAM,MAAM,CAAC;AAAA,EAClC,CAAC;AAED,aAAW,OAAO,YAAY;AAC1B,UAAM,OAAO,WAAW;AACxB,QAAK,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,KAAM,WAAW,IAAI,GAAG;AAExD,UAA+C,KAAK;AAChD,YAAI,SAAS,OAAO,KAAK,MAAM,YAAY,GAAG,CAAC;AAAA,MAGnD,WACS,CAAC,gBAAgB;AAEtB,YAAI,gBAAgB,cAAc,IAAI,GAAG;AACrC,cAAI,MAAM,IAAI,GAAG;AACb,iBAAK,QAAQ,aAAa;AAAA,UAC9B,OACK;AAGD,iCAAqB,MAAM,aAAa,IAAI;AAAA,UAChD;AAAA,QACJ;AAGA,YAAI,QAAQ;AACR,cAAI,MAAM,MAAM,MAAM,MAAM,KAAK,IAAI;AAAA,QACzC,OACK;AACD,gBAAM,MAAM,MAAM,KAAK,OAAO;AAAA,QAClC;AAAA,MACJ;AAEA,UAAK,MAAwC;AACzC,oBAAY,MAAM,KAAK,GAAG;AAAA,MAC9B;AAAA,IAEJ,WACS,OAAO,SAAS,YAAY;AAEjC,YAAM,cAAyD,MAAM,OAAO,WAAW,KAAK,IAAI;AAIhG,UAAI,QAAQ;AACR,YAAI,YAAY,KAAK,WAAW;AAAA,MACpC,OACK;AAED,mBAAW,OAAO;AAAA,MACtB;AAEA,UAAK,MAAwC;AACzC,oBAAY,QAAQ,OAAO;AAAA,MAC/B;AAGA,uBAAiB,QAAQ,OAAO;AAAA,IACpC,WACU,MAAwC;AAE9C,UAAI,WAAW,IAAI,GAAG;AAClB,oBAAY,QAAQ,OAAO,iBAEnB,QAAQ,QAAQ,OAClB;AACN,YAAI,WAAW;AACX,gBAAM,UAAU,WAAW,aAEtB,WAAW,WAAW,QAAQ,CAAC,CAAC;AACrC,kBAAQ,KAAK,GAAG;AAAA,QACpB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAGA,MAAI,QAAQ;AACR,WAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ;AACrC,UAAI,OAAO,KAAK,WAAW,IAAI;AAAA,IACnC,CAAC;AAAA,EACL,OACK;AACD,WAAO,OAAO,UAAU;AAGxB,WAAO,MAAM,KAAK,GAAG,UAAU;AAAA,EACnC;AAIA,SAAO,eAAe,OAAO,UAAU;AAAA,IACnC,KAAK,MAAkD,MAAM,SAAS,QAAQ,MAAM,MAAM,MAAM;AAAA,IAChG,KAAK,CAAC,UAAU;AAEZ,UAA+C,KAAK;AAChD,cAAM,IAAI,MAAM,qBAAqB;AAAA,MACzC;AACA,aAAO,CAAC,WAAW;AACf,eAAO,QAAQ,KAAK;AAAA,MACxB,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAGD,MAAK,MAAwC;AACzC,UAAM,aAAa,QAAQ,CAAC,aAAa;AACrC,YAAM,eAAe;AACrB,eAAS,YAAY,MAAM,QAAQ,CAAC,aAAa;AAC7C,YAAI,YAAY,MAAM,QAAQ;AAC1B,gBAAM,iBAAiB,SAAS,OAAO;AACvC,gBAAM,iBAAiB,MAAM,OAAO;AACpC,cAAI,OAAO,mBAAmB,YAC1B,cAAc,cAAc,KAC5B,cAAc,cAAc,GAAG;AAC/B,wBAAY,gBAAgB,cAAc;AAAA,UAC9C,OACK;AAED,qBAAS,OAAO,YAAY;AAAA,UAChC;AAAA,QACJ;AAGA,YAAI,OAAO,UAAU,MAAM,SAAS,QAAQ,QAAQ,CAAC;AAAA,MACzD,CAAC;AAED,aAAO,KAAK,MAAM,MAAM,EAAE,QAAQ,CAAC,aAAa;AAC5C,YAAI,EAAE,YAAY,SAAS,SAAS;AAChC,cAAI,OAAO,QAAQ;AAAA,QACvB;AAAA,MACJ,CAAC;AAED,oBAAc;AACd,wBAAkB;AAClB,YAAM,MAAM,MAAM,OAAO,MAAM,SAAS,aAAa,UAAU;AAC/D,wBAAkB;AAClB,eAAS,EAAE,KAAK,MAAM;AAClB,sBAAc;AAAA,MAClB,CAAC;AACD,iBAAW,cAAc,SAAS,YAAY,SAAS;AACnD,cAAM,SAAS,SAAS;AACxB,YAAI,OAAO,YAAY,WAAW,YAAY,MAAM,CAAC;AAAA,MACzD;AAEA,iBAAW,cAAc,SAAS,YAAY,SAAS;AACnD,cAAM,SAAS,SAAS,YAAY,QAAQ;AAC5C,cAAM,cAAc,iBAEZ,SAAS,MAAM;AACX,yBAAe,KAAK;AACpB,iBAAO,OAAO,KAAK,OAAO,KAAK;AAAA,QACnC,CAAC,IACH;AACN,YAAI,OAAO,YAAY,WAAW;AAAA,MACtC;AAEA,aAAO,KAAK,MAAM,YAAY,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpD,YAAI,EAAE,OAAO,SAAS,YAAY,UAAU;AACxC,cAAI,OAAO,GAAG;AAAA,QAClB;AAAA,MACJ,CAAC;AAED,aAAO,KAAK,MAAM,YAAY,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpD,YAAI,EAAE,OAAO,SAAS,YAAY,UAAU;AACxC,cAAI,OAAO,GAAG;AAAA,QAClB;AAAA,MACJ,CAAC;AAED,YAAM,cAAc,SAAS;AAC7B,YAAM,WAAW,SAAS;AAC1B,YAAM,eAAe;AAAA,IACzB,CAAC;AAAA,EACL;AACA,MAAI,cAAc;AACd,UAAM,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,cAAc;AAAA,MAEd,YAAY;AAAA,IAChB;AACA,KAAC,MAAM,eAAe,YAAY,mBAAmB,EAAE,QAAQ,CAAC,MAAM;AAClE,aAAO,eAAe,OAAO,GAAG;AAAA,QAC5B,OAAO,MAAM;AAAA,QACb,GAAG;AAAA,MACP,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAEA,MAAI,QAAQ;AAER,UAAM,KAAK;AAAA,EACf;AAEA,QAAM,GAAG,QAAQ,CAAC,aAAa;AAE3B,QAAI,cAAc;AACd,YAAM,aAAa,MAAM,IAAI,MAAM,SAAS;AAAA,QACxC;AAAA,QACA,KAAK,MAAM;AAAA,QACX;AAAA,QACA,SAAS;AAAA,MACb,CAAC,CAAC;AACF,aAAO,KAAK,cAAc,CAAC,CAAC,EAAE,QAAQ,CAAC,QAAQ,MAAM,kBAAkB,IAAI,GAAG,CAAC;AAC/E,aAAO,OAAO,UAAU;AAAA,IAC5B,OACK;AACD,aAAO,OAAO,MAAM,IAAI,MAAM,SAAS;AAAA,QACnC;AAAA,QACA,KAAK,MAAM;AAAA,QACX;AAAA,QACA,SAAS;AAAA,MACb,CAAC,CAAC,CAAC;AAAA,IACP;AAAA,EACJ,CAAC;AACD,MACI,MAAM,UACN,OAAO,MAAM,WAAW,YACxB,OAAO,MAAM,OAAO,gBAAgB,cACpC,CAAC,MAAM,OAAO,YAAY,SAAS,EAAE,SAAS,eAAe,GAAG;AAChE,YAAQ,KAAK;AAAA;AAAA,kBAEU,MAAM,OAAO;AAAA,EACxC;AAEA,MAAI,gBACA,kBACA,QAAQ,SAAS;AACjB,YAAQ,QAAQ,MAAM,QAAQ,YAAY;AAAA,EAC9C;AACA,gBAAc;AACd,oBAAkB;AAClB,SAAO;AACX;AACA,SAAS,YAET,aAAa,OAAO,cAAc;AAC9B,MAAI;AACJ,MAAI;AACJ,QAAM,eAAe,OAAO,UAAU;AACtC,MAAI,OAAO,gBAAgB,UAAU;AACjC,SAAK;AAEL,cAAU,eAAe,eAAe;AAAA,EAC5C,OACK;AACD,cAAU;AACV,SAAK,YAAY;AAAA,EACrB;AACA,WAAS,SAAS,OAAO,KAAK;AAC1B,UAAM,kBAAkB,mBAAmB;AAC3C,aAGM,QAA0E,OAAO,UAC9E,mBAAmB,OAAO,WAAW;AAC9C,QAAI;AACA,qBAAe,KAAK;AACxB,QAA+C,CAAC,aAAa;AACzD,YAAM,IAAI,MAAM;AAAA;AAAA;AAAA,8BAGmB;AAAA,IACvC;AACA,YAAQ;AACR,QAAI,CAAC,MAAM,GAAG,IAAI,EAAE,GAAG;AAEnB,UAAI,cAAc;AACd,yBAAiB,IAAI,OAAO,SAAS,KAAK;AAAA,MAC9C,OACK;AACD,2BAAmB,IAAI,SAAS,KAAK;AAAA,MACzC;AAEA,UAAK,MAAwC;AAEzC,iBAAS,SAAS;AAAA,MACtB;AAAA,IACJ;AACA,UAAM,QAAQ,MAAM,GAAG,IAAI,EAAE;AAC7B,QAA+C,KAAK;AAChD,YAAM,QAAQ,WAAW;AACzB,YAAM,WAAW,eACX,iBAAiB,OAAO,OAAO,SAAS,OAAO,IAAI,IACnD,mBAAmB,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,OAAO,IAAI;AAChE,UAAI,WAAW,QAAQ;AAEvB,aAAO,MAAM,MAAM,MAAM;AACzB,YAAM,GAAG,OAAO,KAAK;AAAA,IACzB;AAEA,QACI,aACA,mBACA,gBAAgB,SAEhB,CAAC,KAAK;AACN,YAAM,KAAK,gBAAgB;AAC3B,YAAM,QAAQ,cAAc,KAAK,GAAG,WAAY,GAAG,WAAW,CAAC;AAC/D,YAAM,MAAM;AAAA,IAChB;AAEA,WAAO;AAAA,EACX;AACA,WAAS,MAAM;AACf,SAAO;AACX;AAEA,IAAI,iBAAiB;AAQrB,SAAS,kBAAkB,QACzB;AACE,mBAAiB;AACrB;AAuBA,SAAS,aAAa,QAAQ;AAC1B,MAA+C,MAAM,QAAQ,OAAO,EAAE,GAAG;AACrE,YAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,2CAKmC;AAChD,aAAS,OAAO;AAAA,EACpB;AACA,SAAO,OAAO,OAAO,CAAC,SAAS,aAAa;AAExC,YAAQ,SAAS,MAAM,kBAAkB,WAAY;AACjD,aAAO,SAAS,KAAK,MAAM;AAAA,IAC/B;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AASA,SAAS,SAAS,UAAU,cAAc;AACtC,SAAO,MAAM,QAAQ,YAAY,IAC3B,aAAa,OAAO,CAAC,SAAS,QAAQ;AACpC,YAAQ,OAAO,WAAY;AACvB,aAAO,SAAS,KAAK,MAAM,EAAE;AAAA,IACjC;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,IACH,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,SAAS,QAAQ;AAEjD,YAAQ,OAAO,WAAY;AACvB,YAAM,QAAQ,SAAS,KAAK,MAAM;AAClC,YAAM,WAAW,aAAa;AAG9B,aAAO,OAAO,aAAa,aACrB,SAAS,KAAK,MAAM,KAAK,IACzB,MAAM;AAAA,IAChB;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACb;AAKA,IAAM,aAAa;AASnB,SAAS,WAAW,UAAU,cAAc;AACxC,SAAO,MAAM,QAAQ,YAAY,IAC3B,aAAa,OAAO,CAAC,SAAS,QAAQ;AAEpC,YAAQ,OAAO,YAAa,MAAM;AAC9B,aAAO,SAAS,KAAK,MAAM,EAAE,KAAK,GAAG,IAAI;AAAA,IAC7C;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,IACH,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,SAAS,QAAQ;AAEjD,YAAQ,OAAO,YAAa,MAAM;AAC9B,aAAO,SAAS,KAAK,MAAM,EAAE,aAAa,MAAM,GAAG,IAAI;AAAA,IAC3D;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACb;AASA,SAAS,iBAAiB,UAAU,cAAc;AAC9C,SAAO,MAAM,QAAQ,YAAY,IAC3B,aAAa,OAAO,CAAC,SAAS,QAAQ;AAEpC,YAAQ,OAAO;AAAA,MACX,MAAM;AACF,eAAO,SAAS,KAAK,MAAM,EAAE;AAAA,MACjC;AAAA,MACA,IAAI,OAAO;AAEP,eAAQ,SAAS,KAAK,MAAM,EAAE,OAAO;AAAA,MACzC;AAAA,IACJ;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,IACH,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,SAAS,QAAQ;AAEjD,YAAQ,OAAO;AAAA,MACX,MAAM;AACF,eAAO,SAAS,KAAK,MAAM,EAAE,aAAa;AAAA,MAC9C;AAAA,MACA,IAAI,OAAO;AAEP,eAAQ,SAAS,KAAK,MAAM,EAAE,aAAa,QAAQ;AAAA,MACvD;AAAA,IACJ;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACb;AAUA,SAAS,YAAY,OAAO;AAGxB,MAAI,QAAQ;AAER,WAAO,OAAO,KAAK;AAAA,EACvB,OACK;AACD,YAAQ,MAAM,KAAK;AACnB,UAAM,OAAO,CAAC;AACd,eAAW,OAAO,OAAO;AACrB,YAAM,QAAQ,MAAM;AACpB,UAAI,MAAM,KAAK,KAAK,WAAW,KAAK,GAAG;AAEnC,aAAK,OAED,MAAM,OAAO,GAAG;AAAA,MACxB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAwBA,IAAM,iBAAiB,SAAU,MAAM;AAGnC,OAAK,MAAM;AAAA,IACP,eAAe;AACX,YAAM,UAAU,KAAK;AACrB,UAAI,QAAQ,OAAO;AACf,cAAM,QAAQ,QAAQ;AAGtB,YAAI,CAAC,KAAK,WAAW;AACjB,gBAAM,eAAe,CAAC;AACtB,iBAAO,eAAe,MAAM,aAAa;AAAA,YACrC,KAAK,MAAM;AAAA,YACX,KAAK,CAAC,MAAM,OAAO,OAAO,cAAc,CAAC;AAAA,UAC7C,CAAC;AAAA,QACL;AACA,aAAK,UAAU,eAAe;AAI9B,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,SAAS;AAAA,QAClB;AACA,cAAM,KAAK;AACX,YAAI,WAAW;AAGX,yBAAe,KAAK;AAAA,QACxB;AACA,YAAI,cAAc;AACd,gCAAsB,MAAM,IAAI,KAAK;AAAA,QACzC;AAAA,MACJ,WACS,CAAC,KAAK,UAAU,QAAQ,UAAU,QAAQ,OAAO,QAAQ;AAC9D,aAAK,SAAS,QAAQ,OAAO;AAAA,MACjC;AAAA,IACJ;AAAA,IACA,YAAY;AACR,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,CAAC;AACL;", "names": ["MutationType", "open", "state", "store", "options"]}
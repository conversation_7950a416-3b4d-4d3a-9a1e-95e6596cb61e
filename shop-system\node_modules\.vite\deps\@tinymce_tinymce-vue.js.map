{"version": 3, "sources": ["../../@tinymce/tinymce-vue/lib/es2015/main/ts/Utils.js", "../../@tinymce/tinymce-vue/lib/es2015/main/ts/ScriptLoader.js", "../../@tinymce/tinymce-vue/lib/es2015/main/ts/TinyMCE.js", "../../@tinymce/tinymce-vue/lib/es2015/main/ts/components/EditorPropTypes.js", "../../@tinymce/tinymce-vue/lib/es2015/main/ts/components/Editor.js", "../../@tinymce/tinymce-vue/lib/es2015/main/ts/index.js"], "sourcesContent": ["/**\n * Copyright (c) 2018-present, Ephox, Inc.\n *\n * This source code is licensed under the Apache 2 license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport { watch } from 'vue';\nvar validEvents = [\n    'onActivate',\n    'onAddUndo',\n    'onBeforeAddUndo',\n    'onBeforeExecCommand',\n    'onBeforeGetContent',\n    'onBeforeRenderUI',\n    'onBeforeSetContent',\n    'onBeforePaste',\n    'onBlur',\n    'onChange',\n    'onClearUndos',\n    'onClick',\n    'onContextMenu',\n    'onCopy',\n    'onCut',\n    'onDblclick',\n    'onDeactivate',\n    'onDirty',\n    'onDrag',\n    'onDragDrop',\n    'onDragEnd',\n    'onDragGesture',\n    'onDragOver',\n    'onDrop',\n    'onExecCommand',\n    'onFocus',\n    'onFocusIn',\n    'onFocusOut',\n    'onGetContent',\n    'onHide',\n    'onInit',\n    'onKeyDown',\n    'onKeyPress',\n    'onKeyUp',\n    'onLoadContent',\n    'onMouseDown',\n    'onMouseEnter',\n    'onMouseLeave',\n    'onMouseMove',\n    'onMouseOut',\n    'onMouseOver',\n    'onMouseUp',\n    'onNodeChange',\n    'onObjectResizeStart',\n    'onObjectResized',\n    'onObjectSelected',\n    'onPaste',\n    'onPostProcess',\n    'onPostRender',\n    'onPreProcess',\n    'onProgressState',\n    'onRedo',\n    'onRemove',\n    'onReset',\n    'onSaveContent',\n    'onSelectionChange',\n    'onSetAttrib',\n    'onSetContent',\n    'onShow',\n    'onSubmit',\n    'onUndo',\n    'onVisualAid'\n];\nvar isValidKey = function (key) {\n    return validEvents.map(function (event) { return event.toLowerCase(); }).indexOf(key.toLowerCase()) !== -1;\n};\nvar bindHandlers = function (initEvent, listeners, editor) {\n    Object.keys(listeners)\n        .filter(isValidKey)\n        .forEach(function (key) {\n        var handler = listeners[key];\n        if (typeof handler === 'function') {\n            if (key === 'onInit') {\n                handler(initEvent, editor);\n            }\n            else {\n                editor.on(key.substring(2), function (e) { return handler(e, editor); });\n            }\n        }\n    });\n};\nvar bindModelHandlers = function (props, ctx, editor, modelValue) {\n    var modelEvents = props.modelEvents ? props.modelEvents : null;\n    var normalizedEvents = Array.isArray(modelEvents) ? modelEvents.join(' ') : modelEvents;\n    watch(modelValue, function (val, prevVal) {\n        if (editor && typeof val === 'string' && val !== prevVal && val !== editor.getContent({ format: props.outputFormat })) {\n            editor.setContent(val);\n        }\n    });\n    editor.on(normalizedEvents ? normalizedEvents : 'change input undo redo', function () {\n        ctx.emit('update:modelValue', editor.getContent({ format: props.outputFormat }));\n    });\n};\nvar initEditor = function (initEvent, props, ctx, editor, modelValue, content) {\n    editor.setContent(content());\n    if (ctx.attrs['onUpdate:modelValue']) {\n        bindModelHandlers(props, ctx, editor, modelValue);\n    }\n    bindHandlers(initEvent, ctx.attrs, editor);\n};\nvar unique = 0;\nvar uuid = function (prefix) {\n    var time = Date.now();\n    var random = Math.floor(Math.random() * 1000000000);\n    unique++;\n    return prefix + '_' + random + unique + String(time);\n};\nvar isTextarea = function (element) {\n    return element !== null && element.tagName.toLowerCase() === 'textarea';\n};\nvar normalizePluginArray = function (plugins) {\n    if (typeof plugins === 'undefined' || plugins === '') {\n        return [];\n    }\n    return Array.isArray(plugins) ? plugins : plugins.split(' ');\n};\nvar mergePlugins = function (initPlugins, inputPlugins) {\n    return normalizePluginArray(initPlugins).concat(normalizePluginArray(inputPlugins));\n};\nvar isNullOrUndefined = function (value) {\n    return value === null || value === undefined;\n};\nexport { bindHandlers, bindModelHandlers, initEditor, isValidKey, uuid, isTextarea, mergePlugins, isNullOrUndefined };\n", "/**\n * Copyright (c) 2018-present, Ephox, Inc.\n *\n * This source code is licensed under the Apache 2 license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport { uuid } from './Utils';\nvar createState = function () { return ({\n    listeners: [],\n    scriptId: uuid('tiny-script'),\n    scriptLoaded: false\n}); };\nvar CreateScriptLoader = function () {\n    var state = createState();\n    var injectScriptTag = function (scriptId, doc, url, callback) {\n        var scriptTag = doc.createElement('script');\n        scriptTag.referrerPolicy = 'origin';\n        scriptTag.type = 'application/javascript';\n        scriptTag.id = scriptId;\n        scriptTag.src = url;\n        var handler = function () {\n            scriptTag.removeEventListener('load', handler);\n            callback();\n        };\n        scriptTag.addEventListener('load', handler);\n        if (doc.head) {\n            doc.head.appendChild(scriptTag);\n        }\n    };\n    var load = function (doc, url, callback) {\n        if (state.scriptLoaded) {\n            callback();\n        }\n        else {\n            state.listeners.push(callback);\n            if (!doc.getElementById(state.scriptId)) {\n                injectScriptTag(state.scriptId, doc, url, function () {\n                    state.listeners.forEach(function (fn) { return fn(); });\n                    state.scriptLoaded = true;\n                });\n            }\n        }\n    };\n    // Only to be used by tests.\n    var reinitialize = function () {\n        state = createState();\n    };\n    return {\n        load: load,\n        reinitialize: reinitialize\n    };\n};\nvar ScriptLoader = CreateScriptLoader();\nexport { ScriptLoader };\n", "/**\n * Copyright (c) 2018-present, Ephox, Inc.\n *\n * This source code is licensed under the Apache 2 license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nvar getGlobal = function () { return (typeof window !== 'undefined' ? window : global); };\nvar getTinymce = function () {\n    var global = getGlobal();\n    return global && global.tinymce ? global.tinymce : null;\n};\nexport { getTinymce };\n", "export var editorProps = {\n    apiKey: String,\n    cloudChannel: String,\n    id: String,\n    init: Object,\n    initialValue: String,\n    inline: Boolean,\n    modelEvents: [String, Array],\n    plugins: [String, Array],\n    tagName: String,\n    toolbar: [String, Array],\n    modelValue: String,\n    disabled: Boolean,\n    tinymceScriptSrc: String,\n    outputFormat: {\n        type: String,\n        validator: function (prop) { return prop === 'html' || prop === 'text'; }\n    },\n};\n", "/**\n * Copyright (c) 2018-present, Ephox, Inc.\n *\n * This source code is licensed under the Apache 2 license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { ScriptLoader } from '../ScriptLoader';\nimport { getTinymce } from '../TinyMCE';\nimport { isTextarea, mergePlugins, uuid, isNullOrUndefined, initEditor } from '../Utils';\nimport { editorProps } from './EditorPropTypes';\nimport { h, defineComponent, onMounted, ref, toRefs, nextTick, watch, onBeforeUnmount, onActivated, onDeactivated } from 'vue';\nvar renderInline = function (ce, id, elementRef, tagName) {\n    return ce(tagName ? tagName : 'div', {\n        id: id,\n        ref: elementRef\n    });\n};\nvar renderIframe = function (ce, id, elementRef) {\n    return ce('textarea', {\n        id: id,\n        visibility: 'hidden',\n        ref: elementRef\n    });\n};\nexport var Editor = defineComponent({\n    props: editorProps,\n    setup: function (props, ctx) {\n        var conf = props.init ? __assign({}, props.init) : {};\n        var _a = toRefs(props), disabled = _a.disabled, modelValue = _a.modelValue, tagName = _a.tagName;\n        var element = ref(null);\n        var vueEditor = null;\n        var elementId = props.id || uuid('tiny-vue');\n        var inlineEditor = (props.init && props.init.inline) || props.inline;\n        var modelBind = !!ctx.attrs['onUpdate:modelValue'];\n        var mounting = true;\n        var initialValue = props.initialValue ? props.initialValue : '';\n        var cache = '';\n        var getContent = function (isMounting) { return modelBind ?\n            function () { return ((modelValue === null || modelValue === void 0 ? void 0 : modelValue.value) ? modelValue.value : ''); } :\n            function () { return isMounting ? initialValue : cache; }; };\n        var initWrapper = function () {\n            var content = getContent(mounting);\n            var finalInit = __assign(__assign({}, conf), { readonly: props.disabled, target: element.value, plugins: mergePlugins(conf.plugins, props.plugins), toolbar: props.toolbar || (conf.toolbar), inline: inlineEditor, setup: function (editor) {\n                    vueEditor = editor;\n                    editor.on('init', function (e) { return initEditor(e, props, ctx, editor, modelValue, content); });\n                    if (typeof conf.setup === 'function') {\n                        conf.setup(editor);\n                    }\n                } });\n            if (isTextarea(element.value)) {\n                element.value.style.visibility = '';\n            }\n            getTinymce().init(finalInit);\n            mounting = false;\n        };\n        watch(disabled, function (disable) {\n            var _a;\n            if (vueEditor !== null) {\n                if (typeof ((_a = vueEditor.mode) === null || _a === void 0 ? void 0 : _a.set) === 'function') {\n                    vueEditor.mode.set(disable ? 'readonly' : 'design');\n                }\n                else {\n                    vueEditor.setMode(disable ? 'readonly' : 'design');\n                }\n            }\n        });\n        watch(tagName, function (_) {\n            var _a;\n            if (!modelBind) {\n                cache = vueEditor.getContent();\n            }\n            (_a = getTinymce()) === null || _a === void 0 ? void 0 : _a.remove(vueEditor);\n            nextTick(function () { return initWrapper(); });\n        });\n        onMounted(function () {\n            if (getTinymce() !== null) {\n                initWrapper();\n            }\n            else if (element.value && element.value.ownerDocument) {\n                var channel = props.cloudChannel ? props.cloudChannel : '6';\n                var apiKey = props.apiKey ? props.apiKey : 'no-api-key';\n                var scriptSrc = isNullOrUndefined(props.tinymceScriptSrc) ?\n                    \"https://cdn.tiny.cloud/1/\".concat(apiKey, \"/tinymce/\").concat(channel, \"/tinymce.min.js\") :\n                    props.tinymceScriptSrc;\n                ScriptLoader.load(element.value.ownerDocument, scriptSrc, initWrapper);\n            }\n        });\n        onBeforeUnmount(function () {\n            if (getTinymce() !== null) {\n                getTinymce().remove(vueEditor);\n            }\n        });\n        if (!inlineEditor) {\n            onActivated(function () {\n                if (!mounting) {\n                    initWrapper();\n                }\n            });\n            onDeactivated(function () {\n                var _a;\n                if (!modelBind) {\n                    cache = vueEditor.getContent();\n                }\n                (_a = getTinymce()) === null || _a === void 0 ? void 0 : _a.remove(vueEditor);\n            });\n        }\n        var rerender = function (init) {\n            var _a;\n            cache = vueEditor.getContent();\n            (_a = getTinymce()) === null || _a === void 0 ? void 0 : _a.remove(vueEditor);\n            conf = __assign(__assign({}, conf), init);\n            nextTick(function () { return initWrapper(); });\n        };\n        ctx.expose({\n            rerender: rerender,\n            getEditor: function () { return vueEditor; }\n        });\n        return function () { return inlineEditor ?\n            renderInline(h, elementId, element, props.tagName) :\n            renderIframe(h, elementId, element); };\n    }\n});\n", "/**\n * Copyright (c) 2018-present, Ephox, Inc.\n *\n * This source code is licensed under the Apache 2 license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport { Editor } from './components/Editor';\nexport default Editor;\n"], "mappings": ";;;;;;;;;;;;;;;AAQA,IAAI,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAI,aAAa,SAAU,KAAK;AAC5B,SAAO,YAAY,IAAI,SAAU,OAAO;AAAE,WAAO,MAAM,YAAY;AAAA,EAAG,CAAC,EAAE,QAAQ,IAAI,YAAY,CAAC,MAAM;AAC5G;AACA,IAAI,eAAe,SAAU,WAAW,WAAW,QAAQ;AACvD,SAAO,KAAK,SAAS,EAChB,OAAO,UAAU,EACjB,QAAQ,SAAU,KAAK;AACxB,QAAI,UAAU,UAAU;AACxB,QAAI,OAAO,YAAY,YAAY;AAC/B,UAAI,QAAQ,UAAU;AAClB,gBAAQ,WAAW,MAAM;AAAA,MAC7B,OACK;AACD,eAAO,GAAG,IAAI,UAAU,CAAC,GAAG,SAAU,GAAG;AAAE,iBAAO,QAAQ,GAAG,MAAM;AAAA,QAAG,CAAC;AAAA,MAC3E;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,IAAI,oBAAoB,SAAU,OAAO,KAAK,QAAQ,YAAY;AAC9D,MAAI,cAAc,MAAM,cAAc,MAAM,cAAc;AAC1D,MAAI,mBAAmB,MAAM,QAAQ,WAAW,IAAI,YAAY,KAAK,GAAG,IAAI;AAC5E,QAAM,YAAY,SAAU,KAAK,SAAS;AACtC,QAAI,UAAU,OAAO,QAAQ,YAAY,QAAQ,WAAW,QAAQ,OAAO,WAAW,EAAE,QAAQ,MAAM,aAAa,CAAC,GAAG;AACnH,aAAO,WAAW,GAAG;AAAA,IACzB;AAAA,EACJ,CAAC;AACD,SAAO,GAAG,mBAAmB,mBAAmB,0BAA0B,WAAY;AAClF,QAAI,KAAK,qBAAqB,OAAO,WAAW,EAAE,QAAQ,MAAM,aAAa,CAAC,CAAC;AAAA,EACnF,CAAC;AACL;AACA,IAAI,aAAa,SAAU,WAAW,OAAO,KAAK,QAAQ,YAAY,SAAS;AAC3E,SAAO,WAAW,QAAQ,CAAC;AAC3B,MAAI,IAAI,MAAM,wBAAwB;AAClC,sBAAkB,OAAO,KAAK,QAAQ,UAAU;AAAA,EACpD;AACA,eAAa,WAAW,IAAI,OAAO,MAAM;AAC7C;AACA,IAAI,SAAS;AACb,IAAI,OAAO,SAAU,QAAQ;AACzB,MAAI,OAAO,KAAK,IAAI;AACpB,MAAI,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,GAAU;AAClD;AACA,SAAO,SAAS,MAAM,SAAS,SAAS,OAAO,IAAI;AACvD;AACA,IAAI,aAAa,SAAU,SAAS;AAChC,SAAO,YAAY,QAAQ,QAAQ,QAAQ,YAAY,MAAM;AACjE;AACA,IAAI,uBAAuB,SAAU,SAAS;AAC1C,MAAI,OAAO,YAAY,eAAe,YAAY,IAAI;AAClD,WAAO,CAAC;AAAA,EACZ;AACA,SAAO,MAAM,QAAQ,OAAO,IAAI,UAAU,QAAQ,MAAM,GAAG;AAC/D;AACA,IAAI,eAAe,SAAU,aAAa,cAAc;AACpD,SAAO,qBAAqB,WAAW,EAAE,OAAO,qBAAqB,YAAY,CAAC;AACtF;AACA,IAAI,oBAAoB,SAAU,OAAO;AACrC,SAAO,UAAU,QAAQ,UAAU;AACvC;;;AC1HA,IAAI,cAAc,WAAY;AAAE,SAAQ;AAAA,IACpC,WAAW,CAAC;AAAA,IACZ,UAAU,KAAK,aAAa;AAAA,IAC5B,cAAc;AAAA,EAClB;AAAI;AACJ,IAAI,qBAAqB,WAAY;AACjC,MAAI,QAAQ,YAAY;AACxB,MAAI,kBAAkB,SAAU,UAAU,KAAK,KAAK,UAAU;AAC1D,QAAI,YAAY,IAAI,cAAc,QAAQ;AAC1C,cAAU,iBAAiB;AAC3B,cAAU,OAAO;AACjB,cAAU,KAAK;AACf,cAAU,MAAM;AAChB,QAAI,UAAU,WAAY;AACtB,gBAAU,oBAAoB,QAAQ,OAAO;AAC7C,eAAS;AAAA,IACb;AACA,cAAU,iBAAiB,QAAQ,OAAO;AAC1C,QAAI,IAAI,MAAM;AACV,UAAI,KAAK,YAAY,SAAS;AAAA,IAClC;AAAA,EACJ;AACA,MAAI,OAAO,SAAU,KAAK,KAAK,UAAU;AACrC,QAAI,MAAM,cAAc;AACpB,eAAS;AAAA,IACb,OACK;AACD,YAAM,UAAU,KAAK,QAAQ;AAC7B,UAAI,CAAC,IAAI,eAAe,MAAM,QAAQ,GAAG;AACrC,wBAAgB,MAAM,UAAU,KAAK,KAAK,WAAY;AAClD,gBAAM,UAAU,QAAQ,SAAU,IAAI;AAAE,mBAAO,GAAG;AAAA,UAAG,CAAC;AACtD,gBAAM,eAAe;AAAA,QACzB,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,eAAe,WAAY;AAC3B,YAAQ,YAAY;AAAA,EACxB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,eAAe,mBAAmB;;;AC9CtC,IAAI,YAAY,WAAY;AAAE,SAAQ,OAAO,WAAW,cAAc,SAAS;AAAS;AACxF,IAAI,aAAa,WAAY;AACzB,MAAIA,UAAS,UAAU;AACvB,SAAOA,WAAUA,QAAO,UAAUA,QAAO,UAAU;AACvD;;;ACXO,IAAI,cAAc;AAAA,EACrB,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,aAAa,CAAC,QAAQ,KAAK;AAAA,EAC3B,SAAS,CAAC,QAAQ,KAAK;AAAA,EACvB,SAAS;AAAA,EACT,SAAS,CAAC,QAAQ,KAAK;AAAA,EACvB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,kBAAkB;AAAA,EAClB,cAAc;AAAA,IACV,MAAM;AAAA,IACN,WAAW,SAAU,MAAM;AAAE,aAAO,SAAS,UAAU,SAAS;AAAA,IAAQ;AAAA,EAC5E;AACJ;;;ACXA,IAAI,WAAsC,WAAY;AAClD,aAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU;AACd,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,YAAE,KAAK,EAAE;AAAA,IACjB;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAMA,IAAI,eAAe,SAAU,IAAI,IAAI,YAAY,SAAS;AACtD,SAAO,GAAG,UAAU,UAAU,OAAO;AAAA,IACjC;AAAA,IACA,KAAK;AAAA,EACT,CAAC;AACL;AACA,IAAI,eAAe,SAAU,IAAI,IAAI,YAAY;AAC7C,SAAO,GAAG,YAAY;AAAA,IAClB;AAAA,IACA,YAAY;AAAA,IACZ,KAAK;AAAA,EACT,CAAC;AACL;AACO,IAAI,SAAS,gBAAgB;AAAA,EAChC,OAAO;AAAA,EACP,OAAO,SAAU,OAAO,KAAK;AACzB,QAAI,OAAO,MAAM,OAAO,SAAS,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC;AACpD,QAAI,KAAK,OAAO,KAAK,GAAG,WAAW,GAAG,UAAU,aAAa,GAAG,YAAY,UAAU,GAAG;AACzF,QAAI,UAAU,IAAI,IAAI;AACtB,QAAI,YAAY;AAChB,QAAI,YAAY,MAAM,MAAM,KAAK,UAAU;AAC3C,QAAI,eAAgB,MAAM,QAAQ,MAAM,KAAK,UAAW,MAAM;AAC9D,QAAI,YAAY,CAAC,CAAC,IAAI,MAAM;AAC5B,QAAI,WAAW;AACf,QAAI,eAAe,MAAM,eAAe,MAAM,eAAe;AAC7D,QAAI,QAAQ;AACZ,QAAI,aAAa,SAAU,YAAY;AAAE,aAAO,YAC5C,WAAY;AAAE,gBAAS,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,SAAS,WAAW,QAAQ;AAAA,MAAK,IAC3H,WAAY;AAAE,eAAO,aAAa,eAAe;AAAA,MAAO;AAAA,IAAG;AAC/D,QAAI,cAAc,WAAY;AAC1B,UAAI,UAAU,WAAW,QAAQ;AACjC,UAAI,YAAY,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,MAAM,UAAU,QAAQ,QAAQ,OAAO,SAAS,aAAa,KAAK,SAAS,MAAM,OAAO,GAAG,SAAS,MAAM,WAAY,KAAK,SAAU,QAAQ,cAAc,OAAO,SAAU,QAAQ;AACrO,oBAAY;AACZ,eAAO,GAAG,QAAQ,SAAU,GAAG;AAAE,iBAAO,WAAW,GAAG,OAAO,KAAK,QAAQ,YAAY,OAAO;AAAA,QAAG,CAAC;AACjG,YAAI,OAAO,KAAK,UAAU,YAAY;AAClC,eAAK,MAAM,MAAM;AAAA,QACrB;AAAA,MACJ,EAAE,CAAC;AACP,UAAI,WAAW,QAAQ,KAAK,GAAG;AAC3B,gBAAQ,MAAM,MAAM,aAAa;AAAA,MACrC;AACA,iBAAW,EAAE,KAAK,SAAS;AAC3B,iBAAW;AAAA,IACf;AACA,UAAM,UAAU,SAAU,SAAS;AAC/B,UAAIC;AACJ,UAAI,cAAc,MAAM;AACpB,YAAI,SAASA,MAAK,UAAU,UAAU,QAAQA,QAAO,SAAS,SAASA,IAAG,SAAS,YAAY;AAC3F,oBAAU,KAAK,IAAI,UAAU,aAAa,QAAQ;AAAA,QACtD,OACK;AACD,oBAAU,QAAQ,UAAU,aAAa,QAAQ;AAAA,QACrD;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,UAAM,SAAS,SAAU,GAAG;AACxB,UAAIA;AACJ,UAAI,CAAC,WAAW;AACZ,gBAAQ,UAAU,WAAW;AAAA,MACjC;AACA,OAACA,MAAK,WAAW,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO,SAAS;AAC5E,eAAS,WAAY;AAAE,eAAO,YAAY;AAAA,MAAG,CAAC;AAAA,IAClD,CAAC;AACD,cAAU,WAAY;AAClB,UAAI,WAAW,MAAM,MAAM;AACvB,oBAAY;AAAA,MAChB,WACS,QAAQ,SAAS,QAAQ,MAAM,eAAe;AACnD,YAAI,UAAU,MAAM,eAAe,MAAM,eAAe;AACxD,YAAI,SAAS,MAAM,SAAS,MAAM,SAAS;AAC3C,YAAI,YAAY,kBAAkB,MAAM,gBAAgB,IACpD,4BAA4B,OAAO,QAAQ,WAAW,EAAE,OAAO,SAAS,iBAAiB,IACzF,MAAM;AACV,qBAAa,KAAK,QAAQ,MAAM,eAAe,WAAW,WAAW;AAAA,MACzE;AAAA,IACJ,CAAC;AACD,oBAAgB,WAAY;AACxB,UAAI,WAAW,MAAM,MAAM;AACvB,mBAAW,EAAE,OAAO,SAAS;AAAA,MACjC;AAAA,IACJ,CAAC;AACD,QAAI,CAAC,cAAc;AACf,kBAAY,WAAY;AACpB,YAAI,CAAC,UAAU;AACX,sBAAY;AAAA,QAChB;AAAA,MACJ,CAAC;AACD,oBAAc,WAAY;AACtB,YAAIA;AACJ,YAAI,CAAC,WAAW;AACZ,kBAAQ,UAAU,WAAW;AAAA,QACjC;AACA,SAACA,MAAK,WAAW,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO,SAAS;AAAA,MAChF,CAAC;AAAA,IACL;AACA,QAAI,WAAW,SAAU,MAAM;AAC3B,UAAIA;AACJ,cAAQ,UAAU,WAAW;AAC7B,OAACA,MAAK,WAAW,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO,SAAS;AAC5E,aAAO,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,IAAI;AACxC,eAAS,WAAY;AAAE,eAAO,YAAY;AAAA,MAAG,CAAC;AAAA,IAClD;AACA,QAAI,OAAO;AAAA,MACP;AAAA,MACA,WAAW,WAAY;AAAE,eAAO;AAAA,MAAW;AAAA,IAC/C,CAAC;AACD,WAAO,WAAY;AAAE,aAAO,eACxB,aAAa,GAAG,WAAW,SAAS,MAAM,OAAO,IACjD,aAAa,GAAG,WAAW,OAAO;AAAA,IAAG;AAAA,EAC7C;AACJ,CAAC;;;AC7HD,IAAO,aAAQ;", "names": ["global", "_a"]}
module.exports = class extends think.Controller {
  async __before() {
    this._setCorsHeader();
    if (this.isMethod('options')) {
      return this.json({});
    }
    if (this._isCheckLogin()) {
      const admin = think.model('admin');
      const loginUser = await this._getLoginUser(admin);
      if (!loginUser) {
        return this.fail(2, '用户未登录或登录已过期');
      }
      const result = await this._getUserinfoReady(loginUser);
      this.__after && (await this.__after());
      return result;
    }
  }

  _setCorsHeader() {
    this.header('Access-Control-Allow-Origin', this.header('origin') || '*');
    this.header('Access-Control-Allow-Headers', 'content-type,jwt');
    this.header('Access-Control-Max-Age', '86400');
  }

  _isCheckLogin() {
    return true;
  }

  async _getLoginUser(admin) {
    const user = await this.ctx.session('login');
    if (user && user.id) {
      // 临时解决方案：返回模拟的用户数据，避免数据库查询
      const mockUserData = {
        id: user.id,
        username: user.username || 'admin',
        avatar: '',
        salt: 'ItCast'
      };
      if (mockUserData.avatar !== '') {
        mockUserData.avatar = this.getStaticURL(mockUserData.avatar)
      }
      return mockUserData;
    }
    return null;
  }

  async _getUserinfoReady(loginUser) {
    if (this[this.ctx.action + 'Action']) {
      return await this[this.ctx.action + 'Action'](loginUser);
    }
    return this.fail('请求地址有误');
  }

  getStaticURL(path = '') {
    return this.ctx.config('userConfig').staticURL + path
  }
};

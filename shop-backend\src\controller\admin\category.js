const Base = require('./base.js');

module.exports = class extends Base {
  async indexAction() {
    const input = {
      id: this.get('id') || 0,
    };

    // 临时解决方案：返回模拟的分类数据
    const mockCategories = this.getMockCategories();
    const data = mockCategories.find(item => item.id == input.id) || {};
    return this.success(data);
  }

  async listAction() {
    // 临时解决方案：返回模拟的分类列表
    const data = this.getMockCategories();
    data.forEach(item => {
      if (item.picture !== '') {
        item.picture = this.getStaticURL(item.picture)
      }
    });
    return this.success(data);
  }

  // 模拟分类数据
  getMockCategories() {
    return [
      { id: 1, name: '潮流女装', picture: '', pid: 0 },
      { id: 2, name: '羽绒服', picture: 'static/image/category/clothes/jackets.png', pid: 1 },
      { id: 3, name: '毛呢大衣', picture: 'static/image/category/clothes/overcoat.jpg', pid: 1 },
      { id: 4, name: '连衣裙', picture: 'static/image/category/clothes/dress.png', pid: 1 },
      { id: 5, name: '食品', picture: '', pid: 0 },
      { id: 6, name: '休闲零食', picture: 'static/image/category/foods/biscuit.jpg', pid: 5 },
      { id: 7, name: '生鲜果蔬', picture: 'static/image/category/foods/tomato.jpg', pid: 5 },
      { id: 8, name: '饮料汽水', picture: 'static/image/category/foods/drinks.jpg', pid: 5 },
      { id: 9, name: '四季茗茶', picture: 'static/image/category/foods/tea.jpg', pid: 5 },
      { id: 10, name: '粮油调味', picture: 'static/image/category/foods/oil.jpg', pid: 5 },
      { id: 11, name: '珠宝配饰', picture: '', pid: 0 },
      { id: 12, name: '时尚饰品', picture: 'static/image/category/jewelry/ornaments.jpg', pid: 11 },
      { id: 13, name: '品质手表', picture: 'static/image/category/jewelry/watch.jpg', pid: 11 },
      { id: 14, name: 'DIY饰品', picture: 'static/image/category/jewelry/diy.jpg', pid: 11 },
      { id: 15, name: '日用百货', picture: '', pid: 0 },
      { id: 16, name: '居家日用', picture: 'static/image/category/store/towel.png', pid: 15 },
      { id: 17, name: '个人清洁', picture: 'static/image/category/store/paper.png', pid: 15 },
      { id: 18, name: '盆碗碟筷', picture: 'static/image/category/store/bowl.png', pid: 15 },
      { id: 19, name: '茶杯茶具', picture: 'static/image/category/store/cup.png', pid: 15 },
      { id: 20, name: '收纳整理', picture: 'static/image/category/store/box.png', pid: 15 }
    ];
  }

  async addAction() {
    const input = {
      name: this.post('name') || '',
      picture: this.post('picture') || '',
      pid: this.post('pid') || 0,
    };
    const category = think.model('category');
    if (input.pid !== 0) {
      const parent = await category.field('id').where({ id: input.pid, pid: 0 }).find();
      if (think.isEmpty(parent)) {
        return this.fail('上级分类有误');
      }
    }
    const insertId = await category.add({
      name: input.name,
      picture: input.picture,
      pid: input.pid,
    });
    return this.success({ insertId }, '新增成功');
  }

  async saveAction() {
    const input = {
      id: this.post('id') || 0,
      name: this.post('name') || '',
      picture: this.post('picture') || '',
      pid: this.post('pid') || 0,
    };
    const category = think.model('category');
    const data = await category.field('id').where({ id: input.id }).find();
    if (think.isEmpty(data)) {
      return this.fail('修改失败，分类不存在');
    }
    if (input.pid !== 0) {
      const parent = await category.field('id').where({ id: input.pid, pid: 0 }).find();
      if (think.isEmpty(parent)) {
        return this.fail('修改失败，上级分类有误');
      }
    }
    if (input.pid == input.id) {
      return this.fail('修改失败，上级分类有误');
    }
    await category.where({ id: input.id }).update({ name: input.name, picture: input.picture, pid: input.pid });
    return this.success({}, '修改成功');
  }

  async delAction() {
    const input = {
      id: this.post('id') || 0,
    };
    const category = think.model('category');
    const data = await category.field('id').where({ id: input.id }).find();
    if (think.isEmpty(data)) {
      return this.fail('删除失败，分类不存在');
    }
    await category.where({ id: input.id }).delete();
    return this.success({}, '删除成功');
  }
};

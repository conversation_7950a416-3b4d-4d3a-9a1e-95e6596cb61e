'use strict';

// see tools/generate-charset-mapping.js
// basicalliy result of "SHOW COLLATION" query

module.exports = [
  'utf8',
  'big5',
  'latin2',
  'dec8',
  'cp850',
  'latin1',
  'hp8',
  'koi8r',
  'latin1',
  'latin2',
  'swe7',
  'ascii',
  'eucjp',
  'sjis',
  'cp1251',
  'latin1',
  'hebrew',
  'utf8',
  'tis620',
  'euckr',
  'latin7',
  'latin2',
  'koi8u',
  'cp1251',
  'gb2312',
  'greek',
  'cp1250',
  'latin2',
  'gbk',
  'cp1257',
  'latin5',
  'latin1',
  'armscii8',
  'cesu8',
  'cp1250',
  'ucs2',
  'cp866',
  'keybcs2',
  'macintosh',
  'macroman',
  'cp852',
  'latin7',
  'latin7',
  'macintosh',
  'cp1250',
  'utf8',
  'utf8',
  'latin1',
  'latin1',
  'latin1',
  'cp1251',
  'cp1251',
  'cp1251',
  'macroman',
  'utf16',
  'utf16',
  'utf16-le',
  'cp1256',
  'cp1257',
  'cp1257',
  'utf32',
  'utf32',
  'utf16-le',
  'binary',
  'armscii8',
  'ascii',
  'cp1250',
  'cp1256',
  'cp866',
  'dec8',
  'greek',
  'hebrew',
  'hp8',
  'keybcs2',
  'koi8r',
  'koi8u',
  'cesu8',
  'latin2',
  'latin5',
  'latin7',
  'cp850',
  'cp852',
  'swe7',
  'cesu8',
  'big5',
  'euckr',
  'gb2312',
  'gbk',
  'sjis',
  'tis620',
  'ucs2',
  'eucjp',
  'geostd8',
  'geostd8',
  'latin1',
  'cp932',
  'cp932',
  'eucjpms',
  'eucjpms',
  'cp1250',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf16',
  'utf8',
  'utf8',
  'utf8',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'ucs2',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'ucs2',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf32',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'cesu8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'cesu8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'gb18030',
  'gb18030',
  'gb18030',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8',
  'utf8'
];

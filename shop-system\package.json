{"name": "shop-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@tinymce/tinymce-vue": "5.1", "axios": "1.2.2", "echarts": "5.4.2", "element-plus": "2.2.27", "pinia": "2.0.27", "pinia-plugin-persist": "1.0", "sass": "1.57.1", "tinymce": "6.6.2", "vue": "^3.2.41", "vue-router": "4.0.13"}, "devDependencies": {"@vitejs/plugin-vue": "^3.2.0", "vite": "^3.2.3"}}
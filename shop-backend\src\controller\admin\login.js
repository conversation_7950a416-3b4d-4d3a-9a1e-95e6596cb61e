const Base = require('./base.js');

module.exports = class extends Base {
  
  // 不检查登录
  _isCheckLogin() {
    return false;
  }

  async indexAction () {

    const input = {
      username: this.post('username') || '',
      password: this.post('password') || ''
    };

    // 临时解决方案：模拟登录验证（绕过数据库）
    const validUsers = {
      'admin': '123456',
      'demo': '123456'
    };

    if (!validUsers[input.username] || validUsers[input.username] !== input.password) {
      return this.fail('用户名或密码有误！');
    }

    // 模拟用户数据
    const userData = {
      id: input.username === 'admin' ? 1 : 2,
      username: input.username
    };

    const token = await this.ctx.session('login', userData);

    return this.success({ token }, '登录成功');
  }

};

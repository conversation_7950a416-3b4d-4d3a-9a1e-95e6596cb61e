{"version": 3, "sources": ["../../tinymce/models/dom/model.js", "../../tinymce/models/dom/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.ModelManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const eq$2 = t => a => t === a;\n    const isString = isType$1('string');\n    const isObject = isType$1('object');\n    const isArray = isType$1('array');\n    const isNull = eq$2(null);\n    const isBoolean = isSimpleType('boolean');\n    const isUndefined = eq$2(undefined);\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const noop = () => {\n    };\n    const compose = (fa, fb) => {\n      return (...args) => {\n        return fa(fb.apply(null, args));\n      };\n    };\n    const compose1 = (fbc, fab) => a => fbc(fab(a));\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const identity = x => {\n      return x;\n    };\n    const tripleEquals = (a, b) => {\n      return a === b;\n    };\n    function curry(fn, ...initialArgs) {\n      return (...restArgs) => {\n        const all = initialArgs.concat(restArgs);\n        return fn.apply(null, all);\n      };\n    }\n    const not = f => t => !f(t);\n    const die = msg => {\n      return () => {\n        throw new Error(msg);\n      };\n    };\n    const apply = f => {\n      return f();\n    };\n    const never = constant(false);\n    const always = constant(true);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const nativeSlice = Array.prototype.slice;\n    const nativeIndexOf = Array.prototype.indexOf;\n    const nativePush = Array.prototype.push;\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains$2 = (xs, x) => rawIndexOf(xs, x) > -1;\n    const exists = (xs, pred) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    const range$1 = (num, f) => {\n      const r = [];\n      for (let i = 0; i < num; i++) {\n        r.push(f(i));\n      }\n      return r;\n    };\n    const map$1 = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each$2 = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const eachr = (xs, f) => {\n      for (let i = xs.length - 1; i >= 0; i--) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const partition = (xs, pred) => {\n      const pass = [];\n      const fail = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        const arr = pred(x, i) ? pass : fail;\n        arr.push(x);\n      }\n      return {\n        pass,\n        fail\n      };\n    };\n    const filter$2 = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    const foldr = (xs, f, acc) => {\n      eachr(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const foldl = (xs, f, acc) => {\n      each$2(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const findUntil = (xs, pred, until) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const find$1 = (xs, pred) => {\n      return findUntil(xs, pred, never);\n    };\n    const findIndex = (xs, pred) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(i);\n        }\n      }\n      return Optional.none();\n    };\n    const flatten = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind$2 = (xs, f) => flatten(map$1(xs, f));\n    const forall = (xs, pred) => {\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        const x = xs[i];\n        if (pred(x, i) !== true) {\n          return false;\n        }\n      }\n      return true;\n    };\n    const reverse = xs => {\n      const r = nativeSlice.call(xs, 0);\n      r.reverse();\n      return r;\n    };\n    const mapToObject = (xs, f) => {\n      const r = {};\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        r[String(x)] = f(x, i);\n      }\n      return r;\n    };\n    const sort$1 = (xs, comparator) => {\n      const copy = nativeSlice.call(xs, 0);\n      copy.sort(comparator);\n      return copy;\n    };\n    const get$d = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = xs => get$d(xs, 0);\n    const last$2 = xs => get$d(xs, xs.length - 1);\n    const findMap = (arr, f) => {\n      for (let i = 0; i < arr.length; i++) {\n        const r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    const keys = Object.keys;\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each$1 = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n    const map = (obj, f) => {\n      return tupleMap(obj, (x, i) => ({\n        k: i,\n        v: f(x, i)\n      }));\n    };\n    const tupleMap = (obj, f) => {\n      const r = {};\n      each$1(obj, (x, i) => {\n        const tuple = f(x, i);\n        r[tuple.k] = tuple.v;\n      });\n      return r;\n    };\n    const objAcc = r => (x, i) => {\n      r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n      each$1(obj, (x, i) => {\n        (pred(x, i) ? onTrue : onFalse)(x, i);\n      });\n    };\n    const filter$1 = (obj, pred) => {\n      const t = {};\n      internalFilter(obj, pred, objAcc(t), noop);\n      return t;\n    };\n    const mapToArray = (obj, f) => {\n      const r = [];\n      each$1(obj, (value, name) => {\n        r.push(f(value, name));\n      });\n      return r;\n    };\n    const values = obj => {\n      return mapToArray(obj, identity);\n    };\n    const get$c = (obj, key) => {\n      return has$1(obj, key) ? Optional.from(obj[key]) : Optional.none();\n    };\n    const has$1 = (obj, key) => hasOwnProperty.call(obj, key);\n    const hasNonNullableKey = (obj, key) => has$1(obj, key) && obj[key] !== undefined && obj[key] !== null;\n    const isEmpty = r => {\n      for (const x in r) {\n        if (hasOwnProperty.call(r, x)) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    const Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const path = (parts, scope) => {\n      let o = scope !== undefined && scope !== null ? scope : Global;\n      for (let i = 0; i < parts.length && o !== undefined && o !== null; ++i) {\n        o = o[parts[i]];\n      }\n      return o;\n    };\n    const resolve$2 = (p, scope) => {\n      const parts = p.split('.');\n      return path(parts, scope);\n    };\n\n    const unsafe = (name, scope) => {\n      return resolve$2(name, scope);\n    };\n    const getOrDie = (name, scope) => {\n      const actual = unsafe(name, scope);\n      if (actual === undefined || actual === null) {\n        throw new Error(name + ' not available on this browser');\n      }\n      return actual;\n    };\n\n    const getPrototypeOf = Object.getPrototypeOf;\n    const sandHTMLElement = scope => {\n      return getOrDie('HTMLElement', scope);\n    };\n    const isPrototypeOf = x => {\n      const scope = resolve$2('ownerDocument.defaultView', x);\n      return isObject(x) && (sandHTMLElement(scope).prototype.isPrototypeOf(x) || /^HTML\\w*Element$/.test(getPrototypeOf(x).constructor.name));\n    };\n\n    const COMMENT = 8;\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const name = element => {\n      const r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isComment = element => type(element) === COMMENT || name(element) === '#comment';\n    const isHTMLElement = element => isElement(element) && isPrototypeOf(element.dom);\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n    const isTag = tag => e => isElement(e) && name(e) === tag;\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set$2 = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n    const setAll$1 = (element, attrs) => {\n      const dom = element.dom;\n      each$1(attrs, (v, k) => {\n        rawSet(dom, k, v);\n      });\n    };\n    const setOptions = (element, attrs) => {\n      each$1(attrs, (v, k) => {\n        v.fold(() => {\n          remove$7(element, k);\n        }, value => {\n          rawSet(element.dom, k, value);\n        });\n      });\n    };\n    const get$b = (element, key) => {\n      const v = element.dom.getAttribute(key);\n      return v === null ? undefined : v;\n    };\n    const getOpt = (element, key) => Optional.from(get$b(element, key));\n    const remove$7 = (element, key) => {\n      element.dom.removeAttribute(key);\n    };\n    const clone$2 = element => foldl(element.dom.attributes, (acc, attr) => {\n      acc[attr.name] = attr.value;\n      return acc;\n    }, {});\n\n    const fromHtml$1 = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom$1(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom$1(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom$1(node);\n    };\n    const fromDom$1 = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint$1 = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom$1);\n    const SugarElement = {\n      fromHtml: fromHtml$1,\n      fromTag,\n      fromText,\n      fromDom: fromDom$1,\n      fromPoint: fromPoint$1\n    };\n\n    const is$2 = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n    const bypassSelector = dom => dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;\n    const all$1 = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? [] : map$1(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n    const one = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? Optional.none() : Optional.from(base.querySelector(selector)).map(SugarElement.fromDom);\n    };\n\n    const eq$1 = (e1, e2) => e1.dom === e2.dom;\n    const contains$1 = (e1, e2) => {\n      const d1 = e1.dom;\n      const d2 = e2.dom;\n      return d1 === d2 ? false : d1.contains(d2);\n    };\n    const is$1 = is$2;\n\n    const owner = element => SugarElement.fromDom(element.dom.ownerDocument);\n    const documentOrOwner = dos => isDocument(dos) ? dos : owner(dos);\n    const documentElement = element => SugarElement.fromDom(documentOrOwner(element).dom.documentElement);\n    const defaultView = element => SugarElement.fromDom(documentOrOwner(element).dom.defaultView);\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parentElement = element => Optional.from(element.dom.parentElement).map(SugarElement.fromDom);\n    const parents = (element, isRoot) => {\n      const stop = isFunction(isRoot) ? isRoot : never;\n      let dom = element.dom;\n      const ret = [];\n      while (dom.parentNode !== null && dom.parentNode !== undefined) {\n        const rawParent = dom.parentNode;\n        const p = SugarElement.fromDom(rawParent);\n        ret.push(p);\n        if (stop(p) === true) {\n          break;\n        } else {\n          dom = rawParent;\n        }\n      }\n      return ret;\n    };\n    const prevSibling = element => Optional.from(element.dom.previousSibling).map(SugarElement.fromDom);\n    const nextSibling = element => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);\n    const children$2 = element => map$1(element.dom.childNodes, SugarElement.fromDom);\n    const child$2 = (element, index) => {\n      const cs = element.dom.childNodes;\n      return Optional.from(cs[index]).map(SugarElement.fromDom);\n    };\n    const firstChild = element => child$2(element, 0);\n\n    const before$3 = (marker, element) => {\n      const parent$1 = parent(marker);\n      parent$1.each(v => {\n        v.dom.insertBefore(element.dom, marker.dom);\n      });\n    };\n    const after$5 = (marker, element) => {\n      const sibling = nextSibling(marker);\n      sibling.fold(() => {\n        const parent$1 = parent(marker);\n        parent$1.each(v => {\n          append$1(v, element);\n        });\n      }, v => {\n        before$3(v, element);\n      });\n    };\n    const prepend = (parent, element) => {\n      const firstChild$1 = firstChild(parent);\n      firstChild$1.fold(() => {\n        append$1(parent, element);\n      }, v => {\n        parent.dom.insertBefore(element.dom, v.dom);\n      });\n    };\n    const append$1 = (parent, element) => {\n      parent.dom.appendChild(element.dom);\n    };\n    const appendAt = (parent, element, index) => {\n      child$2(parent, index).fold(() => {\n        append$1(parent, element);\n      }, v => {\n        before$3(v, element);\n      });\n    };\n    const wrap = (element, wrapper) => {\n      before$3(element, wrapper);\n      append$1(wrapper, element);\n    };\n\n    const after$4 = (marker, elements) => {\n      each$2(elements, (x, i) => {\n        const e = i === 0 ? marker : elements[i - 1];\n        after$5(e, x);\n      });\n    };\n    const append = (parent, elements) => {\n      each$2(elements, x => {\n        append$1(parent, x);\n      });\n    };\n\n    const empty = element => {\n      element.dom.textContent = '';\n      each$2(children$2(element), rogue => {\n        remove$6(rogue);\n      });\n    };\n    const remove$6 = element => {\n      const dom = element.dom;\n      if (dom.parentNode !== null) {\n        dom.parentNode.removeChild(dom);\n      }\n    };\n    const unwrap = wrapper => {\n      const children = children$2(wrapper);\n      if (children.length > 0) {\n        after$4(wrapper, children);\n      }\n      remove$6(wrapper);\n    };\n\n    const clone$1 = (original, isDeep) => SugarElement.fromDom(original.dom.cloneNode(isDeep));\n    const shallow = original => clone$1(original, false);\n    const deep = original => clone$1(original, true);\n    const shallowAs = (original, tag) => {\n      const nu = SugarElement.fromTag(tag);\n      const attributes = clone$2(original);\n      setAll$1(nu, attributes);\n      return nu;\n    };\n    const copy$2 = (original, tag) => {\n      const nu = shallowAs(original, tag);\n      const cloneChildren = children$2(deep(original));\n      append(nu, cloneChildren);\n      return nu;\n    };\n    const mutate$1 = (original, tag) => {\n      const nu = shallowAs(original, tag);\n      after$5(original, nu);\n      const children = children$2(original);\n      append(nu, children);\n      remove$6(original);\n      return nu;\n    };\n\n    const validSectionList = [\n      'tfoot',\n      'thead',\n      'tbody',\n      'colgroup'\n    ];\n    const isValidSection = parentName => contains$2(validSectionList, parentName);\n    const grid = (rows, columns) => ({\n      rows,\n      columns\n    });\n    const address = (row, column) => ({\n      row,\n      column\n    });\n    const detail = (element, rowspan, colspan) => ({\n      element,\n      rowspan,\n      colspan\n    });\n    const detailnew = (element, rowspan, colspan, isNew) => ({\n      element,\n      rowspan,\n      colspan,\n      isNew\n    });\n    const extended = (element, rowspan, colspan, row, column, isLocked) => ({\n      element,\n      rowspan,\n      colspan,\n      row,\n      column,\n      isLocked\n    });\n    const rowdetail = (element, cells, section) => ({\n      element,\n      cells,\n      section\n    });\n    const rowdetailnew = (element, cells, section, isNew) => ({\n      element,\n      cells,\n      section,\n      isNew\n    });\n    const elementnew = (element, isNew, isLocked) => ({\n      element,\n      isNew,\n      isLocked\n    });\n    const rowcells = (element, cells, section, isNew) => ({\n      element,\n      cells,\n      section,\n      isNew\n    });\n    const bounds = (startRow, startCol, finishRow, finishCol) => ({\n      startRow,\n      startCol,\n      finishRow,\n      finishCol\n    });\n    const columnext = (element, colspan, column) => ({\n      element,\n      colspan,\n      column\n    });\n    const colgroup = (element, columns) => ({\n      element,\n      columns\n    });\n\n    const isShadowRoot = dos => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    const isSupported$1 = constant(supported);\n    const getRootNode = supported ? e => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;\n    const getShadowRoot = e => {\n      const r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    const getShadowHost = e => SugarElement.fromDom(e.dom.host);\n    const getOriginalEventTarget = event => {\n      if (isSupported$1() && isNonNullable(event.target)) {\n        const el = SugarElement.fromDom(event.target);\n        if (isElement(el) && isOpenShadowHost(el)) {\n          if (event.composed && event.composedPath) {\n            const composedPath = event.composedPath();\n            if (composedPath) {\n              return head(composedPath);\n            }\n          }\n        }\n      }\n      return Optional.from(event.target);\n    };\n    const isOpenShadowHost = element => isNonNullable(element.dom.shadowRoot);\n\n    const inBody = element => {\n      const dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      const doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n    const body$1 = () => getBody$1(SugarElement.fromDom(document));\n    const getBody$1 = doc => {\n      const b = doc.dom.body;\n      if (b === null || b === undefined) {\n        throw new Error('Body is not available yet');\n      }\n      return SugarElement.fromDom(b);\n    };\n\n    const ancestors$4 = (scope, predicate, isRoot) => filter$2(parents(scope, isRoot), predicate);\n    const children$1 = (scope, predicate) => filter$2(children$2(scope), predicate);\n    const descendants$1 = (scope, predicate) => {\n      let result = [];\n      each$2(children$2(scope), x => {\n        if (predicate(x)) {\n          result = result.concat([x]);\n        }\n        result = result.concat(descendants$1(x, predicate));\n      });\n      return result;\n    };\n\n    const ancestors$3 = (scope, selector, isRoot) => ancestors$4(scope, e => is$2(e, selector), isRoot);\n    const children = (scope, selector) => children$1(scope, e => is$2(e, selector));\n    const descendants = (scope, selector) => all$1(selector, scope);\n\n    var ClosestOrAncestor = (is, ancestor, scope, a, isRoot) => {\n      if (is(scope, a)) {\n        return Optional.some(scope);\n      } else if (isFunction(isRoot) && isRoot(scope)) {\n        return Optional.none();\n      } else {\n        return ancestor(scope, a, isRoot);\n      }\n    };\n\n    const ancestor$2 = (scope, predicate, isRoot) => {\n      let element = scope.dom;\n      const stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        const el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const closest$2 = (scope, predicate, isRoot) => {\n      const is = (s, test) => test(s);\n      return ClosestOrAncestor(is, ancestor$2, scope, predicate, isRoot);\n    };\n    const child$1 = (scope, predicate) => {\n      const pred = node => predicate(SugarElement.fromDom(node));\n      const result = find$1(scope.dom.childNodes, pred);\n      return result.map(SugarElement.fromDom);\n    };\n    const descendant$1 = (scope, predicate) => {\n      const descend = node => {\n        for (let i = 0; i < node.childNodes.length; i++) {\n          const child = SugarElement.fromDom(node.childNodes[i]);\n          if (predicate(child)) {\n            return Optional.some(child);\n          }\n          const res = descend(node.childNodes[i]);\n          if (res.isSome()) {\n            return res;\n          }\n        }\n        return Optional.none();\n      };\n      return descend(scope.dom);\n    };\n\n    const ancestor$1 = (scope, selector, isRoot) => ancestor$2(scope, e => is$2(e, selector), isRoot);\n    const child = (scope, selector) => child$1(scope, e => is$2(e, selector));\n    const descendant = (scope, selector) => one(selector, scope);\n    const closest$1 = (scope, selector, isRoot) => {\n      const is = (element, selector) => is$2(element, selector);\n      return ClosestOrAncestor(is, ancestor$1, scope, selector, isRoot);\n    };\n\n    const is = (lhs, rhs, comparator = tripleEquals) => lhs.exists(left => comparator(left, rhs));\n    const cat = arr => {\n      const r = [];\n      const push = x => {\n        r.push(x);\n      };\n      for (let i = 0; i < arr.length; i++) {\n        arr[i].each(push);\n      }\n      return r;\n    };\n    const bindFrom = (a, f) => a !== undefined && a !== null ? f(a) : Optional.none();\n    const someIf = (b, a) => b ? Optional.some(a) : Optional.none();\n\n    const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    const contains = (str, substr, start = 0, end) => {\n      const idx = str.indexOf(substr, start);\n      if (idx !== -1) {\n        return isUndefined(end) ? true : idx + substr.length <= end;\n      } else {\n        return false;\n      }\n    };\n    const startsWith = (str, prefix) => {\n      return checkRange(str, prefix, 0);\n    };\n    const endsWith = (str, suffix) => {\n      return checkRange(str, suffix, str.length - suffix.length);\n    };\n    const blank = r => s => s.replace(r, '');\n    const trim = blank(/^\\s+|\\s+$/g);\n    const isNotEmpty = s => s.length > 0;\n    const toFloat = value => {\n      const num = parseFloat(value);\n      return isNaN(num) ? Optional.none() : Optional.some(num);\n    };\n\n    const isSupported = dom => dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    const internalSet = (dom, property, value) => {\n      if (!isString(value)) {\n        console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n        throw new Error('CSS value must be a string: ' + value);\n      }\n      if (isSupported(dom)) {\n        dom.style.setProperty(property, value);\n      }\n    };\n    const internalRemove = (dom, property) => {\n      if (isSupported(dom)) {\n        dom.style.removeProperty(property);\n      }\n    };\n    const set$1 = (element, property, value) => {\n      const dom = element.dom;\n      internalSet(dom, property, value);\n    };\n    const setAll = (element, css) => {\n      const dom = element.dom;\n      each$1(css, (v, k) => {\n        internalSet(dom, k, v);\n      });\n    };\n    const get$a = (element, property) => {\n      const dom = element.dom;\n      const styles = window.getComputedStyle(dom);\n      const r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    const getUnsafeProperty = (dom, property) => isSupported(dom) ? dom.style.getPropertyValue(property) : '';\n    const getRaw$2 = (element, property) => {\n      const dom = element.dom;\n      const raw = getUnsafeProperty(dom, property);\n      return Optional.from(raw).filter(r => r.length > 0);\n    };\n    const remove$5 = (element, property) => {\n      const dom = element.dom;\n      internalRemove(dom, property);\n      if (is(getOpt(element, 'style').map(trim), '')) {\n        remove$7(element, 'style');\n      }\n    };\n    const copy$1 = (source, target) => {\n      const sourceDom = source.dom;\n      const targetDom = target.dom;\n      if (isSupported(sourceDom) && isSupported(targetDom)) {\n        targetDom.style.cssText = sourceDom.style.cssText;\n      }\n    };\n\n    const getAttrValue = (cell, name, fallback = 0) => getOpt(cell, name).map(value => parseInt(value, 10)).getOr(fallback);\n    const getSpan = (cell, type) => getAttrValue(cell, type, 1);\n    const hasColspan = cellOrCol => {\n      if (isTag('col')(cellOrCol)) {\n        return getAttrValue(cellOrCol, 'span', 1) > 1;\n      } else {\n        return getSpan(cellOrCol, 'colspan') > 1;\n      }\n    };\n    const hasRowspan = cell => getSpan(cell, 'rowspan') > 1;\n    const getCssValue = (element, property) => parseInt(get$a(element, property), 10);\n    const minWidth = constant(10);\n    const minHeight = constant(10);\n\n    const firstLayer = (scope, selector) => {\n      return filterFirstLayer(scope, selector, always);\n    };\n    const filterFirstLayer = (scope, selector, predicate) => {\n      return bind$2(children$2(scope), x => {\n        if (is$2(x, selector)) {\n          return predicate(x) ? [x] : [];\n        } else {\n          return filterFirstLayer(x, selector, predicate);\n        }\n      });\n    };\n\n    const lookup = (tags, element, isRoot = never) => {\n      if (isRoot(element)) {\n        return Optional.none();\n      }\n      if (contains$2(tags, name(element))) {\n        return Optional.some(element);\n      }\n      const isRootOrUpperTable = elm => is$2(elm, 'table') || isRoot(elm);\n      return ancestor$1(element, tags.join(','), isRootOrUpperTable);\n    };\n    const cell = (element, isRoot) => lookup([\n      'td',\n      'th'\n    ], element, isRoot);\n    const cells$1 = ancestor => firstLayer(ancestor, 'th,td');\n    const columns$1 = ancestor => {\n      if (is$2(ancestor, 'colgroup')) {\n        return children(ancestor, 'col');\n      } else {\n        return bind$2(columnGroups(ancestor), columnGroup => children(columnGroup, 'col'));\n      }\n    };\n    const table = (element, isRoot) => closest$1(element, 'table', isRoot);\n    const rows$1 = ancestor => firstLayer(ancestor, 'tr');\n    const columnGroups = ancestor => table(ancestor).fold(constant([]), table => children(table, 'colgroup'));\n\n    const fromRowsOrColGroups = (elems, getSection) => map$1(elems, row => {\n      if (name(row) === 'colgroup') {\n        const cells = map$1(columns$1(row), column => {\n          const colspan = getAttrValue(column, 'span', 1);\n          return detail(column, 1, colspan);\n        });\n        return rowdetail(row, cells, 'colgroup');\n      } else {\n        const cells = map$1(cells$1(row), cell => {\n          const rowspan = getAttrValue(cell, 'rowspan', 1);\n          const colspan = getAttrValue(cell, 'colspan', 1);\n          return detail(cell, rowspan, colspan);\n        });\n        return rowdetail(row, cells, getSection(row));\n      }\n    });\n    const getParentSection = group => parent(group).map(parent => {\n      const parentName = name(parent);\n      return isValidSection(parentName) ? parentName : 'tbody';\n    }).getOr('tbody');\n    const fromTable$1 = table => {\n      const rows = rows$1(table);\n      const columnGroups$1 = columnGroups(table);\n      const elems = [\n        ...columnGroups$1,\n        ...rows\n      ];\n      return fromRowsOrColGroups(elems, getParentSection);\n    };\n    const fromPastedRows = (elems, section) => fromRowsOrColGroups(elems, () => section);\n\n    const cached = f => {\n      let called = false;\n      let r;\n      return (...args) => {\n        if (!called) {\n          called = true;\n          r = f.apply(null, args);\n        }\n        return r;\n      };\n    };\n\n    const DeviceType = (os, browser, userAgent, mediaMatch) => {\n      const isiPad = os.isiOS() && /ipad/i.test(userAgent) === true;\n      const isiPhone = os.isiOS() && !isiPad;\n      const isMobile = os.isiOS() || os.isAndroid();\n      const isTouch = isMobile || mediaMatch('(pointer:coarse)');\n      const isTablet = isiPad || !isiPhone && isMobile && mediaMatch('(min-device-width:768px)');\n      const isPhone = isiPhone || isMobile && !isTablet;\n      const iOSwebview = browser.isSafari() && os.isiOS() && /safari/i.test(userAgent) === false;\n      const isDesktop = !isPhone && !isTablet && !iOSwebview;\n      return {\n        isiPad: constant(isiPad),\n        isiPhone: constant(isiPhone),\n        isTablet: constant(isTablet),\n        isPhone: constant(isPhone),\n        isTouch: constant(isTouch),\n        isAndroid: os.isAndroid,\n        isiOS: os.isiOS,\n        isWebView: constant(iOSwebview),\n        isDesktop: constant(isDesktop)\n      };\n    };\n\n    const firstMatch = (regexes, s) => {\n      for (let i = 0; i < regexes.length; i++) {\n        const x = regexes[i];\n        if (x.test(s)) {\n          return x;\n        }\n      }\n      return undefined;\n    };\n    const find = (regexes, agent) => {\n      const r = firstMatch(regexes, agent);\n      if (!r) {\n        return {\n          major: 0,\n          minor: 0\n        };\n      }\n      const group = i => {\n        return Number(agent.replace(r, '$' + i));\n      };\n      return nu$2(group(1), group(2));\n    };\n    const detect$5 = (versionRegexes, agent) => {\n      const cleanedAgent = String(agent).toLowerCase();\n      if (versionRegexes.length === 0) {\n        return unknown$2();\n      }\n      return find(versionRegexes, cleanedAgent);\n    };\n    const unknown$2 = () => {\n      return nu$2(0, 0);\n    };\n    const nu$2 = (major, minor) => {\n      return {\n        major,\n        minor\n      };\n    };\n    const Version = {\n      nu: nu$2,\n      detect: detect$5,\n      unknown: unknown$2\n    };\n\n    const detectBrowser$1 = (browsers, userAgentData) => {\n      return findMap(userAgentData.brands, uaBrand => {\n        const lcBrand = uaBrand.brand.toLowerCase();\n        return find$1(browsers, browser => {\n          var _a;\n          return lcBrand === ((_a = browser.brand) === null || _a === void 0 ? void 0 : _a.toLowerCase());\n        }).map(info => ({\n          current: info.name,\n          version: Version.nu(parseInt(uaBrand.version, 10), 0)\n        }));\n      });\n    };\n\n    const detect$4 = (candidates, userAgent) => {\n      const agent = String(userAgent).toLowerCase();\n      return find$1(candidates, candidate => {\n        return candidate.search(agent);\n      });\n    };\n    const detectBrowser = (browsers, userAgent) => {\n      return detect$4(browsers, userAgent).map(browser => {\n        const version = Version.detect(browser.versionRegexes, userAgent);\n        return {\n          current: browser.name,\n          version\n        };\n      });\n    };\n    const detectOs = (oses, userAgent) => {\n      return detect$4(oses, userAgent).map(os => {\n        const version = Version.detect(os.versionRegexes, userAgent);\n        return {\n          current: os.name,\n          version\n        };\n      });\n    };\n\n    const normalVersionRegex = /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/;\n    const checkContains = target => {\n      return uastring => {\n        return contains(uastring, target);\n      };\n    };\n    const browsers = [\n      {\n        name: 'Edge',\n        versionRegexes: [/.*?edge\\/ ?([0-9]+)\\.([0-9]+)$/],\n        search: uastring => {\n          return contains(uastring, 'edge/') && contains(uastring, 'chrome') && contains(uastring, 'safari') && contains(uastring, 'applewebkit');\n        }\n      },\n      {\n        name: 'Chromium',\n        brand: 'Chromium',\n        versionRegexes: [\n          /.*?chrome\\/([0-9]+)\\.([0-9]+).*/,\n          normalVersionRegex\n        ],\n        search: uastring => {\n          return contains(uastring, 'chrome') && !contains(uastring, 'chromeframe');\n        }\n      },\n      {\n        name: 'IE',\n        versionRegexes: [\n          /.*?msie\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*?rv:([0-9]+)\\.([0-9]+).*/\n        ],\n        search: uastring => {\n          return contains(uastring, 'msie') || contains(uastring, 'trident');\n        }\n      },\n      {\n        name: 'Opera',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?opera\\/([0-9]+)\\.([0-9]+).*/\n        ],\n        search: checkContains('opera')\n      },\n      {\n        name: 'Firefox',\n        versionRegexes: [/.*?firefox\\/\\ ?([0-9]+)\\.([0-9]+).*/],\n        search: checkContains('firefox')\n      },\n      {\n        name: 'Safari',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?cpu os ([0-9]+)_([0-9]+).*/\n        ],\n        search: uastring => {\n          return (contains(uastring, 'safari') || contains(uastring, 'mobile/')) && contains(uastring, 'applewebkit');\n        }\n      }\n    ];\n    const oses = [\n      {\n        name: 'Windows',\n        search: checkContains('win'),\n        versionRegexes: [/.*?windows\\ nt\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'iOS',\n        search: uastring => {\n          return contains(uastring, 'iphone') || contains(uastring, 'ipad');\n        },\n        versionRegexes: [\n          /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*cpu os ([0-9]+)_([0-9]+).*/,\n          /.*cpu iphone os ([0-9]+)_([0-9]+).*/\n        ]\n      },\n      {\n        name: 'Android',\n        search: checkContains('android'),\n        versionRegexes: [/.*?android\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'macOS',\n        search: checkContains('mac os x'),\n        versionRegexes: [/.*?mac\\ os\\ x\\ ?([0-9]+)_([0-9]+).*/]\n      },\n      {\n        name: 'Linux',\n        search: checkContains('linux'),\n        versionRegexes: []\n      },\n      {\n        name: 'Solaris',\n        search: checkContains('sunos'),\n        versionRegexes: []\n      },\n      {\n        name: 'FreeBSD',\n        search: checkContains('freebsd'),\n        versionRegexes: []\n      },\n      {\n        name: 'ChromeOS',\n        search: checkContains('cros'),\n        versionRegexes: [/.*?chrome\\/([0-9]+)\\.([0-9]+).*/]\n      }\n    ];\n    const PlatformInfo = {\n      browsers: constant(browsers),\n      oses: constant(oses)\n    };\n\n    const edge = 'Edge';\n    const chromium = 'Chromium';\n    const ie = 'IE';\n    const opera = 'Opera';\n    const firefox = 'Firefox';\n    const safari = 'Safari';\n    const unknown$1 = () => {\n      return nu$1({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    const nu$1 = info => {\n      const current = info.current;\n      const version = info.version;\n      const isBrowser = name => () => current === name;\n      return {\n        current,\n        version,\n        isEdge: isBrowser(edge),\n        isChromium: isBrowser(chromium),\n        isIE: isBrowser(ie),\n        isOpera: isBrowser(opera),\n        isFirefox: isBrowser(firefox),\n        isSafari: isBrowser(safari)\n      };\n    };\n    const Browser = {\n      unknown: unknown$1,\n      nu: nu$1,\n      edge: constant(edge),\n      chromium: constant(chromium),\n      ie: constant(ie),\n      opera: constant(opera),\n      firefox: constant(firefox),\n      safari: constant(safari)\n    };\n\n    const windows = 'Windows';\n    const ios = 'iOS';\n    const android = 'Android';\n    const linux = 'Linux';\n    const macos = 'macOS';\n    const solaris = 'Solaris';\n    const freebsd = 'FreeBSD';\n    const chromeos = 'ChromeOS';\n    const unknown = () => {\n      return nu({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    const nu = info => {\n      const current = info.current;\n      const version = info.version;\n      const isOS = name => () => current === name;\n      return {\n        current,\n        version,\n        isWindows: isOS(windows),\n        isiOS: isOS(ios),\n        isAndroid: isOS(android),\n        isMacOS: isOS(macos),\n        isLinux: isOS(linux),\n        isSolaris: isOS(solaris),\n        isFreeBSD: isOS(freebsd),\n        isChromeOS: isOS(chromeos)\n      };\n    };\n    const OperatingSystem = {\n      unknown,\n      nu,\n      windows: constant(windows),\n      ios: constant(ios),\n      android: constant(android),\n      linux: constant(linux),\n      macos: constant(macos),\n      solaris: constant(solaris),\n      freebsd: constant(freebsd),\n      chromeos: constant(chromeos)\n    };\n\n    const detect$3 = (userAgent, userAgentDataOpt, mediaMatch) => {\n      const browsers = PlatformInfo.browsers();\n      const oses = PlatformInfo.oses();\n      const browser = userAgentDataOpt.bind(userAgentData => detectBrowser$1(browsers, userAgentData)).orThunk(() => detectBrowser(browsers, userAgent)).fold(Browser.unknown, Browser.nu);\n      const os = detectOs(oses, userAgent).fold(OperatingSystem.unknown, OperatingSystem.nu);\n      const deviceType = DeviceType(os, browser, userAgent, mediaMatch);\n      return {\n        browser,\n        os,\n        deviceType\n      };\n    };\n    const PlatformDetection = { detect: detect$3 };\n\n    const mediaMatch = query => window.matchMedia(query).matches;\n    let platform = cached(() => PlatformDetection.detect(navigator.userAgent, Optional.from(navigator.userAgentData), mediaMatch));\n    const detect$2 = () => platform();\n\n    const Dimension = (name, getOffset) => {\n      const set = (element, h) => {\n        if (!isNumber(h) && !h.match(/^[0-9]+$/)) {\n          throw new Error(name + '.set accepts only positive integer values. Value was ' + h);\n        }\n        const dom = element.dom;\n        if (isSupported(dom)) {\n          dom.style[name] = h + 'px';\n        }\n      };\n      const get = element => {\n        const r = getOffset(element);\n        if (r <= 0 || r === null) {\n          const css = get$a(element, name);\n          return parseFloat(css) || 0;\n        }\n        return r;\n      };\n      const getOuter = get;\n      const aggregate = (element, properties) => foldl(properties, (acc, property) => {\n        const val = get$a(element, property);\n        const value = val === undefined ? 0 : parseInt(val, 10);\n        return isNaN(value) ? acc : acc + value;\n      }, 0);\n      const max = (element, value, properties) => {\n        const cumulativeInclusions = aggregate(element, properties);\n        const absoluteMax = value > cumulativeInclusions ? value - cumulativeInclusions : 0;\n        return absoluteMax;\n      };\n      return {\n        set,\n        get,\n        getOuter,\n        aggregate,\n        max\n      };\n    };\n\n    const toNumber = (px, fallback) => toFloat(px).getOr(fallback);\n    const getProp = (element, name, fallback) => toNumber(get$a(element, name), fallback);\n    const calcContentBoxSize = (element, size, upper, lower) => {\n      const paddingUpper = getProp(element, `padding-${ upper }`, 0);\n      const paddingLower = getProp(element, `padding-${ lower }`, 0);\n      const borderUpper = getProp(element, `border-${ upper }-width`, 0);\n      const borderLower = getProp(element, `border-${ lower }-width`, 0);\n      return size - paddingUpper - paddingLower - borderUpper - borderLower;\n    };\n    const getCalculatedWidth = (element, boxSizing) => {\n      const dom = element.dom;\n      const width = dom.getBoundingClientRect().width || dom.offsetWidth;\n      return boxSizing === 'border-box' ? width : calcContentBoxSize(element, width, 'left', 'right');\n    };\n    const getHeight$1 = element => getProp(element, 'height', element.dom.offsetHeight);\n    const getWidth = element => getProp(element, 'width', element.dom.offsetWidth);\n    const getInnerWidth = element => getCalculatedWidth(element, 'content-box');\n\n    const api$2 = Dimension('width', element => element.dom.offsetWidth);\n    const get$9 = element => api$2.get(element);\n    const getOuter$2 = element => api$2.getOuter(element);\n    const getInner = getInnerWidth;\n    const getRuntime$1 = getWidth;\n\n    const addCells = (gridRow, index, cells) => {\n      const existingCells = gridRow.cells;\n      const before = existingCells.slice(0, index);\n      const after = existingCells.slice(index);\n      const newCells = before.concat(cells).concat(after);\n      return setCells(gridRow, newCells);\n    };\n    const addCell = (gridRow, index, cell) => addCells(gridRow, index, [cell]);\n    const mutateCell = (gridRow, index, cell) => {\n      const cells = gridRow.cells;\n      cells[index] = cell;\n    };\n    const setCells = (gridRow, cells) => rowcells(gridRow.element, cells, gridRow.section, gridRow.isNew);\n    const mapCells = (gridRow, f) => {\n      const cells = gridRow.cells;\n      const r = map$1(cells, f);\n      return rowcells(gridRow.element, r, gridRow.section, gridRow.isNew);\n    };\n    const getCell = (gridRow, index) => gridRow.cells[index];\n    const getCellElement = (gridRow, index) => getCell(gridRow, index).element;\n    const cellLength = gridRow => gridRow.cells.length;\n    const extractGridDetails = grid => {\n      const result = partition(grid, row => row.section === 'colgroup');\n      return {\n        rows: result.fail,\n        cols: result.pass\n      };\n    };\n    const clone = (gridRow, cloneRow, cloneCell) => {\n      const newCells = map$1(gridRow.cells, cloneCell);\n      return rowcells(cloneRow(gridRow.element), newCells, gridRow.section, true);\n    };\n\n    const LOCKED_COL_ATTR = 'data-snooker-locked-cols';\n    const getLockedColumnsFromTable = table => getOpt(table, LOCKED_COL_ATTR).bind(lockedColStr => Optional.from(lockedColStr.match(/\\d+/g))).map(lockedCols => mapToObject(lockedCols, always));\n    const getLockedColumnsFromGrid = grid => {\n      const locked = foldl(extractGridDetails(grid).rows, (acc, row) => {\n        each$2(row.cells, (cell, idx) => {\n          if (cell.isLocked) {\n            acc[idx] = true;\n          }\n        });\n        return acc;\n      }, {});\n      const lockedArr = mapToArray(locked, (_val, key) => parseInt(key, 10));\n      return sort$1(lockedArr);\n    };\n\n    const key = (row, column) => {\n      return row + ',' + column;\n    };\n    const getAt = (warehouse, row, column) => Optional.from(warehouse.access[key(row, column)]);\n    const findItem = (warehouse, item, comparator) => {\n      const filtered = filterItems(warehouse, detail => {\n        return comparator(item, detail.element);\n      });\n      return filtered.length > 0 ? Optional.some(filtered[0]) : Optional.none();\n    };\n    const filterItems = (warehouse, predicate) => {\n      const all = bind$2(warehouse.all, r => {\n        return r.cells;\n      });\n      return filter$2(all, predicate);\n    };\n    const generateColumns = rowData => {\n      const columnsGroup = {};\n      let index = 0;\n      each$2(rowData.cells, column => {\n        const colspan = column.colspan;\n        range$1(colspan, columnIndex => {\n          const colIndex = index + columnIndex;\n          columnsGroup[colIndex] = columnext(column.element, colspan, colIndex);\n        });\n        index += colspan;\n      });\n      return columnsGroup;\n    };\n    const generate$1 = list => {\n      const access = {};\n      const cells = [];\n      const tableOpt = head(list).map(rowData => rowData.element).bind(table);\n      const lockedColumns = tableOpt.bind(getLockedColumnsFromTable).getOr({});\n      let maxRows = 0;\n      let maxColumns = 0;\n      let rowCount = 0;\n      const {\n        pass: colgroupRows,\n        fail: rows\n      } = partition(list, rowData => rowData.section === 'colgroup');\n      each$2(rows, rowData => {\n        const currentRow = [];\n        each$2(rowData.cells, rowCell => {\n          let start = 0;\n          while (access[key(rowCount, start)] !== undefined) {\n            start++;\n          }\n          const isLocked = hasNonNullableKey(lockedColumns, start.toString());\n          const current = extended(rowCell.element, rowCell.rowspan, rowCell.colspan, rowCount, start, isLocked);\n          for (let occupiedColumnPosition = 0; occupiedColumnPosition < rowCell.colspan; occupiedColumnPosition++) {\n            for (let occupiedRowPosition = 0; occupiedRowPosition < rowCell.rowspan; occupiedRowPosition++) {\n              const rowPosition = rowCount + occupiedRowPosition;\n              const columnPosition = start + occupiedColumnPosition;\n              const newpos = key(rowPosition, columnPosition);\n              access[newpos] = current;\n              maxColumns = Math.max(maxColumns, columnPosition + 1);\n            }\n          }\n          currentRow.push(current);\n        });\n        maxRows++;\n        cells.push(rowdetail(rowData.element, currentRow, rowData.section));\n        rowCount++;\n      });\n      const {columns, colgroups} = last$2(colgroupRows).map(rowData => {\n        const columns = generateColumns(rowData);\n        const colgroup$1 = colgroup(rowData.element, values(columns));\n        return {\n          colgroups: [colgroup$1],\n          columns\n        };\n      }).getOrThunk(() => ({\n        colgroups: [],\n        columns: {}\n      }));\n      const grid$1 = grid(maxRows, maxColumns);\n      return {\n        grid: grid$1,\n        access,\n        all: cells,\n        columns,\n        colgroups\n      };\n    };\n    const fromTable = table => {\n      const list = fromTable$1(table);\n      return generate$1(list);\n    };\n    const justCells = warehouse => bind$2(warehouse.all, w => w.cells);\n    const justColumns = warehouse => values(warehouse.columns);\n    const hasColumns = warehouse => keys(warehouse.columns).length > 0;\n    const getColumnAt = (warehouse, columnIndex) => Optional.from(warehouse.columns[columnIndex]);\n    const Warehouse = {\n      fromTable,\n      generate: generate$1,\n      getAt,\n      findItem,\n      filterItems,\n      justCells,\n      justColumns,\n      hasColumns,\n      getColumnAt\n    };\n\n    const columns = (warehouse, isValidCell = always) => {\n      const grid = warehouse.grid;\n      const cols = range$1(grid.columns, identity);\n      const rowsArr = range$1(grid.rows, identity);\n      return map$1(cols, col => {\n        const getBlock = () => bind$2(rowsArr, r => Warehouse.getAt(warehouse, r, col).filter(detail => detail.column === col).toArray());\n        const isValid = detail => detail.colspan === 1 && isValidCell(detail.element);\n        const getFallback = () => Warehouse.getAt(warehouse, 0, col);\n        return decide(getBlock, isValid, getFallback);\n      });\n    };\n    const decide = (getBlock, isValid, getFallback) => {\n      const inBlock = getBlock();\n      const validInBlock = find$1(inBlock, isValid);\n      const detailOption = validInBlock.orThunk(() => Optional.from(inBlock[0]).orThunk(getFallback));\n      return detailOption.map(detail => detail.element);\n    };\n    const rows = warehouse => {\n      const grid = warehouse.grid;\n      const rowsArr = range$1(grid.rows, identity);\n      const cols = range$1(grid.columns, identity);\n      return map$1(rowsArr, row => {\n        const getBlock = () => bind$2(cols, c => Warehouse.getAt(warehouse, row, c).filter(detail => detail.row === row).fold(constant([]), detail => [detail]));\n        const isSingle = detail => detail.rowspan === 1;\n        const getFallback = () => Warehouse.getAt(warehouse, row, 0);\n        return decide(getBlock, isSingle, getFallback);\n      });\n    };\n\n    const deduce = (xs, index) => {\n      if (index < 0 || index >= xs.length - 1) {\n        return Optional.none();\n      }\n      const current = xs[index].fold(() => {\n        const rest = reverse(xs.slice(0, index));\n        return findMap(rest, (a, i) => a.map(aa => ({\n          value: aa,\n          delta: i + 1\n        })));\n      }, c => Optional.some({\n        value: c,\n        delta: 0\n      }));\n      const next = xs[index + 1].fold(() => {\n        const rest = xs.slice(index + 1);\n        return findMap(rest, (a, i) => a.map(aa => ({\n          value: aa,\n          delta: i + 1\n        })));\n      }, n => Optional.some({\n        value: n,\n        delta: 1\n      }));\n      return current.bind(c => next.map(n => {\n        const extras = n.delta + c.delta;\n        return Math.abs(n.value - c.value) / extras;\n      }));\n    };\n\n    const onDirection = (isLtr, isRtl) => element => getDirection(element) === 'rtl' ? isRtl : isLtr;\n    const getDirection = element => get$a(element, 'direction') === 'rtl' ? 'rtl' : 'ltr';\n\n    const api$1 = Dimension('height', element => {\n      const dom = element.dom;\n      return inBody(element) ? dom.getBoundingClientRect().height : dom.offsetHeight;\n    });\n    const get$8 = element => api$1.get(element);\n    const getOuter$1 = element => api$1.getOuter(element);\n    const getRuntime = getHeight$1;\n\n    const r = (left, top) => {\n      const translate = (x, y) => r(left + x, top + y);\n      return {\n        left,\n        top,\n        translate\n      };\n    };\n    const SugarPosition = r;\n\n    const boxPosition = dom => {\n      const box = dom.getBoundingClientRect();\n      return SugarPosition(box.left, box.top);\n    };\n    const firstDefinedOrZero = (a, b) => {\n      if (a !== undefined) {\n        return a;\n      } else {\n        return b !== undefined ? b : 0;\n      }\n    };\n    const absolute = element => {\n      const doc = element.dom.ownerDocument;\n      const body = doc.body;\n      const win = doc.defaultView;\n      const html = doc.documentElement;\n      if (body === element.dom) {\n        return SugarPosition(body.offsetLeft, body.offsetTop);\n      }\n      const scrollTop = firstDefinedOrZero(win === null || win === void 0 ? void 0 : win.pageYOffset, html.scrollTop);\n      const scrollLeft = firstDefinedOrZero(win === null || win === void 0 ? void 0 : win.pageXOffset, html.scrollLeft);\n      const clientTop = firstDefinedOrZero(html.clientTop, body.clientTop);\n      const clientLeft = firstDefinedOrZero(html.clientLeft, body.clientLeft);\n      return viewport(element).translate(scrollLeft - clientLeft, scrollTop - clientTop);\n    };\n    const viewport = element => {\n      const dom = element.dom;\n      const doc = dom.ownerDocument;\n      const body = doc.body;\n      if (body === dom) {\n        return SugarPosition(body.offsetLeft, body.offsetTop);\n      }\n      if (!inBody(element)) {\n        return SugarPosition(0, 0);\n      }\n      return boxPosition(dom);\n    };\n\n    const rowInfo = (row, y) => ({\n      row,\n      y\n    });\n    const colInfo = (col, x) => ({\n      col,\n      x\n    });\n    const rtlEdge = cell => {\n      const pos = absolute(cell);\n      return pos.left + getOuter$2(cell);\n    };\n    const ltrEdge = cell => {\n      return absolute(cell).left;\n    };\n    const getLeftEdge = (index, cell) => {\n      return colInfo(index, ltrEdge(cell));\n    };\n    const getRightEdge = (index, cell) => {\n      return colInfo(index, rtlEdge(cell));\n    };\n    const getTop$1 = cell => {\n      return absolute(cell).top;\n    };\n    const getTopEdge = (index, cell) => {\n      return rowInfo(index, getTop$1(cell));\n    };\n    const getBottomEdge = (index, cell) => {\n      return rowInfo(index, getTop$1(cell) + getOuter$1(cell));\n    };\n    const findPositions = (getInnerEdge, getOuterEdge, array) => {\n      if (array.length === 0) {\n        return [];\n      }\n      const lines = map$1(array.slice(1), (cellOption, index) => {\n        return cellOption.map(cell => {\n          return getInnerEdge(index, cell);\n        });\n      });\n      const lastLine = array[array.length - 1].map(cell => {\n        return getOuterEdge(array.length - 1, cell);\n      });\n      return lines.concat([lastLine]);\n    };\n    const negate = step => {\n      return -step;\n    };\n    const height = {\n      delta: identity,\n      positions: optElements => findPositions(getTopEdge, getBottomEdge, optElements),\n      edge: getTop$1\n    };\n    const ltr$1 = {\n      delta: identity,\n      edge: ltrEdge,\n      positions: optElements => findPositions(getLeftEdge, getRightEdge, optElements)\n    };\n    const rtl$1 = {\n      delta: negate,\n      edge: rtlEdge,\n      positions: optElements => findPositions(getRightEdge, getLeftEdge, optElements)\n    };\n    const detect$1 = onDirection(ltr$1, rtl$1);\n    const width = {\n      delta: (amount, table) => detect$1(table).delta(amount, table),\n      positions: (cols, table) => detect$1(table).positions(cols, table),\n      edge: cell => detect$1(cell).edge(cell)\n    };\n\n    const units = {\n      unsupportedLength: [\n        'em',\n        'ex',\n        'cap',\n        'ch',\n        'ic',\n        'rem',\n        'lh',\n        'rlh',\n        'vw',\n        'vh',\n        'vi',\n        'vb',\n        'vmin',\n        'vmax',\n        'cm',\n        'mm',\n        'Q',\n        'in',\n        'pc',\n        'pt',\n        'px'\n      ],\n      fixed: [\n        'px',\n        'pt'\n      ],\n      relative: ['%'],\n      empty: ['']\n    };\n    const pattern = (() => {\n      const decimalDigits = '[0-9]+';\n      const signedInteger = '[+-]?' + decimalDigits;\n      const exponentPart = '[eE]' + signedInteger;\n      const dot = '\\\\.';\n      const opt = input => `(?:${ input })?`;\n      const unsignedDecimalLiteral = [\n        'Infinity',\n        decimalDigits + dot + opt(decimalDigits) + opt(exponentPart),\n        dot + decimalDigits + opt(exponentPart),\n        decimalDigits + opt(exponentPart)\n      ].join('|');\n      const float = `[+-]?(?:${ unsignedDecimalLiteral })`;\n      return new RegExp(`^(${ float })(.*)$`);\n    })();\n    const isUnit = (unit, accepted) => exists(accepted, acc => exists(units[acc], check => unit === check));\n    const parse = (input, accepted) => {\n      const match = Optional.from(pattern.exec(input));\n      return match.bind(array => {\n        const value = Number(array[1]);\n        const unitRaw = array[2];\n        if (isUnit(unitRaw, accepted)) {\n          return Optional.some({\n            value,\n            unit: unitRaw\n          });\n        } else {\n          return Optional.none();\n        }\n      });\n    };\n\n    const rPercentageBasedSizeRegex = /(\\d+(\\.\\d+)?)%/;\n    const rPixelBasedSizeRegex = /(\\d+(\\.\\d+)?)px|em/;\n    const isCol$2 = isTag('col');\n    const getPercentSize = (elm, outerGetter, innerGetter) => {\n      const relativeParent = parentElement(elm).getOrThunk(() => getBody$1(owner(elm)));\n      return outerGetter(elm) / innerGetter(relativeParent) * 100;\n    };\n    const setPixelWidth = (cell, amount) => {\n      set$1(cell, 'width', amount + 'px');\n    };\n    const setPercentageWidth = (cell, amount) => {\n      set$1(cell, 'width', amount + '%');\n    };\n    const setHeight = (cell, amount) => {\n      set$1(cell, 'height', amount + 'px');\n    };\n    const getHeightValue = cell => getRuntime(cell) + 'px';\n    const convert = (cell, number, getter, setter) => {\n      const newSize = table(cell).map(table => {\n        const total = getter(table);\n        return Math.floor(number / 100 * total);\n      }).getOr(number);\n      setter(cell, newSize);\n      return newSize;\n    };\n    const normalizePixelSize = (value, cell, getter, setter) => {\n      const number = parseFloat(value);\n      return endsWith(value, '%') && name(cell) !== 'table' ? convert(cell, number, getter, setter) : number;\n    };\n    const getTotalHeight = cell => {\n      const value = getHeightValue(cell);\n      if (!value) {\n        return get$8(cell);\n      }\n      return normalizePixelSize(value, cell, get$8, setHeight);\n    };\n    const get$7 = (cell, type, f) => {\n      const v = f(cell);\n      const span = getSpan(cell, type);\n      return v / span;\n    };\n    const getRaw$1 = (element, prop) => {\n      return getRaw$2(element, prop).orThunk(() => {\n        return getOpt(element, prop).map(val => val + 'px');\n      });\n    };\n    const getRawWidth$1 = element => getRaw$1(element, 'width');\n    const getRawHeight = element => getRaw$1(element, 'height');\n    const getPercentageWidth = cell => getPercentSize(cell, get$9, getInner);\n    const getPixelWidth$1 = cell => isCol$2(cell) ? get$9(cell) : getRuntime$1(cell);\n    const getHeight = cell => {\n      return get$7(cell, 'rowspan', getTotalHeight);\n    };\n    const getGenericWidth = cell => {\n      const width = getRawWidth$1(cell);\n      return width.bind(w => parse(w, [\n        'fixed',\n        'relative',\n        'empty'\n      ]));\n    };\n    const setGenericWidth = (cell, amount, unit) => {\n      set$1(cell, 'width', amount + unit);\n    };\n    const getPixelTableWidth = table => get$9(table) + 'px';\n    const getPercentTableWidth = table => getPercentSize(table, get$9, getInner) + '%';\n    const isPercentSizing$1 = table => getRawWidth$1(table).exists(size => rPercentageBasedSizeRegex.test(size));\n    const isPixelSizing$1 = table => getRawWidth$1(table).exists(size => rPixelBasedSizeRegex.test(size));\n    const isNoneSizing$1 = table => getRawWidth$1(table).isNone();\n    const percentageBasedSizeRegex = constant(rPercentageBasedSizeRegex);\n\n    const isCol$1 = isTag('col');\n    const getRawW = cell => {\n      return getRawWidth$1(cell).getOrThunk(() => getPixelWidth$1(cell) + 'px');\n    };\n    const getRawH = cell => {\n      return getRawHeight(cell).getOrThunk(() => getHeight(cell) + 'px');\n    };\n    const justCols = warehouse => map$1(Warehouse.justColumns(warehouse), column => Optional.from(column.element));\n    const isValidColumn = cell => {\n      const browser = detect$2().browser;\n      const supportsColWidths = browser.isChromium() || browser.isFirefox();\n      return isCol$1(cell) ? supportsColWidths : true;\n    };\n    const getDimension = (cellOpt, index, backups, filter, getter, fallback) => cellOpt.filter(filter).fold(() => fallback(deduce(backups, index)), cell => getter(cell));\n    const getWidthFrom = (warehouse, table, getWidth, fallback) => {\n      const columnCells = columns(warehouse);\n      const columns$1 = Warehouse.hasColumns(warehouse) ? justCols(warehouse) : columnCells;\n      const backups = [Optional.some(width.edge(table))].concat(map$1(width.positions(columnCells, table), pos => pos.map(p => p.x)));\n      const colFilter = not(hasColspan);\n      return map$1(columns$1, (cellOption, c) => {\n        return getDimension(cellOption, c, backups, colFilter, column => {\n          if (isValidColumn(column)) {\n            return getWidth(column);\n          } else {\n            const cell = bindFrom(columnCells[c], identity);\n            return getDimension(cell, c, backups, colFilter, cell => fallback(Optional.some(get$9(cell))), fallback);\n          }\n        }, fallback);\n      });\n    };\n    const getDeduced = deduced => {\n      return deduced.map(d => {\n        return d + 'px';\n      }).getOr('');\n    };\n    const getRawWidths = (warehouse, table) => {\n      return getWidthFrom(warehouse, table, getRawW, getDeduced);\n    };\n    const getPercentageWidths = (warehouse, table, tableSize) => {\n      return getWidthFrom(warehouse, table, getPercentageWidth, deduced => {\n        return deduced.fold(() => {\n          return tableSize.minCellWidth();\n        }, cellWidth => {\n          return cellWidth / tableSize.pixelWidth() * 100;\n        });\n      });\n    };\n    const getPixelWidths = (warehouse, table, tableSize) => {\n      return getWidthFrom(warehouse, table, getPixelWidth$1, deduced => {\n        return deduced.getOrThunk(tableSize.minCellWidth);\n      });\n    };\n    const getHeightFrom = (warehouse, table, direction, getHeight, fallback) => {\n      const rows$1 = rows(warehouse);\n      const backups = [Optional.some(direction.edge(table))].concat(map$1(direction.positions(rows$1, table), pos => pos.map(p => p.y)));\n      return map$1(rows$1, (cellOption, c) => {\n        return getDimension(cellOption, c, backups, not(hasRowspan), getHeight, fallback);\n      });\n    };\n    const getPixelHeights = (warehouse, table, direction) => {\n      return getHeightFrom(warehouse, table, direction, getHeight, deduced => {\n        return deduced.getOrThunk(minHeight);\n      });\n    };\n    const getRawHeights = (warehouse, table, direction) => {\n      return getHeightFrom(warehouse, table, direction, getRawH, getDeduced);\n    };\n\n    const widthLookup = (table, getter) => () => {\n      if (inBody(table)) {\n        return getter(table);\n      } else {\n        return parseFloat(getRaw$2(table, 'width').getOr('0'));\n      }\n    };\n    const noneSize = table => {\n      const getWidth = widthLookup(table, get$9);\n      const zero = constant(0);\n      const getWidths = (warehouse, tableSize) => getPixelWidths(warehouse, table, tableSize);\n      return {\n        width: getWidth,\n        pixelWidth: getWidth,\n        getWidths,\n        getCellDelta: zero,\n        singleColumnWidth: constant([0]),\n        minCellWidth: zero,\n        setElementWidth: noop,\n        adjustTableWidth: noop,\n        isRelative: true,\n        label: 'none'\n      };\n    };\n    const percentageSize = table => {\n      const getFloatWidth = widthLookup(table, elem => parseFloat(getPercentTableWidth(elem)));\n      const getWidth = widthLookup(table, get$9);\n      const getCellDelta = delta => delta / getWidth() * 100;\n      const singleColumnWidth = (w, _delta) => [100 - w];\n      const minCellWidth = () => minWidth() / getWidth() * 100;\n      const adjustTableWidth = delta => {\n        const currentWidth = getFloatWidth();\n        const change = delta / 100 * currentWidth;\n        const newWidth = currentWidth + change;\n        setPercentageWidth(table, newWidth);\n      };\n      const getWidths = (warehouse, tableSize) => getPercentageWidths(warehouse, table, tableSize);\n      return {\n        width: getFloatWidth,\n        pixelWidth: getWidth,\n        getWidths,\n        getCellDelta,\n        singleColumnWidth,\n        minCellWidth,\n        setElementWidth: setPercentageWidth,\n        adjustTableWidth,\n        isRelative: true,\n        label: 'percent'\n      };\n    };\n    const pixelSize = table => {\n      const getWidth = widthLookup(table, get$9);\n      const getCellDelta = identity;\n      const singleColumnWidth = (w, delta) => {\n        const newNext = Math.max(minWidth(), w + delta);\n        return [newNext - w];\n      };\n      const adjustTableWidth = delta => {\n        const newWidth = getWidth() + delta;\n        setPixelWidth(table, newWidth);\n      };\n      const getWidths = (warehouse, tableSize) => getPixelWidths(warehouse, table, tableSize);\n      return {\n        width: getWidth,\n        pixelWidth: getWidth,\n        getWidths,\n        getCellDelta,\n        singleColumnWidth,\n        minCellWidth: minWidth,\n        setElementWidth: setPixelWidth,\n        adjustTableWidth,\n        isRelative: false,\n        label: 'pixel'\n      };\n    };\n    const chooseSize = (element, width) => {\n      const percentMatch = percentageBasedSizeRegex().exec(width);\n      if (percentMatch !== null) {\n        return percentageSize(element);\n      } else {\n        return pixelSize(element);\n      }\n    };\n    const getTableSize = table => {\n      const width = getRawWidth$1(table);\n      return width.fold(() => noneSize(table), w => chooseSize(table, w));\n    };\n    const TableSize = {\n      getTableSize,\n      pixelSize,\n      percentageSize,\n      noneSize\n    };\n\n    const statsStruct = (minRow, minCol, maxRow, maxCol, allCells, selectedCells) => ({\n      minRow,\n      minCol,\n      maxRow,\n      maxCol,\n      allCells,\n      selectedCells\n    });\n    const findSelectedStats = (house, isSelected) => {\n      const totalColumns = house.grid.columns;\n      const totalRows = house.grid.rows;\n      let minRow = totalRows;\n      let minCol = totalColumns;\n      let maxRow = 0;\n      let maxCol = 0;\n      const allCells = [];\n      const selectedCells = [];\n      each$1(house.access, detail => {\n        allCells.push(detail);\n        if (isSelected(detail)) {\n          selectedCells.push(detail);\n          const startRow = detail.row;\n          const endRow = startRow + detail.rowspan - 1;\n          const startCol = detail.column;\n          const endCol = startCol + detail.colspan - 1;\n          if (startRow < minRow) {\n            minRow = startRow;\n          } else if (endRow > maxRow) {\n            maxRow = endRow;\n          }\n          if (startCol < minCol) {\n            minCol = startCol;\n          } else if (endCol > maxCol) {\n            maxCol = endCol;\n          }\n        }\n      });\n      return statsStruct(minRow, minCol, maxRow, maxCol, allCells, selectedCells);\n    };\n    const makeCell = (list, seenSelected, rowIndex) => {\n      const row = list[rowIndex].element;\n      const td = SugarElement.fromTag('td');\n      append$1(td, SugarElement.fromTag('br'));\n      const f = seenSelected ? append$1 : prepend;\n      f(row, td);\n    };\n    const fillInGaps = (list, house, stats, isSelected) => {\n      const rows = filter$2(list, row => row.section !== 'colgroup');\n      const totalColumns = house.grid.columns;\n      const totalRows = house.grid.rows;\n      for (let i = 0; i < totalRows; i++) {\n        let seenSelected = false;\n        for (let j = 0; j < totalColumns; j++) {\n          if (!(i < stats.minRow || i > stats.maxRow || j < stats.minCol || j > stats.maxCol)) {\n            const needCell = Warehouse.getAt(house, i, j).filter(isSelected).isNone();\n            if (needCell) {\n              makeCell(rows, seenSelected, i);\n            } else {\n              seenSelected = true;\n            }\n          }\n        }\n      }\n    };\n    const clean = (replica, stats, house, widthDelta) => {\n      each$1(house.columns, col => {\n        if (col.column < stats.minCol || col.column > stats.maxCol) {\n          remove$6(col.element);\n        }\n      });\n      const emptyRows = filter$2(firstLayer(replica, 'tr'), row => row.dom.childElementCount === 0);\n      each$2(emptyRows, remove$6);\n      if (stats.minCol === stats.maxCol || stats.minRow === stats.maxRow) {\n        each$2(firstLayer(replica, 'th,td'), cell => {\n          remove$7(cell, 'rowspan');\n          remove$7(cell, 'colspan');\n        });\n      }\n      remove$7(replica, LOCKED_COL_ATTR);\n      remove$7(replica, 'data-snooker-col-series');\n      const tableSize = TableSize.getTableSize(replica);\n      tableSize.adjustTableWidth(widthDelta);\n    };\n    const getTableWidthDelta = (table, warehouse, tableSize, stats) => {\n      if (stats.minCol === 0 && warehouse.grid.columns === stats.maxCol + 1) {\n        return 0;\n      }\n      const colWidths = getPixelWidths(warehouse, table, tableSize);\n      const allColsWidth = foldl(colWidths, (acc, width) => acc + width, 0);\n      const selectedColsWidth = foldl(colWidths.slice(stats.minCol, stats.maxCol + 1), (acc, width) => acc + width, 0);\n      const newWidth = selectedColsWidth / allColsWidth * tableSize.pixelWidth();\n      const delta = newWidth - tableSize.pixelWidth();\n      return tableSize.getCellDelta(delta);\n    };\n    const extract$1 = (table, selectedSelector) => {\n      const isSelected = detail => is$2(detail.element, selectedSelector);\n      const replica = deep(table);\n      const list = fromTable$1(replica);\n      const tableSize = TableSize.getTableSize(table);\n      const replicaHouse = Warehouse.generate(list);\n      const replicaStats = findSelectedStats(replicaHouse, isSelected);\n      const selector = 'th:not(' + selectedSelector + ')' + ',td:not(' + selectedSelector + ')';\n      const unselectedCells = filterFirstLayer(replica, 'th,td', cell => is$2(cell, selector));\n      each$2(unselectedCells, remove$6);\n      fillInGaps(list, replicaHouse, replicaStats, isSelected);\n      const house = Warehouse.fromTable(table);\n      const widthDelta = getTableWidthDelta(table, house, tableSize, replicaStats);\n      clean(replica, replicaStats, replicaHouse, widthDelta);\n      return replica;\n    };\n\n    const nbsp = '\\xA0';\n\n    const NodeValue = (is, name) => {\n      const get = element => {\n        if (!is(element)) {\n          throw new Error('Can only get ' + name + ' value of a ' + name + ' node');\n        }\n        return getOption(element).getOr('');\n      };\n      const getOption = element => is(element) ? Optional.from(element.dom.nodeValue) : Optional.none();\n      const set = (element, value) => {\n        if (!is(element)) {\n          throw new Error('Can only set raw ' + name + ' value of a ' + name + ' node');\n        }\n        element.dom.nodeValue = value;\n      };\n      return {\n        get,\n        getOption,\n        set\n      };\n    };\n\n    const api = NodeValue(isText, 'text');\n    const get$6 = element => api.get(element);\n    const getOption = element => api.getOption(element);\n    const set = (element, value) => api.set(element, value);\n\n    const getEnd = element => name(element) === 'img' ? 1 : getOption(element).fold(() => children$2(element).length, v => v.length);\n    const isTextNodeWithCursorPosition = el => getOption(el).filter(text => text.trim().length !== 0 || text.indexOf(nbsp) > -1).isSome();\n    const isContentEditableFalse = elem => isHTMLElement(elem) && get$b(elem, 'contenteditable') === 'false';\n    const elementsWithCursorPosition = [\n      'img',\n      'br'\n    ];\n    const isCursorPosition = elem => {\n      const hasCursorPosition = isTextNodeWithCursorPosition(elem);\n      return hasCursorPosition || contains$2(elementsWithCursorPosition, name(elem)) || isContentEditableFalse(elem);\n    };\n\n    const first = element => descendant$1(element, isCursorPosition);\n    const last$1 = element => descendantRtl(element, isCursorPosition);\n    const descendantRtl = (scope, predicate) => {\n      const descend = element => {\n        const children = children$2(element);\n        for (let i = children.length - 1; i >= 0; i--) {\n          const child = children[i];\n          if (predicate(child)) {\n            return Optional.some(child);\n          }\n          const res = descend(child);\n          if (res.isSome()) {\n            return res;\n          }\n        }\n        return Optional.none();\n      };\n      return descend(scope);\n    };\n\n    const transferableAttributes = {\n      scope: [\n        'row',\n        'col'\n      ]\n    };\n    const createCell = doc => () => {\n      const td = SugarElement.fromTag('td', doc.dom);\n      append$1(td, SugarElement.fromTag('br', doc.dom));\n      return td;\n    };\n    const createCol = doc => () => {\n      return SugarElement.fromTag('col', doc.dom);\n    };\n    const createColgroup = doc => () => {\n      return SugarElement.fromTag('colgroup', doc.dom);\n    };\n    const createRow$1 = doc => () => {\n      return SugarElement.fromTag('tr', doc.dom);\n    };\n    const replace$1 = (cell, tag, attrs) => {\n      const replica = copy$2(cell, tag);\n      each$1(attrs, (v, k) => {\n        if (v === null) {\n          remove$7(replica, k);\n        } else {\n          set$2(replica, k, v);\n        }\n      });\n      return replica;\n    };\n    const pasteReplace = cell => {\n      return cell;\n    };\n    const cloneFormats = (oldCell, newCell, formats) => {\n      const first$1 = first(oldCell);\n      return first$1.map(firstText => {\n        const formatSelector = formats.join(',');\n        const parents = ancestors$3(firstText, formatSelector, element => {\n          return eq$1(element, oldCell);\n        });\n        return foldr(parents, (last, parent) => {\n          const clonedFormat = shallow(parent);\n          append$1(last, clonedFormat);\n          return clonedFormat;\n        }, newCell);\n      }).getOr(newCell);\n    };\n    const cloneAppropriateAttributes = (original, clone) => {\n      each$1(transferableAttributes, (validAttributes, attributeName) => getOpt(original, attributeName).filter(attribute => contains$2(validAttributes, attribute)).each(attribute => set$2(clone, attributeName, attribute)));\n    };\n    const cellOperations = (mutate, doc, formatsToClone) => {\n      const cloneCss = (prev, clone) => {\n        copy$1(prev.element, clone);\n        remove$5(clone, 'height');\n        if (prev.colspan !== 1) {\n          remove$5(clone, 'width');\n        }\n      };\n      const newCell = prev => {\n        const td = SugarElement.fromTag(name(prev.element), doc.dom);\n        const formats = formatsToClone.getOr([\n          'strong',\n          'em',\n          'b',\n          'i',\n          'span',\n          'font',\n          'h1',\n          'h2',\n          'h3',\n          'h4',\n          'h5',\n          'h6',\n          'p',\n          'div'\n        ]);\n        const lastNode = formats.length > 0 ? cloneFormats(prev.element, td, formats) : td;\n        append$1(lastNode, SugarElement.fromTag('br'));\n        cloneCss(prev, td);\n        cloneAppropriateAttributes(prev.element, td);\n        mutate(prev.element, td);\n        return td;\n      };\n      const newCol = prev => {\n        const col = SugarElement.fromTag(name(prev.element), doc.dom);\n        cloneCss(prev, col);\n        mutate(prev.element, col);\n        return col;\n      };\n      return {\n        col: newCol,\n        colgroup: createColgroup(doc),\n        row: createRow$1(doc),\n        cell: newCell,\n        replace: replace$1,\n        colGap: createCol(doc),\n        gap: createCell(doc)\n      };\n    };\n    const paste$1 = doc => {\n      return {\n        col: createCol(doc),\n        colgroup: createColgroup(doc),\n        row: createRow$1(doc),\n        cell: createCell(doc),\n        replace: pasteReplace,\n        colGap: createCol(doc),\n        gap: createCell(doc)\n      };\n    };\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      return children$2(SugarElement.fromDom(div));\n    };\n    const fromDom = nodes => map$1(nodes, SugarElement.fromDom);\n\n    const option = name => editor => editor.options.get(name);\n    const defaultWidth = '100%';\n    const getPixelForcedWidth = editor => {\n      var _a;\n      const dom = editor.dom;\n      const parentBlock = (_a = dom.getParent(editor.selection.getStart(), dom.isBlock)) !== null && _a !== void 0 ? _a : editor.getBody();\n      return getInner(SugarElement.fromDom(parentBlock)) + 'px';\n    };\n    const determineDefaultTableStyles = (editor, defaultStyles) => {\n      if (isTableResponsiveForced(editor) || !shouldStyleWithCss(editor)) {\n        return defaultStyles;\n      } else if (isTablePixelsForced(editor)) {\n        return {\n          ...defaultStyles,\n          width: getPixelForcedWidth(editor)\n        };\n      } else {\n        return {\n          ...defaultStyles,\n          width: defaultWidth\n        };\n      }\n    };\n    const determineDefaultTableAttributes = (editor, defaultAttributes) => {\n      if (isTableResponsiveForced(editor) || shouldStyleWithCss(editor)) {\n        return defaultAttributes;\n      } else if (isTablePixelsForced(editor)) {\n        return {\n          ...defaultAttributes,\n          width: getPixelForcedWidth(editor)\n        };\n      } else {\n        return {\n          ...defaultAttributes,\n          width: defaultWidth\n        };\n      }\n    };\n    const register = editor => {\n      const registerOption = editor.options.register;\n      registerOption('table_clone_elements', { processor: 'string[]' });\n      registerOption('table_use_colgroups', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_header_type', {\n        processor: value => {\n          const valid = contains$2([\n            'section',\n            'cells',\n            'sectionCells',\n            'auto'\n          ], value);\n          return valid ? {\n            value,\n            valid\n          } : {\n            valid: false,\n            message: 'Must be one of: section, cells, sectionCells or auto.'\n          };\n        },\n        default: 'section'\n      });\n      registerOption('table_sizing_mode', {\n        processor: 'string',\n        default: 'auto'\n      });\n      registerOption('table_default_attributes', {\n        processor: 'object',\n        default: { border: '1' }\n      });\n      registerOption('table_default_styles', {\n        processor: 'object',\n        default: { 'border-collapse': 'collapse' }\n      });\n      registerOption('table_column_resizing', {\n        processor: value => {\n          const valid = contains$2([\n            'preservetable',\n            'resizetable'\n          ], value);\n          return valid ? {\n            value,\n            valid\n          } : {\n            valid: false,\n            message: 'Must be preservetable, or resizetable.'\n          };\n        },\n        default: 'preservetable'\n      });\n      registerOption('table_resize_bars', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_style_by_css', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_merge_content_on_paste', {\n        processor: 'boolean',\n        default: true\n      });\n    };\n    const getTableCloneElements = editor => {\n      return Optional.from(editor.options.get('table_clone_elements'));\n    };\n    const hasTableObjectResizing = editor => {\n      const objectResizing = editor.options.get('object_resizing');\n      return contains$2(objectResizing.split(','), 'table');\n    };\n    const getTableHeaderType = option('table_header_type');\n    const getTableColumnResizingBehaviour = option('table_column_resizing');\n    const isPreserveTableColumnResizing = editor => getTableColumnResizingBehaviour(editor) === 'preservetable';\n    const isResizeTableColumnResizing = editor => getTableColumnResizingBehaviour(editor) === 'resizetable';\n    const getTableSizingMode = option('table_sizing_mode');\n    const isTablePercentagesForced = editor => getTableSizingMode(editor) === 'relative';\n    const isTablePixelsForced = editor => getTableSizingMode(editor) === 'fixed';\n    const isTableResponsiveForced = editor => getTableSizingMode(editor) === 'responsive';\n    const hasTableResizeBars = option('table_resize_bars');\n    const shouldStyleWithCss = option('table_style_by_css');\n    const shouldMergeContentOnPaste = option('table_merge_content_on_paste');\n    const getTableDefaultAttributes = editor => {\n      const options = editor.options;\n      const defaultAttributes = options.get('table_default_attributes');\n      return options.isSet('table_default_attributes') ? defaultAttributes : determineDefaultTableAttributes(editor, defaultAttributes);\n    };\n    const getTableDefaultStyles = editor => {\n      const options = editor.options;\n      const defaultStyles = options.get('table_default_styles');\n      return options.isSet('table_default_styles') ? defaultStyles : determineDefaultTableStyles(editor, defaultStyles);\n    };\n    const tableUseColumnGroup = option('table_use_colgroups');\n\n    const closest = target => closest$1(target, '[contenteditable]');\n    const isEditable$1 = (element, assumeEditable = false) => {\n      if (inBody(element)) {\n        return element.dom.isContentEditable;\n      } else {\n        return closest(element).fold(constant(assumeEditable), editable => getRaw(editable) === 'true');\n      }\n    };\n    const getRaw = element => element.dom.contentEditable;\n\n    const getBody = editor => SugarElement.fromDom(editor.getBody());\n    const getIsRoot = editor => element => eq$1(element, getBody(editor));\n    const removeDataStyle = table => {\n      remove$7(table, 'data-mce-style');\n      const removeStyleAttribute = element => remove$7(element, 'data-mce-style');\n      each$2(cells$1(table), removeStyleAttribute);\n      each$2(columns$1(table), removeStyleAttribute);\n      each$2(rows$1(table), removeStyleAttribute);\n    };\n    const getSelectionStart = editor => SugarElement.fromDom(editor.selection.getStart());\n    const getPixelWidth = elm => elm.getBoundingClientRect().width;\n    const getPixelHeight = elm => elm.getBoundingClientRect().height;\n    const getRawWidth = (editor, elm) => {\n      const raw = editor.dom.getStyle(elm, 'width') || editor.dom.getAttrib(elm, 'width');\n      return Optional.from(raw).filter(isNotEmpty);\n    };\n    const isPercentage$1 = value => /^(\\d+(\\.\\d+)?)%$/.test(value);\n    const isPixel = value => /^(\\d+(\\.\\d+)?)px$/.test(value);\n    const isInEditableContext$1 = cell => closest$2(cell, isTag('table')).exists(isEditable$1);\n\n    const inSelection = (bounds, detail) => {\n      const leftEdge = detail.column;\n      const rightEdge = detail.column + detail.colspan - 1;\n      const topEdge = detail.row;\n      const bottomEdge = detail.row + detail.rowspan - 1;\n      return leftEdge <= bounds.finishCol && rightEdge >= bounds.startCol && (topEdge <= bounds.finishRow && bottomEdge >= bounds.startRow);\n    };\n    const isWithin = (bounds, detail) => {\n      return detail.column >= bounds.startCol && detail.column + detail.colspan - 1 <= bounds.finishCol && detail.row >= bounds.startRow && detail.row + detail.rowspan - 1 <= bounds.finishRow;\n    };\n    const isRectangular = (warehouse, bounds) => {\n      let isRect = true;\n      const detailIsWithin = curry(isWithin, bounds);\n      for (let i = bounds.startRow; i <= bounds.finishRow; i++) {\n        for (let j = bounds.startCol; j <= bounds.finishCol; j++) {\n          isRect = isRect && Warehouse.getAt(warehouse, i, j).exists(detailIsWithin);\n        }\n      }\n      return isRect ? Optional.some(bounds) : Optional.none();\n    };\n\n    const getBounds = (detailA, detailB) => {\n      return bounds(Math.min(detailA.row, detailB.row), Math.min(detailA.column, detailB.column), Math.max(detailA.row + detailA.rowspan - 1, detailB.row + detailB.rowspan - 1), Math.max(detailA.column + detailA.colspan - 1, detailB.column + detailB.colspan - 1));\n    };\n    const getAnyBox = (warehouse, startCell, finishCell) => {\n      const startCoords = Warehouse.findItem(warehouse, startCell, eq$1);\n      const finishCoords = Warehouse.findItem(warehouse, finishCell, eq$1);\n      return startCoords.bind(sc => {\n        return finishCoords.map(fc => {\n          return getBounds(sc, fc);\n        });\n      });\n    };\n    const getBox$1 = (warehouse, startCell, finishCell) => {\n      return getAnyBox(warehouse, startCell, finishCell).bind(bounds => {\n        return isRectangular(warehouse, bounds);\n      });\n    };\n\n    const moveBy$1 = (warehouse, cell, row, column) => {\n      return Warehouse.findItem(warehouse, cell, eq$1).bind(detail => {\n        const startRow = row > 0 ? detail.row + detail.rowspan - 1 : detail.row;\n        const startCol = column > 0 ? detail.column + detail.colspan - 1 : detail.column;\n        const dest = Warehouse.getAt(warehouse, startRow + row, startCol + column);\n        return dest.map(d => {\n          return d.element;\n        });\n      });\n    };\n    const intercepts$1 = (warehouse, start, finish) => {\n      return getAnyBox(warehouse, start, finish).map(bounds => {\n        const inside = Warehouse.filterItems(warehouse, curry(inSelection, bounds));\n        return map$1(inside, detail => {\n          return detail.element;\n        });\n      });\n    };\n    const parentCell = (warehouse, innerCell) => {\n      const isContainedBy = (c1, c2) => {\n        return contains$1(c2, c1);\n      };\n      return Warehouse.findItem(warehouse, innerCell, isContainedBy).map(detail => {\n        return detail.element;\n      });\n    };\n\n    const moveBy = (cell, deltaRow, deltaColumn) => {\n      return table(cell).bind(table => {\n        const warehouse = getWarehouse(table);\n        return moveBy$1(warehouse, cell, deltaRow, deltaColumn);\n      });\n    };\n    const intercepts = (table, first, last) => {\n      const warehouse = getWarehouse(table);\n      return intercepts$1(warehouse, first, last);\n    };\n    const nestedIntercepts = (table, first, firstTable, last, lastTable) => {\n      const warehouse = getWarehouse(table);\n      const optStartCell = eq$1(table, firstTable) ? Optional.some(first) : parentCell(warehouse, first);\n      const optLastCell = eq$1(table, lastTable) ? Optional.some(last) : parentCell(warehouse, last);\n      return optStartCell.bind(startCell => optLastCell.bind(lastCell => intercepts$1(warehouse, startCell, lastCell)));\n    };\n    const getBox = (table, first, last) => {\n      const warehouse = getWarehouse(table);\n      return getBox$1(warehouse, first, last);\n    };\n    const getWarehouse = Warehouse.fromTable;\n\n    var TagBoundaries = [\n      'body',\n      'p',\n      'div',\n      'article',\n      'aside',\n      'figcaption',\n      'figure',\n      'footer',\n      'header',\n      'nav',\n      'section',\n      'ol',\n      'ul',\n      'li',\n      'table',\n      'thead',\n      'tbody',\n      'tfoot',\n      'caption',\n      'tr',\n      'td',\n      'th',\n      'h1',\n      'h2',\n      'h3',\n      'h4',\n      'h5',\n      'h6',\n      'blockquote',\n      'pre',\n      'address'\n    ];\n\n    var DomUniverse = () => {\n      const clone = element => {\n        return SugarElement.fromDom(element.dom.cloneNode(false));\n      };\n      const document = element => documentOrOwner(element).dom;\n      const isBoundary = element => {\n        if (!isElement(element)) {\n          return false;\n        }\n        if (name(element) === 'body') {\n          return true;\n        }\n        return contains$2(TagBoundaries, name(element));\n      };\n      const isEmptyTag = element => {\n        if (!isElement(element)) {\n          return false;\n        }\n        return contains$2([\n          'br',\n          'img',\n          'hr',\n          'input'\n        ], name(element));\n      };\n      const isNonEditable = element => isElement(element) && get$b(element, 'contenteditable') === 'false';\n      const comparePosition = (element, other) => {\n        return element.dom.compareDocumentPosition(other.dom);\n      };\n      const copyAttributesTo = (source, destination) => {\n        const as = clone$2(source);\n        setAll$1(destination, as);\n      };\n      const isSpecial = element => {\n        const tag = name(element);\n        return contains$2([\n          'script',\n          'noscript',\n          'iframe',\n          'noframes',\n          'noembed',\n          'title',\n          'style',\n          'textarea',\n          'xmp'\n        ], tag);\n      };\n      const getLanguage = element => isElement(element) ? getOpt(element, 'lang') : Optional.none();\n      return {\n        up: constant({\n          selector: ancestor$1,\n          closest: closest$1,\n          predicate: ancestor$2,\n          all: parents\n        }),\n        down: constant({\n          selector: descendants,\n          predicate: descendants$1\n        }),\n        styles: constant({\n          get: get$a,\n          getRaw: getRaw$2,\n          set: set$1,\n          remove: remove$5\n        }),\n        attrs: constant({\n          get: get$b,\n          set: set$2,\n          remove: remove$7,\n          copyTo: copyAttributesTo\n        }),\n        insert: constant({\n          before: before$3,\n          after: after$5,\n          afterAll: after$4,\n          append: append$1,\n          appendAll: append,\n          prepend: prepend,\n          wrap: wrap\n        }),\n        remove: constant({\n          unwrap: unwrap,\n          remove: remove$6\n        }),\n        create: constant({\n          nu: SugarElement.fromTag,\n          clone,\n          text: SugarElement.fromText\n        }),\n        query: constant({\n          comparePosition,\n          prevSibling: prevSibling,\n          nextSibling: nextSibling\n        }),\n        property: constant({\n          children: children$2,\n          name: name,\n          parent: parent,\n          document,\n          isText: isText,\n          isComment: isComment,\n          isElement: isElement,\n          isSpecial,\n          getLanguage,\n          getText: get$6,\n          setText: set,\n          isBoundary,\n          isEmptyTag,\n          isNonEditable\n        }),\n        eq: eq$1,\n        is: is$1\n      };\n    };\n\n    const all = (universe, look, elements, f) => {\n      const head = elements[0];\n      const tail = elements.slice(1);\n      return f(universe, look, head, tail);\n    };\n    const oneAll = (universe, look, elements) => {\n      return elements.length > 0 ? all(universe, look, elements, unsafeOne) : Optional.none();\n    };\n    const unsafeOne = (universe, look, head, tail) => {\n      const start = look(universe, head);\n      return foldr(tail, (b, a) => {\n        const current = look(universe, a);\n        return commonElement(universe, b, current);\n      }, start);\n    };\n    const commonElement = (universe, start, end) => {\n      return start.bind(s => {\n        return end.filter(curry(universe.eq, s));\n      });\n    };\n\n    const eq = (universe, item) => {\n      return curry(universe.eq, item);\n    };\n    const ancestors$2 = (universe, start, end, isRoot = never) => {\n      const ps1 = [start].concat(universe.up().all(start));\n      const ps2 = [end].concat(universe.up().all(end));\n      const prune = path => {\n        const index = findIndex(path, isRoot);\n        return index.fold(() => {\n          return path;\n        }, ind => {\n          return path.slice(0, ind + 1);\n        });\n      };\n      const pruned1 = prune(ps1);\n      const pruned2 = prune(ps2);\n      const shared = find$1(pruned1, x => {\n        return exists(pruned2, eq(universe, x));\n      });\n      return {\n        firstpath: pruned1,\n        secondpath: pruned2,\n        shared\n      };\n    };\n\n    const sharedOne$1 = oneAll;\n    const ancestors$1 = ancestors$2;\n\n    const universe$3 = DomUniverse();\n    const sharedOne = (look, elements) => {\n      return sharedOne$1(universe$3, (_universe, element) => {\n        return look(element);\n      }, elements);\n    };\n    const ancestors = (start, finish, isRoot) => {\n      return ancestors$1(universe$3, start, finish, isRoot);\n    };\n\n    const lookupTable = container => {\n      return ancestor$1(container, 'table');\n    };\n    const identify = (start, finish, isRoot) => {\n      const getIsRoot = rootTable => {\n        return element => {\n          return isRoot !== undefined && isRoot(element) || eq$1(element, rootTable);\n        };\n      };\n      if (eq$1(start, finish)) {\n        return Optional.some({\n          boxes: Optional.some([start]),\n          start,\n          finish\n        });\n      } else {\n        return lookupTable(start).bind(startTable => {\n          return lookupTable(finish).bind(finishTable => {\n            if (eq$1(startTable, finishTable)) {\n              return Optional.some({\n                boxes: intercepts(startTable, start, finish),\n                start,\n                finish\n              });\n            } else if (contains$1(startTable, finishTable)) {\n              const ancestorCells = ancestors$3(finish, 'td,th', getIsRoot(startTable));\n              const finishCell = ancestorCells.length > 0 ? ancestorCells[ancestorCells.length - 1] : finish;\n              return Optional.some({\n                boxes: nestedIntercepts(startTable, start, startTable, finish, finishTable),\n                start,\n                finish: finishCell\n              });\n            } else if (contains$1(finishTable, startTable)) {\n              const ancestorCells = ancestors$3(start, 'td,th', getIsRoot(finishTable));\n              const startCell = ancestorCells.length > 0 ? ancestorCells[ancestorCells.length - 1] : start;\n              return Optional.some({\n                boxes: nestedIntercepts(finishTable, start, startTable, finish, finishTable),\n                start,\n                finish: startCell\n              });\n            } else {\n              return ancestors(start, finish).shared.bind(lca => {\n                return closest$1(lca, 'table', isRoot).bind(lcaTable => {\n                  const finishAncestorCells = ancestors$3(finish, 'td,th', getIsRoot(lcaTable));\n                  const finishCell = finishAncestorCells.length > 0 ? finishAncestorCells[finishAncestorCells.length - 1] : finish;\n                  const startAncestorCells = ancestors$3(start, 'td,th', getIsRoot(lcaTable));\n                  const startCell = startAncestorCells.length > 0 ? startAncestorCells[startAncestorCells.length - 1] : start;\n                  return Optional.some({\n                    boxes: nestedIntercepts(lcaTable, start, startTable, finish, finishTable),\n                    start: startCell,\n                    finish: finishCell\n                  });\n                });\n              });\n            }\n          });\n        });\n      }\n    };\n    const retrieve$1 = (container, selector) => {\n      const sels = descendants(container, selector);\n      return sels.length > 0 ? Optional.some(sels) : Optional.none();\n    };\n    const getLast = (boxes, lastSelectedSelector) => {\n      return find$1(boxes, box => {\n        return is$2(box, lastSelectedSelector);\n      });\n    };\n    const getEdges = (container, firstSelectedSelector, lastSelectedSelector) => {\n      return descendant(container, firstSelectedSelector).bind(first => {\n        return descendant(container, lastSelectedSelector).bind(last => {\n          return sharedOne(lookupTable, [\n            first,\n            last\n          ]).map(table => {\n            return {\n              first,\n              last,\n              table\n            };\n          });\n        });\n      });\n    };\n    const expandTo = (finish, firstSelectedSelector) => {\n      return ancestor$1(finish, 'table').bind(table => {\n        return descendant(table, firstSelectedSelector).bind(start => {\n          return identify(start, finish).bind(identified => {\n            return identified.boxes.map(boxes => {\n              return {\n                boxes,\n                start: identified.start,\n                finish: identified.finish\n              };\n            });\n          });\n        });\n      });\n    };\n    const shiftSelection = (boxes, deltaRow, deltaColumn, firstSelectedSelector, lastSelectedSelector) => {\n      return getLast(boxes, lastSelectedSelector).bind(last => {\n        return moveBy(last, deltaRow, deltaColumn).bind(finish => {\n          return expandTo(finish, firstSelectedSelector);\n        });\n      });\n    };\n\n    const retrieve = (container, selector) => {\n      return retrieve$1(container, selector);\n    };\n    const retrieveBox = (container, firstSelectedSelector, lastSelectedSelector) => {\n      return getEdges(container, firstSelectedSelector, lastSelectedSelector).bind(edges => {\n        const isRoot = ancestor => {\n          return eq$1(container, ancestor);\n        };\n        const sectionSelector = 'thead,tfoot,tbody,table';\n        const firstAncestor = ancestor$1(edges.first, sectionSelector, isRoot);\n        const lastAncestor = ancestor$1(edges.last, sectionSelector, isRoot);\n        return firstAncestor.bind(fA => {\n          return lastAncestor.bind(lA => {\n            return eq$1(fA, lA) ? getBox(edges.table, edges.first, edges.last) : Optional.none();\n          });\n        });\n      });\n    };\n\n    const selection = identity;\n    const unmergable = selectedCells => {\n      const hasSpan = (elem, type) => getOpt(elem, type).exists(span => parseInt(span, 10) > 1);\n      const hasRowOrColSpan = elem => hasSpan(elem, 'rowspan') || hasSpan(elem, 'colspan');\n      return selectedCells.length > 0 && forall(selectedCells, hasRowOrColSpan) ? Optional.some(selectedCells) : Optional.none();\n    };\n    const mergable = (table, selectedCells, ephemera) => {\n      if (selectedCells.length <= 1) {\n        return Optional.none();\n      } else {\n        return retrieveBox(table, ephemera.firstSelectedSelector, ephemera.lastSelectedSelector).map(bounds => ({\n          bounds,\n          cells: selectedCells\n        }));\n      }\n    };\n\n    const strSelected = 'data-mce-selected';\n    const strSelectedSelector = 'td[' + strSelected + '],th[' + strSelected + ']';\n    const strAttributeSelector = '[' + strSelected + ']';\n    const strFirstSelected = 'data-mce-first-selected';\n    const strFirstSelectedSelector = 'td[' + strFirstSelected + '],th[' + strFirstSelected + ']';\n    const strLastSelected = 'data-mce-last-selected';\n    const strLastSelectedSelector = 'td[' + strLastSelected + '],th[' + strLastSelected + ']';\n    const attributeSelector = strAttributeSelector;\n    const ephemera = {\n      selected: strSelected,\n      selectedSelector: strSelectedSelector,\n      firstSelected: strFirstSelected,\n      firstSelectedSelector: strFirstSelectedSelector,\n      lastSelected: strLastSelected,\n      lastSelectedSelector: strLastSelectedSelector\n    };\n\n    const forMenu = (selectedCells, table, cell) => ({\n      element: cell,\n      mergable: mergable(table, selectedCells, ephemera),\n      unmergable: unmergable(selectedCells),\n      selection: selection(selectedCells)\n    });\n    const paste = (element, clipboard, generators) => ({\n      element,\n      clipboard,\n      generators\n    });\n    const pasteRows = (selectedCells, _cell, clipboard, generators) => ({\n      selection: selection(selectedCells),\n      clipboard,\n      generators\n    });\n\n    const getSelectionCellFallback = element => table(element).bind(table => retrieve(table, ephemera.firstSelectedSelector)).fold(constant(element), cells => cells[0]);\n    const getSelectionFromSelector = selector => (initCell, isRoot) => {\n      const cellName = name(initCell);\n      const cell = cellName === 'col' || cellName === 'colgroup' ? getSelectionCellFallback(initCell) : initCell;\n      return closest$1(cell, selector, isRoot);\n    };\n    const getSelectionCellOrCaption = getSelectionFromSelector('th,td,caption');\n    const getSelectionCell = getSelectionFromSelector('th,td');\n    const getCellsFromSelection = editor => fromDom(editor.model.table.getSelectedCells());\n    const getCellsFromFakeSelection = editor => filter$2(getCellsFromSelection(editor), cell => is$2(cell, ephemera.selectedSelector));\n\n    const extractSelected = cells => {\n      return table(cells[0]).map(table => {\n        const replica = extract$1(table, attributeSelector);\n        removeDataStyle(replica);\n        return [replica];\n      });\n    };\n    const serializeElements = (editor, elements) => map$1(elements, elm => editor.selection.serializer.serialize(elm.dom, {})).join('');\n    const getTextContent = elements => map$1(elements, element => element.dom.innerText).join('');\n    const registerEvents = (editor, actions) => {\n      editor.on('BeforeGetContent', e => {\n        const multiCellContext = cells => {\n          e.preventDefault();\n          extractSelected(cells).each(elements => {\n            e.content = e.format === 'text' ? getTextContent(elements) : serializeElements(editor, elements);\n          });\n        };\n        if (e.selection === true) {\n          const cells = getCellsFromFakeSelection(editor);\n          if (cells.length >= 1) {\n            multiCellContext(cells);\n          }\n        }\n      });\n      editor.on('BeforeSetContent', e => {\n        if (e.selection === true && e.paste === true) {\n          const selectedCells = getCellsFromSelection(editor);\n          head(selectedCells).each(cell => {\n            table(cell).each(table => {\n              const elements = filter$2(fromHtml(e.content), content => {\n                return name(content) !== 'meta';\n              });\n              const isTable = isTag('table');\n              if (shouldMergeContentOnPaste(editor) && elements.length === 1 && isTable(elements[0])) {\n                e.preventDefault();\n                const doc = SugarElement.fromDom(editor.getDoc());\n                const generators = paste$1(doc);\n                const targets = paste(cell, elements[0], generators);\n                actions.pasteCells(table, targets).each(() => {\n                  editor.focus();\n                });\n              }\n            });\n          });\n        }\n      });\n    };\n\n    const point = (element, offset) => ({\n      element,\n      offset\n    });\n\n    const scan$1 = (universe, element, direction) => {\n      if (universe.property().isText(element) && universe.property().getText(element).trim().length === 0 || universe.property().isComment(element)) {\n        return direction(element).bind(elem => {\n          return scan$1(universe, elem, direction).orThunk(() => {\n            return Optional.some(elem);\n          });\n        });\n      } else {\n        return Optional.none();\n      }\n    };\n    const toEnd = (universe, element) => {\n      if (universe.property().isText(element)) {\n        return universe.property().getText(element).length;\n      }\n      const children = universe.property().children(element);\n      return children.length;\n    };\n    const freefallRtl$2 = (universe, element) => {\n      const candidate = scan$1(universe, element, universe.query().prevSibling).getOr(element);\n      if (universe.property().isText(candidate)) {\n        return point(candidate, toEnd(universe, candidate));\n      }\n      const children = universe.property().children(candidate);\n      return children.length > 0 ? freefallRtl$2(universe, children[children.length - 1]) : point(candidate, toEnd(universe, candidate));\n    };\n\n    const freefallRtl$1 = freefallRtl$2;\n\n    const universe$2 = DomUniverse();\n    const freefallRtl = element => {\n      return freefallRtl$1(universe$2, element);\n    };\n\n    const halve = (main, other) => {\n      if (!hasColspan(main)) {\n        const width = getGenericWidth(main);\n        width.each(w => {\n          const newWidth = w.value / 2;\n          setGenericWidth(main, newWidth, w.unit);\n          setGenericWidth(other, newWidth, w.unit);\n        });\n      }\n    };\n\n    const zero = array => map$1(array, constant(0));\n    const surround = (sizes, startIndex, endIndex, results, f) => f(sizes.slice(0, startIndex)).concat(results).concat(f(sizes.slice(endIndex)));\n    const clampDeltaHelper = predicate => (sizes, index, delta, minCellSize) => {\n      if (!predicate(delta)) {\n        return delta;\n      } else {\n        const newSize = Math.max(minCellSize, sizes[index] - Math.abs(delta));\n        const diff = Math.abs(newSize - sizes[index]);\n        return delta >= 0 ? diff : -diff;\n      }\n    };\n    const clampNegativeDelta = clampDeltaHelper(delta => delta < 0);\n    const clampDelta = clampDeltaHelper(always);\n    const resizeTable = () => {\n      const calcFixedDeltas = (sizes, index, next, delta, minCellSize) => {\n        const clampedDelta = clampNegativeDelta(sizes, index, delta, minCellSize);\n        return surround(sizes, index, next + 1, [\n          clampedDelta,\n          0\n        ], zero);\n      };\n      const calcRelativeDeltas = (sizes, index, delta, minCellSize) => {\n        const ratio = (100 + delta) / 100;\n        const newThis = Math.max(minCellSize, (sizes[index] + delta) / ratio);\n        return map$1(sizes, (size, idx) => {\n          const newSize = idx === index ? newThis : size / ratio;\n          return newSize - size;\n        });\n      };\n      const calcLeftEdgeDeltas = (sizes, index, next, delta, minCellSize, isRelative) => {\n        if (isRelative) {\n          return calcRelativeDeltas(sizes, index, delta, minCellSize);\n        } else {\n          return calcFixedDeltas(sizes, index, next, delta, minCellSize);\n        }\n      };\n      const calcMiddleDeltas = (sizes, _prev, index, next, delta, minCellSize, isRelative) => calcLeftEdgeDeltas(sizes, index, next, delta, minCellSize, isRelative);\n      const resizeTable = (resizer, delta) => resizer(delta);\n      const calcRightEdgeDeltas = (sizes, _prev, index, delta, minCellSize, isRelative) => {\n        if (isRelative) {\n          return calcRelativeDeltas(sizes, index, delta, minCellSize);\n        } else {\n          const clampedDelta = clampNegativeDelta(sizes, index, delta, minCellSize);\n          return zero(sizes.slice(0, index)).concat([clampedDelta]);\n        }\n      };\n      const calcRedestributedWidths = (sizes, totalWidth, pixelDelta, isRelative) => {\n        if (isRelative) {\n          const tableWidth = totalWidth + pixelDelta;\n          const ratio = tableWidth / totalWidth;\n          const newSizes = map$1(sizes, size => size / ratio);\n          return {\n            delta: ratio * 100 - 100,\n            newSizes\n          };\n        } else {\n          return {\n            delta: pixelDelta,\n            newSizes: sizes\n          };\n        }\n      };\n      return {\n        resizeTable,\n        clampTableDelta: clampNegativeDelta,\n        calcLeftEdgeDeltas,\n        calcMiddleDeltas,\n        calcRightEdgeDeltas,\n        calcRedestributedWidths\n      };\n    };\n    const preserveTable = () => {\n      const calcLeftEdgeDeltas = (sizes, index, next, delta, minCellSize) => {\n        const idx = delta >= 0 ? next : index;\n        const clampedDelta = clampDelta(sizes, idx, delta, minCellSize);\n        return surround(sizes, index, next + 1, [\n          clampedDelta,\n          -clampedDelta\n        ], zero);\n      };\n      const calcMiddleDeltas = (sizes, _prev, index, next, delta, minCellSize) => calcLeftEdgeDeltas(sizes, index, next, delta, minCellSize);\n      const resizeTable = (resizer, delta, isLastColumn) => {\n        if (isLastColumn) {\n          resizer(delta);\n        }\n      };\n      const calcRightEdgeDeltas = (sizes, _prev, _index, delta, _minCellSize, isRelative) => {\n        if (isRelative) {\n          return zero(sizes);\n        } else {\n          const diff = delta / sizes.length;\n          return map$1(sizes, constant(diff));\n        }\n      };\n      const clampTableDelta = (sizes, index, delta, minCellSize, isLastColumn) => {\n        if (isLastColumn) {\n          if (delta >= 0) {\n            return delta;\n          } else {\n            const maxDelta = foldl(sizes, (a, b) => a + b - minCellSize, 0);\n            return Math.max(-maxDelta, delta);\n          }\n        } else {\n          return clampNegativeDelta(sizes, index, delta, minCellSize);\n        }\n      };\n      const calcRedestributedWidths = (sizes, _totalWidth, _pixelDelta, _isRelative) => ({\n        delta: 0,\n        newSizes: sizes\n      });\n      return {\n        resizeTable,\n        clampTableDelta,\n        calcLeftEdgeDeltas,\n        calcMiddleDeltas,\n        calcRightEdgeDeltas,\n        calcRedestributedWidths\n      };\n    };\n\n    const getGridSize = table => {\n      const warehouse = Warehouse.fromTable(table);\n      return warehouse.grid;\n    };\n\n    const isHeaderCell = isTag('th');\n    const isHeaderCells = cells => forall(cells, cell => isHeaderCell(cell.element));\n    const getRowHeaderType = (isHeaderRow, isHeaderCells) => {\n      if (isHeaderRow && isHeaderCells) {\n        return 'sectionCells';\n      } else if (isHeaderRow) {\n        return 'section';\n      } else {\n        return 'cells';\n      }\n    };\n    const getRowType = row => {\n      const isHeaderRow = row.section === 'thead';\n      const isHeaderCells = is(findCommonCellType(row.cells), 'th');\n      if (row.section === 'tfoot') {\n        return { type: 'footer' };\n      } else if (isHeaderRow || isHeaderCells) {\n        return {\n          type: 'header',\n          subType: getRowHeaderType(isHeaderRow, isHeaderCells)\n        };\n      } else {\n        return { type: 'body' };\n      }\n    };\n    const findCommonCellType = cells => {\n      const headerCells = filter$2(cells, cell => isHeaderCell(cell.element));\n      if (headerCells.length === 0) {\n        return Optional.some('td');\n      } else if (headerCells.length === cells.length) {\n        return Optional.some('th');\n      } else {\n        return Optional.none();\n      }\n    };\n    const findCommonRowType = rows => {\n      const rowTypes = map$1(rows, row => getRowType(row).type);\n      const hasHeader = contains$2(rowTypes, 'header');\n      const hasFooter = contains$2(rowTypes, 'footer');\n      if (!hasHeader && !hasFooter) {\n        return Optional.some('body');\n      } else {\n        const hasBody = contains$2(rowTypes, 'body');\n        if (hasHeader && !hasBody && !hasFooter) {\n          return Optional.some('header');\n        } else if (!hasHeader && !hasBody && hasFooter) {\n          return Optional.some('footer');\n        } else {\n          return Optional.none();\n        }\n      }\n    };\n    const findTableRowHeaderType = warehouse => findMap(warehouse.all, row => {\n      const rowType = getRowType(row);\n      return rowType.type === 'header' ? Optional.from(rowType.subType) : Optional.none();\n    });\n\n    const transformCell = (cell, comparator, substitution) => elementnew(substitution(cell.element, comparator), true, cell.isLocked);\n    const transformRow = (row, section) => row.section !== section ? rowcells(row.element, row.cells, section, row.isNew) : row;\n    const section = () => ({\n      transformRow,\n      transformCell: (cell, comparator, substitution) => {\n        const newCell = substitution(cell.element, comparator);\n        const fixedCell = name(newCell) !== 'td' ? mutate$1(newCell, 'td') : newCell;\n        return elementnew(fixedCell, cell.isNew, cell.isLocked);\n      }\n    });\n    const sectionCells = () => ({\n      transformRow,\n      transformCell\n    });\n    const cells = () => ({\n      transformRow: (row, section) => {\n        const newSection = section === 'thead' ? 'tbody' : section;\n        return transformRow(row, newSection);\n      },\n      transformCell\n    });\n    const fallback = () => ({\n      transformRow: identity,\n      transformCell\n    });\n    const getTableSectionType = (table, fallback) => {\n      const warehouse = Warehouse.fromTable(table);\n      const type = findTableRowHeaderType(warehouse).getOr(fallback);\n      switch (type) {\n      case 'section':\n        return section();\n      case 'sectionCells':\n        return sectionCells();\n      case 'cells':\n        return cells();\n      }\n    };\n    const TableSection = {\n      getTableSectionType,\n      section,\n      sectionCells,\n      cells,\n      fallback\n    };\n\n    const setIfNot = (element, property, value, ignore) => {\n      if (value === ignore) {\n        remove$7(element, property);\n      } else {\n        set$2(element, property, value);\n      }\n    };\n    const insert$1 = (table, selector, element) => {\n      last$2(children(table, selector)).fold(() => prepend(table, element), child => after$5(child, element));\n    };\n    const generateSection = (table, sectionName) => {\n      const section = child(table, sectionName).getOrThunk(() => {\n        const newSection = SugarElement.fromTag(sectionName, owner(table).dom);\n        if (sectionName === 'thead') {\n          insert$1(table, 'caption,colgroup', newSection);\n        } else if (sectionName === 'colgroup') {\n          insert$1(table, 'caption', newSection);\n        } else {\n          append$1(table, newSection);\n        }\n        return newSection;\n      });\n      empty(section);\n      return section;\n    };\n    const render$1 = (table, grid) => {\n      const newRows = [];\n      const newCells = [];\n      const syncRows = gridSection => map$1(gridSection, row => {\n        if (row.isNew) {\n          newRows.push(row.element);\n        }\n        const tr = row.element;\n        empty(tr);\n        each$2(row.cells, cell => {\n          if (cell.isNew) {\n            newCells.push(cell.element);\n          }\n          setIfNot(cell.element, 'colspan', cell.colspan, 1);\n          setIfNot(cell.element, 'rowspan', cell.rowspan, 1);\n          append$1(tr, cell.element);\n        });\n        return tr;\n      });\n      const syncColGroup = gridSection => bind$2(gridSection, colGroup => map$1(colGroup.cells, col => {\n        setIfNot(col.element, 'span', col.colspan, 1);\n        return col.element;\n      }));\n      const renderSection = (gridSection, sectionName) => {\n        const section = generateSection(table, sectionName);\n        const sync = sectionName === 'colgroup' ? syncColGroup : syncRows;\n        const sectionElems = sync(gridSection);\n        append(section, sectionElems);\n      };\n      const removeSection = sectionName => {\n        child(table, sectionName).each(remove$6);\n      };\n      const renderOrRemoveSection = (gridSection, sectionName) => {\n        if (gridSection.length > 0) {\n          renderSection(gridSection, sectionName);\n        } else {\n          removeSection(sectionName);\n        }\n      };\n      const headSection = [];\n      const bodySection = [];\n      const footSection = [];\n      const columnGroupsSection = [];\n      each$2(grid, row => {\n        switch (row.section) {\n        case 'thead':\n          headSection.push(row);\n          break;\n        case 'tbody':\n          bodySection.push(row);\n          break;\n        case 'tfoot':\n          footSection.push(row);\n          break;\n        case 'colgroup':\n          columnGroupsSection.push(row);\n          break;\n        }\n      });\n      renderOrRemoveSection(columnGroupsSection, 'colgroup');\n      renderOrRemoveSection(headSection, 'thead');\n      renderOrRemoveSection(bodySection, 'tbody');\n      renderOrRemoveSection(footSection, 'tfoot');\n      return {\n        newRows,\n        newCells\n      };\n    };\n    const copy = grid => map$1(grid, row => {\n      const tr = shallow(row.element);\n      each$2(row.cells, cell => {\n        const clonedCell = deep(cell.element);\n        setIfNot(clonedCell, 'colspan', cell.colspan, 1);\n        setIfNot(clonedCell, 'rowspan', cell.rowspan, 1);\n        append$1(tr, clonedCell);\n      });\n      return tr;\n    });\n\n    const getColumn = (grid, index) => {\n      return map$1(grid, row => {\n        return getCell(row, index);\n      });\n    };\n    const getRow = (grid, index) => {\n      return grid[index];\n    };\n    const findDiff = (xs, comp) => {\n      if (xs.length === 0) {\n        return 0;\n      }\n      const first = xs[0];\n      const index = findIndex(xs, x => {\n        return !comp(first.element, x.element);\n      });\n      return index.getOr(xs.length);\n    };\n    const subgrid = (grid, row, column, comparator) => {\n      const gridRow = getRow(grid, row);\n      const isColRow = gridRow.section === 'colgroup';\n      const colspan = findDiff(gridRow.cells.slice(column), comparator);\n      const rowspan = isColRow ? 1 : findDiff(getColumn(grid.slice(row), column), comparator);\n      return {\n        colspan,\n        rowspan\n      };\n    };\n\n    const toDetails = (grid, comparator) => {\n      const seen = map$1(grid, row => map$1(row.cells, never));\n      const updateSeen = (rowIndex, columnIndex, rowspan, colspan) => {\n        for (let row = rowIndex; row < rowIndex + rowspan; row++) {\n          for (let column = columnIndex; column < columnIndex + colspan; column++) {\n            seen[row][column] = true;\n          }\n        }\n      };\n      return map$1(grid, (row, rowIndex) => {\n        const details = bind$2(row.cells, (cell, columnIndex) => {\n          if (seen[rowIndex][columnIndex] === false) {\n            const result = subgrid(grid, rowIndex, columnIndex, comparator);\n            updateSeen(rowIndex, columnIndex, result.rowspan, result.colspan);\n            return [detailnew(cell.element, result.rowspan, result.colspan, cell.isNew)];\n          } else {\n            return [];\n          }\n        });\n        return rowdetailnew(row.element, details, row.section, row.isNew);\n      });\n    };\n    const toGrid = (warehouse, generators, isNew) => {\n      const grid = [];\n      each$2(warehouse.colgroups, colgroup => {\n        const colgroupCols = [];\n        for (let columnIndex = 0; columnIndex < warehouse.grid.columns; columnIndex++) {\n          const element = Warehouse.getColumnAt(warehouse, columnIndex).map(column => elementnew(column.element, isNew, false)).getOrThunk(() => elementnew(generators.colGap(), true, false));\n          colgroupCols.push(element);\n        }\n        grid.push(rowcells(colgroup.element, colgroupCols, 'colgroup', isNew));\n      });\n      for (let rowIndex = 0; rowIndex < warehouse.grid.rows; rowIndex++) {\n        const rowCells = [];\n        for (let columnIndex = 0; columnIndex < warehouse.grid.columns; columnIndex++) {\n          const element = Warehouse.getAt(warehouse, rowIndex, columnIndex).map(item => elementnew(item.element, isNew, item.isLocked)).getOrThunk(() => elementnew(generators.gap(), true, false));\n          rowCells.push(element);\n        }\n        const rowDetail = warehouse.all[rowIndex];\n        const row = rowcells(rowDetail.element, rowCells, rowDetail.section, isNew);\n        grid.push(row);\n      }\n      return grid;\n    };\n\n    const fromWarehouse = (warehouse, generators) => toGrid(warehouse, generators, false);\n    const toDetailList = grid => toDetails(grid, eq$1);\n    const findInWarehouse = (warehouse, element) => findMap(warehouse.all, r => find$1(r.cells, e => eq$1(element, e.element)));\n    const extractCells = (warehouse, target, predicate) => {\n      const details = map$1(target.selection, cell$1 => {\n        return cell(cell$1).bind(lc => findInWarehouse(warehouse, lc)).filter(predicate);\n      });\n      const cells = cat(details);\n      return someIf(cells.length > 0, cells);\n    };\n    const run = (operation, extract, adjustment, postAction, genWrappers) => (table, target, generators, behaviours) => {\n      const warehouse = Warehouse.fromTable(table);\n      const tableSection = Optional.from(behaviours === null || behaviours === void 0 ? void 0 : behaviours.section).getOrThunk(TableSection.fallback);\n      const output = extract(warehouse, target).map(info => {\n        const model = fromWarehouse(warehouse, generators);\n        const result = operation(model, info, eq$1, genWrappers(generators), tableSection);\n        const lockedColumns = getLockedColumnsFromGrid(result.grid);\n        const grid = toDetailList(result.grid);\n        return {\n          info,\n          grid,\n          cursor: result.cursor,\n          lockedColumns\n        };\n      });\n      return output.bind(out => {\n        const newElements = render$1(table, out.grid);\n        const tableSizing = Optional.from(behaviours === null || behaviours === void 0 ? void 0 : behaviours.sizing).getOrThunk(() => TableSize.getTableSize(table));\n        const resizing = Optional.from(behaviours === null || behaviours === void 0 ? void 0 : behaviours.resize).getOrThunk(preserveTable);\n        adjustment(table, out.grid, out.info, {\n          sizing: tableSizing,\n          resize: resizing,\n          section: tableSection\n        });\n        postAction(table);\n        remove$7(table, LOCKED_COL_ATTR);\n        if (out.lockedColumns.length > 0) {\n          set$2(table, LOCKED_COL_ATTR, out.lockedColumns.join(','));\n        }\n        return Optional.some({\n          cursor: out.cursor,\n          newRows: newElements.newRows,\n          newCells: newElements.newCells\n        });\n      });\n    };\n    const onPaste = (warehouse, target) => cell(target.element).bind(cell => findInWarehouse(warehouse, cell).map(details => {\n      const value = {\n        ...details,\n        generators: target.generators,\n        clipboard: target.clipboard\n      };\n      return value;\n    }));\n    const onPasteByEditor = (warehouse, target) => extractCells(warehouse, target, always).map(cells => ({\n      cells,\n      generators: target.generators,\n      clipboard: target.clipboard\n    }));\n    const onMergable = (_warehouse, target) => target.mergable;\n    const onUnmergable = (_warehouse, target) => target.unmergable;\n    const onCells = (warehouse, target) => extractCells(warehouse, target, always);\n    const onUnlockedCells = (warehouse, target) => extractCells(warehouse, target, detail => !detail.isLocked);\n    const isUnlockedTableCell = (warehouse, cell) => findInWarehouse(warehouse, cell).exists(detail => !detail.isLocked);\n    const allUnlocked = (warehouse, cells) => forall(cells, cell => isUnlockedTableCell(warehouse, cell));\n    const onUnlockedMergable = (warehouse, target) => onMergable(warehouse, target).filter(mergeable => allUnlocked(warehouse, mergeable.cells));\n    const onUnlockedUnmergable = (warehouse, target) => onUnmergable(warehouse, target).filter(cells => allUnlocked(warehouse, cells));\n\n    const merge$2 = (grid, bounds, comparator, substitution) => {\n      const rows = extractGridDetails(grid).rows;\n      if (rows.length === 0) {\n        return grid;\n      }\n      for (let i = bounds.startRow; i <= bounds.finishRow; i++) {\n        for (let j = bounds.startCol; j <= bounds.finishCol; j++) {\n          const row = rows[i];\n          const isLocked = getCell(row, j).isLocked;\n          mutateCell(row, j, elementnew(substitution(), false, isLocked));\n        }\n      }\n      return grid;\n    };\n    const unmerge = (grid, target, comparator, substitution) => {\n      const rows = extractGridDetails(grid).rows;\n      let first = true;\n      for (let i = 0; i < rows.length; i++) {\n        for (let j = 0; j < cellLength(rows[0]); j++) {\n          const row = rows[i];\n          const currentCell = getCell(row, j);\n          const currentCellElm = currentCell.element;\n          const isToReplace = comparator(currentCellElm, target);\n          if (isToReplace && !first) {\n            mutateCell(row, j, elementnew(substitution(), true, currentCell.isLocked));\n          } else if (isToReplace) {\n            first = false;\n          }\n        }\n      }\n      return grid;\n    };\n    const uniqueCells = (row, comparator) => {\n      return foldl(row, (rest, cell) => {\n        return exists(rest, currentCell => {\n          return comparator(currentCell.element, cell.element);\n        }) ? rest : rest.concat([cell]);\n      }, []);\n    };\n    const splitCols = (grid, index, comparator, substitution) => {\n      if (index > 0 && index < grid[0].cells.length) {\n        each$2(grid, row => {\n          const prevCell = row.cells[index - 1];\n          let offset = 0;\n          const substitute = substitution();\n          while (row.cells.length > index + offset && comparator(prevCell.element, row.cells[index + offset].element)) {\n            mutateCell(row, index + offset, elementnew(substitute, true, row.cells[index + offset].isLocked));\n            offset++;\n          }\n        });\n      }\n      return grid;\n    };\n    const splitRows = (grid, index, comparator, substitution) => {\n      const rows = extractGridDetails(grid).rows;\n      if (index > 0 && index < rows.length) {\n        const rowPrevCells = rows[index - 1].cells;\n        const cells = uniqueCells(rowPrevCells, comparator);\n        each$2(cells, cell => {\n          let replacement = Optional.none();\n          for (let i = index; i < rows.length; i++) {\n            for (let j = 0; j < cellLength(rows[0]); j++) {\n              const row = rows[i];\n              const current = getCell(row, j);\n              const isToReplace = comparator(current.element, cell.element);\n              if (isToReplace) {\n                if (replacement.isNone()) {\n                  replacement = Optional.some(substitution());\n                }\n                replacement.each(sub => {\n                  mutateCell(row, j, elementnew(sub, true, current.isLocked));\n                });\n              }\n            }\n          }\n        });\n      }\n      return grid;\n    };\n\n    const value$1 = value => {\n      const applyHelper = fn => fn(value);\n      const constHelper = constant(value);\n      const outputHelper = () => output;\n      const output = {\n        tag: true,\n        inner: value,\n        fold: (_onError, onValue) => onValue(value),\n        isValue: always,\n        isError: never,\n        map: mapper => Result.value(mapper(value)),\n        mapError: outputHelper,\n        bind: applyHelper,\n        exists: applyHelper,\n        forall: applyHelper,\n        getOr: constHelper,\n        or: outputHelper,\n        getOrThunk: constHelper,\n        orThunk: outputHelper,\n        getOrDie: constHelper,\n        each: fn => {\n          fn(value);\n        },\n        toOptional: () => Optional.some(value)\n      };\n      return output;\n    };\n    const error = error => {\n      const outputHelper = () => output;\n      const output = {\n        tag: false,\n        inner: error,\n        fold: (onError, _onValue) => onError(error),\n        isValue: never,\n        isError: always,\n        map: outputHelper,\n        mapError: mapper => Result.error(mapper(error)),\n        bind: outputHelper,\n        exists: never,\n        forall: always,\n        getOr: identity,\n        or: identity,\n        getOrThunk: apply,\n        orThunk: apply,\n        getOrDie: die(String(error)),\n        each: noop,\n        toOptional: Optional.none\n      };\n      return output;\n    };\n    const fromOption = (optional, err) => optional.fold(() => error(err), value$1);\n    const Result = {\n      value: value$1,\n      error,\n      fromOption\n    };\n\n    const measure = (startAddress, gridA, gridB) => {\n      if (startAddress.row >= gridA.length || startAddress.column > cellLength(gridA[0])) {\n        return Result.error('invalid start address out of table bounds, row: ' + startAddress.row + ', column: ' + startAddress.column);\n      }\n      const rowRemainder = gridA.slice(startAddress.row);\n      const colRemainder = rowRemainder[0].cells.slice(startAddress.column);\n      const colRequired = cellLength(gridB[0]);\n      const rowRequired = gridB.length;\n      return Result.value({\n        rowDelta: rowRemainder.length - rowRequired,\n        colDelta: colRemainder.length - colRequired\n      });\n    };\n    const measureWidth = (gridA, gridB) => {\n      const colLengthA = cellLength(gridA[0]);\n      const colLengthB = cellLength(gridB[0]);\n      return {\n        rowDelta: 0,\n        colDelta: colLengthA - colLengthB\n      };\n    };\n    const measureHeight = (gridA, gridB) => {\n      const rowLengthA = gridA.length;\n      const rowLengthB = gridB.length;\n      return {\n        rowDelta: rowLengthA - rowLengthB,\n        colDelta: 0\n      };\n    };\n    const generateElements = (amount, row, generators, isLocked) => {\n      const generator = row.section === 'colgroup' ? generators.col : generators.cell;\n      return range$1(amount, idx => elementnew(generator(), true, isLocked(idx)));\n    };\n    const rowFill = (grid, amount, generators, lockedColumns) => {\n      const exampleRow = grid[grid.length - 1];\n      return grid.concat(range$1(amount, () => {\n        const generator = exampleRow.section === 'colgroup' ? generators.colgroup : generators.row;\n        const row = clone(exampleRow, generator, identity);\n        const elements = generateElements(row.cells.length, row, generators, idx => has$1(lockedColumns, idx.toString()));\n        return setCells(row, elements);\n      }));\n    };\n    const colFill = (grid, amount, generators, startIndex) => map$1(grid, row => {\n      const newChildren = generateElements(amount, row, generators, never);\n      return addCells(row, startIndex, newChildren);\n    });\n    const lockedColFill = (grid, generators, lockedColumns) => map$1(grid, row => {\n      return foldl(lockedColumns, (acc, colNum) => {\n        const newChild = generateElements(1, row, generators, always)[0];\n        return addCell(acc, colNum, newChild);\n      }, row);\n    });\n    const tailor = (gridA, delta, generators) => {\n      const fillCols = delta.colDelta < 0 ? colFill : identity;\n      const fillRows = delta.rowDelta < 0 ? rowFill : identity;\n      const lockedColumns = getLockedColumnsFromGrid(gridA);\n      const gridWidth = cellLength(gridA[0]);\n      const isLastColLocked = exists(lockedColumns, locked => locked === gridWidth - 1);\n      const modifiedCols = fillCols(gridA, Math.abs(delta.colDelta), generators, isLastColLocked ? gridWidth - 1 : gridWidth);\n      const newLockedColumns = getLockedColumnsFromGrid(modifiedCols);\n      return fillRows(modifiedCols, Math.abs(delta.rowDelta), generators, mapToObject(newLockedColumns, always));\n    };\n\n    const isSpanning = (grid, row, col, comparator) => {\n      const candidate = getCell(grid[row], col);\n      const matching = curry(comparator, candidate.element);\n      const currentRow = grid[row];\n      return grid.length > 1 && cellLength(currentRow) > 1 && (col > 0 && matching(getCellElement(currentRow, col - 1)) || col < currentRow.cells.length - 1 && matching(getCellElement(currentRow, col + 1)) || row > 0 && matching(getCellElement(grid[row - 1], col)) || row < grid.length - 1 && matching(getCellElement(grid[row + 1], col)));\n    };\n    const mergeTables = (startAddress, gridA, gridBRows, generator, comparator, lockedColumns) => {\n      const startRow = startAddress.row;\n      const startCol = startAddress.column;\n      const mergeHeight = gridBRows.length;\n      const mergeWidth = cellLength(gridBRows[0]);\n      const endRow = startRow + mergeHeight;\n      const endCol = startCol + mergeWidth + lockedColumns.length;\n      const lockedColumnObj = mapToObject(lockedColumns, always);\n      for (let r = startRow; r < endRow; r++) {\n        let skippedCol = 0;\n        for (let c = startCol; c < endCol; c++) {\n          if (lockedColumnObj[c]) {\n            skippedCol++;\n            continue;\n          }\n          if (isSpanning(gridA, r, c, comparator)) {\n            unmerge(gridA, getCellElement(gridA[r], c), comparator, generator.cell);\n          }\n          const gridBColIndex = c - startCol - skippedCol;\n          const newCell = getCell(gridBRows[r - startRow], gridBColIndex);\n          const newCellElm = newCell.element;\n          const replacement = generator.replace(newCellElm);\n          mutateCell(gridA[r], c, elementnew(replacement, true, newCell.isLocked));\n        }\n      }\n      return gridA;\n    };\n    const getValidStartAddress = (currentStartAddress, grid, lockedColumns) => {\n      const gridColLength = cellLength(grid[0]);\n      const adjustedRowAddress = extractGridDetails(grid).cols.length + currentStartAddress.row;\n      const possibleColAddresses = range$1(gridColLength - currentStartAddress.column, num => num + currentStartAddress.column);\n      const validColAddress = find$1(possibleColAddresses, num => forall(lockedColumns, col => col !== num)).getOr(gridColLength - 1);\n      return {\n        row: adjustedRowAddress,\n        column: validColAddress\n      };\n    };\n    const getLockedColumnsWithinBounds = (startAddress, rows, lockedColumns) => filter$2(lockedColumns, colNum => colNum >= startAddress.column && colNum <= cellLength(rows[0]) + startAddress.column);\n    const merge$1 = (startAddress, gridA, gridB, generator, comparator) => {\n      const lockedColumns = getLockedColumnsFromGrid(gridA);\n      const validStartAddress = getValidStartAddress(startAddress, gridA, lockedColumns);\n      const gridBRows = extractGridDetails(gridB).rows;\n      const lockedColumnsWithinBounds = getLockedColumnsWithinBounds(validStartAddress, gridBRows, lockedColumns);\n      const result = measure(validStartAddress, gridA, gridBRows);\n      return result.map(diff => {\n        const delta = {\n          ...diff,\n          colDelta: diff.colDelta - lockedColumnsWithinBounds.length\n        };\n        const fittedGrid = tailor(gridA, delta, generator);\n        const newLockedColumns = getLockedColumnsFromGrid(fittedGrid);\n        const newLockedColumnsWithinBounds = getLockedColumnsWithinBounds(validStartAddress, gridBRows, newLockedColumns);\n        return mergeTables(validStartAddress, fittedGrid, gridBRows, generator, comparator, newLockedColumnsWithinBounds);\n      });\n    };\n    const insertCols = (index, gridA, gridB, generator, comparator) => {\n      splitCols(gridA, index, comparator, generator.cell);\n      const delta = measureHeight(gridB, gridA);\n      const fittedNewGrid = tailor(gridB, delta, generator);\n      const secondDelta = measureHeight(gridA, fittedNewGrid);\n      const fittedOldGrid = tailor(gridA, secondDelta, generator);\n      return map$1(fittedOldGrid, (gridRow, i) => {\n        return addCells(gridRow, index, fittedNewGrid[i].cells);\n      });\n    };\n    const insertRows = (index, gridA, gridB, generator, comparator) => {\n      splitRows(gridA, index, comparator, generator.cell);\n      const locked = getLockedColumnsFromGrid(gridA);\n      const diff = measureWidth(gridA, gridB);\n      const delta = {\n        ...diff,\n        colDelta: diff.colDelta - locked.length\n      };\n      const fittedOldGrid = tailor(gridA, delta, generator);\n      const {\n        cols: oldCols,\n        rows: oldRows\n      } = extractGridDetails(fittedOldGrid);\n      const newLocked = getLockedColumnsFromGrid(fittedOldGrid);\n      const secondDiff = measureWidth(gridB, gridA);\n      const secondDelta = {\n        ...secondDiff,\n        colDelta: secondDiff.colDelta + newLocked.length\n      };\n      const fittedGridB = lockedColFill(gridB, generator, newLocked);\n      const fittedNewGrid = tailor(fittedGridB, secondDelta, generator);\n      return [\n        ...oldCols,\n        ...oldRows.slice(0, index),\n        ...fittedNewGrid,\n        ...oldRows.slice(index, oldRows.length)\n      ];\n    };\n\n    const cloneRow = (row, cloneCell, comparator, substitution) => clone(row, elem => substitution(elem, comparator), cloneCell);\n    const insertRowAt = (grid, index, example, comparator, substitution) => {\n      const {rows, cols} = extractGridDetails(grid);\n      const before = rows.slice(0, index);\n      const after = rows.slice(index);\n      const newRow = cloneRow(rows[example], (ex, c) => {\n        const withinSpan = index > 0 && index < rows.length && comparator(getCellElement(rows[index - 1], c), getCellElement(rows[index], c));\n        const ret = withinSpan ? getCell(rows[index], c) : elementnew(substitution(ex.element, comparator), true, ex.isLocked);\n        return ret;\n      }, comparator, substitution);\n      return [\n        ...cols,\n        ...before,\n        newRow,\n        ...after\n      ];\n    };\n    const getElementFor = (row, column, section, withinSpan, example, comparator, substitution) => {\n      if (section === 'colgroup' || !withinSpan) {\n        const cell = getCell(row, example);\n        return elementnew(substitution(cell.element, comparator), true, false);\n      } else {\n        return getCell(row, column);\n      }\n    };\n    const insertColumnAt = (grid, index, example, comparator, substitution) => map$1(grid, row => {\n      const withinSpan = index > 0 && index < cellLength(row) && comparator(getCellElement(row, index - 1), getCellElement(row, index));\n      const sub = getElementFor(row, index, row.section, withinSpan, example, comparator, substitution);\n      return addCell(row, index, sub);\n    });\n    const deleteColumnsAt = (grid, columns) => bind$2(grid, row => {\n      const existingCells = row.cells;\n      const cells = foldr(columns, (acc, column) => column >= 0 && column < acc.length ? acc.slice(0, column).concat(acc.slice(column + 1)) : acc, existingCells);\n      return cells.length > 0 ? [rowcells(row.element, cells, row.section, row.isNew)] : [];\n    });\n    const deleteRowsAt = (grid, start, finish) => {\n      const {rows, cols} = extractGridDetails(grid);\n      return [\n        ...cols,\n        ...rows.slice(0, start),\n        ...rows.slice(finish + 1)\n      ];\n    };\n\n    const notInStartRow = (grid, rowIndex, colIndex, comparator) => getCellElement(grid[rowIndex], colIndex) !== undefined && (rowIndex > 0 && comparator(getCellElement(grid[rowIndex - 1], colIndex), getCellElement(grid[rowIndex], colIndex)));\n    const notInStartColumn = (row, index, comparator) => index > 0 && comparator(getCellElement(row, index - 1), getCellElement(row, index));\n    const isDuplicatedCell = (grid, rowIndex, colIndex, comparator) => notInStartRow(grid, rowIndex, colIndex, comparator) || notInStartColumn(grid[rowIndex], colIndex, comparator);\n    const rowReplacerPredicate = (targetRow, columnHeaders) => {\n      const entireTableIsHeader = forall(columnHeaders, identity) && isHeaderCells(targetRow.cells);\n      return entireTableIsHeader ? always : (cell, _rowIndex, colIndex) => {\n        const type = name(cell.element);\n        return !(type === 'th' && columnHeaders[colIndex]);\n      };\n    };\n    const columnReplacePredicate = (targetColumn, rowHeaders) => {\n      const entireTableIsHeader = forall(rowHeaders, identity) && isHeaderCells(targetColumn);\n      return entireTableIsHeader ? always : (cell, rowIndex, _colIndex) => {\n        const type = name(cell.element);\n        return !(type === 'th' && rowHeaders[rowIndex]);\n      };\n    };\n    const determineScope = (applyScope, cell, newScope, isInHeader) => {\n      const hasSpan = scope => scope === 'row' ? hasRowspan(cell) : hasColspan(cell);\n      const getScope = scope => hasSpan(scope) ? `${ scope }group` : scope;\n      if (applyScope) {\n        return isHeaderCell(cell) ? getScope(newScope) : null;\n      } else if (isInHeader && isHeaderCell(cell)) {\n        const oppositeScope = newScope === 'row' ? 'col' : 'row';\n        return getScope(oppositeScope);\n      } else {\n        return null;\n      }\n    };\n    const rowScopeGenerator = (applyScope, columnHeaders) => (cell, rowIndex, columnIndex) => Optional.some(determineScope(applyScope, cell.element, 'col', columnHeaders[columnIndex]));\n    const columnScopeGenerator = (applyScope, rowHeaders) => (cell, rowIndex) => Optional.some(determineScope(applyScope, cell.element, 'row', rowHeaders[rowIndex]));\n    const replace = (cell, comparator, substitute) => elementnew(substitute(cell.element, comparator), true, cell.isLocked);\n    const replaceIn = (grid, targets, comparator, substitute, replacer, genScope, shouldReplace) => {\n      const isTarget = cell => {\n        return exists(targets, target => {\n          return comparator(cell.element, target.element);\n        });\n      };\n      return map$1(grid, (row, rowIndex) => {\n        return mapCells(row, (cell, colIndex) => {\n          if (isTarget(cell)) {\n            const newCell = shouldReplace(cell, rowIndex, colIndex) ? replacer(cell, comparator, substitute) : cell;\n            genScope(newCell, rowIndex, colIndex).each(scope => {\n              setOptions(newCell.element, { scope: Optional.from(scope) });\n            });\n            return newCell;\n          } else {\n            return cell;\n          }\n        });\n      });\n    };\n    const getColumnCells = (rows, columnIndex, comparator) => bind$2(rows, (row, i) => {\n      return isDuplicatedCell(rows, i, columnIndex, comparator) ? [] : [getCell(row, columnIndex)];\n    });\n    const getRowCells = (rows, rowIndex, comparator) => {\n      const targetRow = rows[rowIndex];\n      return bind$2(targetRow.cells, (item, i) => {\n        return isDuplicatedCell(rows, rowIndex, i, comparator) ? [] : [item];\n      });\n    };\n    const replaceColumns = (grid, indexes, applyScope, comparator, substitution) => {\n      const rows = extractGridDetails(grid).rows;\n      const targets = bind$2(indexes, index => getColumnCells(rows, index, comparator));\n      const rowHeaders = map$1(rows, row => isHeaderCells(row.cells));\n      const shouldReplaceCell = columnReplacePredicate(targets, rowHeaders);\n      const scopeGenerator = columnScopeGenerator(applyScope, rowHeaders);\n      return replaceIn(grid, targets, comparator, substitution, replace, scopeGenerator, shouldReplaceCell);\n    };\n    const replaceRows = (grid, indexes, section, applyScope, comparator, substitution, tableSection) => {\n      const {cols, rows} = extractGridDetails(grid);\n      const targetRow = rows[indexes[0]];\n      const targets = bind$2(indexes, index => getRowCells(rows, index, comparator));\n      const columnHeaders = map$1(targetRow.cells, (_cell, index) => isHeaderCells(getColumnCells(rows, index, comparator)));\n      const newRows = [...rows];\n      each$2(indexes, index => {\n        newRows[index] = tableSection.transformRow(rows[index], section);\n      });\n      const newGrid = [\n        ...cols,\n        ...newRows\n      ];\n      const shouldReplaceCell = rowReplacerPredicate(targetRow, columnHeaders);\n      const scopeGenerator = rowScopeGenerator(applyScope, columnHeaders);\n      return replaceIn(newGrid, targets, comparator, substitution, tableSection.transformCell, scopeGenerator, shouldReplaceCell);\n    };\n    const replaceCells = (grid, details, comparator, substitution) => {\n      const rows = extractGridDetails(grid).rows;\n      const targetCells = map$1(details, detail => getCell(rows[detail.row], detail.column));\n      return replaceIn(grid, targetCells, comparator, substitution, replace, Optional.none, always);\n    };\n\n    const generate = cases => {\n      if (!isArray(cases)) {\n        throw new Error('cases must be an array');\n      }\n      if (cases.length === 0) {\n        throw new Error('there must be at least one case');\n      }\n      const constructors = [];\n      const adt = {};\n      each$2(cases, (acase, count) => {\n        const keys$1 = keys(acase);\n        if (keys$1.length !== 1) {\n          throw new Error('one and only one name per case');\n        }\n        const key = keys$1[0];\n        const value = acase[key];\n        if (adt[key] !== undefined) {\n          throw new Error('duplicate key detected:' + key);\n        } else if (key === 'cata') {\n          throw new Error('cannot have a case named cata (sorry)');\n        } else if (!isArray(value)) {\n          throw new Error('case arguments must be an array');\n        }\n        constructors.push(key);\n        adt[key] = (...args) => {\n          const argLength = args.length;\n          if (argLength !== value.length) {\n            throw new Error('Wrong number of arguments to case ' + key + '. Expected ' + value.length + ' (' + value + '), got ' + argLength);\n          }\n          const match = branches => {\n            const branchKeys = keys(branches);\n            if (constructors.length !== branchKeys.length) {\n              throw new Error('Wrong number of arguments to match. Expected: ' + constructors.join(',') + '\\nActual: ' + branchKeys.join(','));\n            }\n            const allReqd = forall(constructors, reqKey => {\n              return contains$2(branchKeys, reqKey);\n            });\n            if (!allReqd) {\n              throw new Error('Not all branches were specified when using match. Specified: ' + branchKeys.join(', ') + '\\nRequired: ' + constructors.join(', '));\n            }\n            return branches[key].apply(null, args);\n          };\n          return {\n            fold: (...foldArgs) => {\n              if (foldArgs.length !== cases.length) {\n                throw new Error('Wrong number of arguments to fold. Expected ' + cases.length + ', got ' + foldArgs.length);\n              }\n              const target = foldArgs[count];\n              return target.apply(null, args);\n            },\n            match,\n            log: label => {\n              console.log(label, {\n                constructors,\n                constructor: key,\n                params: args\n              });\n            }\n          };\n        };\n      });\n      return adt;\n    };\n    const Adt = { generate };\n\n    const adt$6 = Adt.generate([\n      { none: [] },\n      { only: ['index'] },\n      {\n        left: [\n          'index',\n          'next'\n        ]\n      },\n      {\n        middle: [\n          'prev',\n          'index',\n          'next'\n        ]\n      },\n      {\n        right: [\n          'prev',\n          'index'\n        ]\n      }\n    ]);\n    const ColumnContext = { ...adt$6 };\n\n    const neighbours = (input, index) => {\n      if (input.length === 0) {\n        return ColumnContext.none();\n      }\n      if (input.length === 1) {\n        return ColumnContext.only(0);\n      }\n      if (index === 0) {\n        return ColumnContext.left(0, 1);\n      }\n      if (index === input.length - 1) {\n        return ColumnContext.right(index - 1, index);\n      }\n      if (index > 0 && index < input.length - 1) {\n        return ColumnContext.middle(index - 1, index, index + 1);\n      }\n      return ColumnContext.none();\n    };\n    const determine = (input, column, step, tableSize, resize) => {\n      const result = input.slice(0);\n      const context = neighbours(input, column);\n      const onNone = constant(map$1(result, constant(0)));\n      const onOnly = index => tableSize.singleColumnWidth(result[index], step);\n      const onLeft = (index, next) => resize.calcLeftEdgeDeltas(result, index, next, step, tableSize.minCellWidth(), tableSize.isRelative);\n      const onMiddle = (prev, index, next) => resize.calcMiddleDeltas(result, prev, index, next, step, tableSize.minCellWidth(), tableSize.isRelative);\n      const onRight = (prev, index) => resize.calcRightEdgeDeltas(result, prev, index, step, tableSize.minCellWidth(), tableSize.isRelative);\n      return context.fold(onNone, onOnly, onLeft, onMiddle, onRight);\n    };\n\n    const total = (start, end, measures) => {\n      let r = 0;\n      for (let i = start; i < end; i++) {\n        r += measures[i] !== undefined ? measures[i] : 0;\n      }\n      return r;\n    };\n    const recalculateWidthForCells = (warehouse, widths) => {\n      const all = Warehouse.justCells(warehouse);\n      return map$1(all, cell => {\n        const width = total(cell.column, cell.column + cell.colspan, widths);\n        return {\n          element: cell.element,\n          width,\n          colspan: cell.colspan\n        };\n      });\n    };\n    const recalculateWidthForColumns = (warehouse, widths) => {\n      const groups = Warehouse.justColumns(warehouse);\n      return map$1(groups, (column, index) => ({\n        element: column.element,\n        width: widths[index],\n        colspan: column.colspan\n      }));\n    };\n    const recalculateHeightForCells = (warehouse, heights) => {\n      const all = Warehouse.justCells(warehouse);\n      return map$1(all, cell => {\n        const height = total(cell.row, cell.row + cell.rowspan, heights);\n        return {\n          element: cell.element,\n          height,\n          rowspan: cell.rowspan\n        };\n      });\n    };\n    const matchRowHeight = (warehouse, heights) => {\n      return map$1(warehouse.all, (row, i) => {\n        return {\n          element: row.element,\n          height: heights[i]\n        };\n      });\n    };\n\n    const sumUp = newSize => foldr(newSize, (b, a) => b + a, 0);\n    const recalculate = (warehouse, widths) => {\n      if (Warehouse.hasColumns(warehouse)) {\n        return recalculateWidthForColumns(warehouse, widths);\n      } else {\n        return recalculateWidthForCells(warehouse, widths);\n      }\n    };\n    const recalculateAndApply = (warehouse, widths, tableSize) => {\n      const newSizes = recalculate(warehouse, widths);\n      each$2(newSizes, cell => {\n        tableSize.setElementWidth(cell.element, cell.width);\n      });\n    };\n    const adjustWidth = (table, delta, index, resizing, tableSize) => {\n      const warehouse = Warehouse.fromTable(table);\n      const step = tableSize.getCellDelta(delta);\n      const widths = tableSize.getWidths(warehouse, tableSize);\n      const isLastColumn = index === warehouse.grid.columns - 1;\n      const clampedStep = resizing.clampTableDelta(widths, index, step, tableSize.minCellWidth(), isLastColumn);\n      const deltas = determine(widths, index, clampedStep, tableSize, resizing);\n      const newWidths = map$1(deltas, (dx, i) => dx + widths[i]);\n      recalculateAndApply(warehouse, newWidths, tableSize);\n      resizing.resizeTable(tableSize.adjustTableWidth, clampedStep, isLastColumn);\n    };\n    const adjustHeight = (table, delta, index, direction) => {\n      const warehouse = Warehouse.fromTable(table);\n      const heights = getPixelHeights(warehouse, table, direction);\n      const newHeights = map$1(heights, (dy, i) => index === i ? Math.max(delta + dy, minHeight()) : dy);\n      const newCellSizes = recalculateHeightForCells(warehouse, newHeights);\n      const newRowSizes = matchRowHeight(warehouse, newHeights);\n      each$2(newRowSizes, row => {\n        setHeight(row.element, row.height);\n      });\n      each$2(newCellSizes, cell => {\n        setHeight(cell.element, cell.height);\n      });\n      const total = sumUp(newHeights);\n      setHeight(table, total);\n    };\n    const adjustAndRedistributeWidths$1 = (_table, list, details, tableSize, resizeBehaviour) => {\n      const warehouse = Warehouse.generate(list);\n      const sizes = tableSize.getWidths(warehouse, tableSize);\n      const tablePixelWidth = tableSize.pixelWidth();\n      const {newSizes, delta} = resizeBehaviour.calcRedestributedWidths(sizes, tablePixelWidth, details.pixelDelta, tableSize.isRelative);\n      recalculateAndApply(warehouse, newSizes, tableSize);\n      tableSize.adjustTableWidth(delta);\n    };\n    const adjustWidthTo = (_table, list, _info, tableSize) => {\n      const warehouse = Warehouse.generate(list);\n      const widths = tableSize.getWidths(warehouse, tableSize);\n      recalculateAndApply(warehouse, widths, tableSize);\n    };\n\n    const uniqueColumns = details => {\n      const uniqueCheck = (rest, detail) => {\n        const columnExists = exists(rest, currentDetail => currentDetail.column === detail.column);\n        return columnExists ? rest : rest.concat([detail]);\n      };\n      return foldl(details, uniqueCheck, []).sort((detailA, detailB) => detailA.column - detailB.column);\n    };\n\n    const isCol = isTag('col');\n    const isColgroup = isTag('colgroup');\n    const isRow$1 = element => name(element) === 'tr' || isColgroup(element);\n    const elementToData = element => {\n      const colspan = getAttrValue(element, 'colspan', 1);\n      const rowspan = getAttrValue(element, 'rowspan', 1);\n      return {\n        element,\n        colspan,\n        rowspan\n      };\n    };\n    const modification = (generators, toData = elementToData) => {\n      const nuCell = data => isCol(data.element) ? generators.col(data) : generators.cell(data);\n      const nuRow = data => isColgroup(data.element) ? generators.colgroup(data) : generators.row(data);\n      const add = element => {\n        if (isRow$1(element)) {\n          return nuRow({ element });\n        } else {\n          const cell = element;\n          const replacement = nuCell(toData(cell));\n          recent = Optional.some({\n            item: cell,\n            replacement\n          });\n          return replacement;\n        }\n      };\n      let recent = Optional.none();\n      const getOrInit = (element, comparator) => {\n        return recent.fold(() => {\n          return add(element);\n        }, p => {\n          return comparator(element, p.item) ? p.replacement : add(element);\n        });\n      };\n      return { getOrInit };\n    };\n    const transform$1 = tag => {\n      return generators => {\n        const list = [];\n        const find = (element, comparator) => {\n          return find$1(list, x => {\n            return comparator(x.item, element);\n          });\n        };\n        const makeNew = element => {\n          const attrs = tag === 'td' ? { scope: null } : {};\n          const cell = generators.replace(element, tag, attrs);\n          list.push({\n            item: element,\n            sub: cell\n          });\n          return cell;\n        };\n        const replaceOrInit = (element, comparator) => {\n          if (isRow$1(element) || isCol(element)) {\n            return element;\n          } else {\n            const cell = element;\n            return find(cell, comparator).fold(() => {\n              return makeNew(cell);\n            }, p => {\n              return comparator(element, p.item) ? p.sub : makeNew(cell);\n            });\n          }\n        };\n        return { replaceOrInit };\n      };\n    };\n    const getScopeAttribute = cell => getOpt(cell, 'scope').map(attribute => attribute.substr(0, 3));\n    const merging = generators => {\n      const unmerge = cell => {\n        const scope = getScopeAttribute(cell);\n        scope.each(attribute => set$2(cell, 'scope', attribute));\n        return () => {\n          const raw = generators.cell({\n            element: cell,\n            colspan: 1,\n            rowspan: 1\n          });\n          remove$5(raw, 'width');\n          remove$5(cell, 'width');\n          scope.each(attribute => set$2(raw, 'scope', attribute));\n          return raw;\n        };\n      };\n      const merge = cells => {\n        const getScopeProperty = () => {\n          const stringAttributes = cat(map$1(cells, getScopeAttribute));\n          if (stringAttributes.length === 0) {\n            return Optional.none();\n          } else {\n            const baseScope = stringAttributes[0];\n            const scopes = [\n              'row',\n              'col'\n            ];\n            const isMixed = exists(stringAttributes, attribute => {\n              return attribute !== baseScope && contains$2(scopes, attribute);\n            });\n            return isMixed ? Optional.none() : Optional.from(baseScope);\n          }\n        };\n        remove$5(cells[0], 'width');\n        getScopeProperty().fold(() => remove$7(cells[0], 'scope'), attribute => set$2(cells[0], 'scope', attribute + 'group'));\n        return constant(cells[0]);\n      };\n      return {\n        unmerge,\n        merge\n      };\n    };\n    const Generators = {\n      modification,\n      transform: transform$1,\n      merging\n    };\n\n    const blockList = [\n      'body',\n      'p',\n      'div',\n      'article',\n      'aside',\n      'figcaption',\n      'figure',\n      'footer',\n      'header',\n      'nav',\n      'section',\n      'ol',\n      'ul',\n      'table',\n      'thead',\n      'tfoot',\n      'tbody',\n      'caption',\n      'tr',\n      'td',\n      'th',\n      'h1',\n      'h2',\n      'h3',\n      'h4',\n      'h5',\n      'h6',\n      'blockquote',\n      'pre',\n      'address'\n    ];\n    const isList$1 = (universe, item) => {\n      const tagName = universe.property().name(item);\n      return contains$2([\n        'ol',\n        'ul'\n      ], tagName);\n    };\n    const isBlock$1 = (universe, item) => {\n      const tagName = universe.property().name(item);\n      return contains$2(blockList, tagName);\n    };\n    const isEmptyTag$1 = (universe, item) => {\n      return contains$2([\n        'br',\n        'img',\n        'hr',\n        'input'\n      ], universe.property().name(item));\n    };\n\n    const universe$1 = DomUniverse();\n    const isBlock = element => {\n      return isBlock$1(universe$1, element);\n    };\n    const isList = element => {\n      return isList$1(universe$1, element);\n    };\n    const isEmptyTag = element => {\n      return isEmptyTag$1(universe$1, element);\n    };\n\n    const merge = cells => {\n      const isBr = isTag('br');\n      const advancedBr = children => {\n        return forall(children, c => {\n          return isBr(c) || isText(c) && get$6(c).trim().length === 0;\n        });\n      };\n      const isListItem = el => {\n        return name(el) === 'li' || ancestor$2(el, isList).isSome();\n      };\n      const siblingIsBlock = el => {\n        return nextSibling(el).map(rightSibling => {\n          if (isBlock(rightSibling)) {\n            return true;\n          }\n          if (isEmptyTag(rightSibling)) {\n            return name(rightSibling) === 'img' ? false : true;\n          }\n          return false;\n        }).getOr(false);\n      };\n      const markCell = cell => {\n        return last$1(cell).bind(rightEdge => {\n          const rightSiblingIsBlock = siblingIsBlock(rightEdge);\n          return parent(rightEdge).map(parent => {\n            return rightSiblingIsBlock === true || isListItem(parent) || isBr(rightEdge) || isBlock(parent) && !eq$1(cell, parent) ? [] : [SugarElement.fromTag('br')];\n          });\n        }).getOr([]);\n      };\n      const markContent = () => {\n        const content = bind$2(cells, cell => {\n          const children = children$2(cell);\n          return advancedBr(children) ? [] : children.concat(markCell(cell));\n        });\n        return content.length === 0 ? [SugarElement.fromTag('br')] : content;\n      };\n      const contents = markContent();\n      empty(cells[0]);\n      append(cells[0], contents);\n    };\n\n    const isEditable = elem => isEditable$1(elem, true);\n    const prune = table => {\n      const cells = cells$1(table);\n      if (cells.length === 0) {\n        remove$6(table);\n      }\n    };\n    const outcome = (grid, cursor) => ({\n      grid,\n      cursor\n    });\n    const findEditableCursorPosition = rows => findMap(rows, row => findMap(row.cells, cell => {\n      const elem = cell.element;\n      return someIf(isEditable(elem), elem);\n    }));\n    const elementFromGrid = (grid, row, column) => {\n      var _a, _b;\n      const rows = extractGridDetails(grid).rows;\n      return Optional.from((_b = (_a = rows[row]) === null || _a === void 0 ? void 0 : _a.cells[column]) === null || _b === void 0 ? void 0 : _b.element).filter(isEditable).orThunk(() => findEditableCursorPosition(rows));\n    };\n    const bundle = (grid, row, column) => {\n      const cursorElement = elementFromGrid(grid, row, column);\n      return outcome(grid, cursorElement);\n    };\n    const uniqueRows = details => {\n      const rowCompilation = (rest, detail) => {\n        const rowExists = exists(rest, currentDetail => currentDetail.row === detail.row);\n        return rowExists ? rest : rest.concat([detail]);\n      };\n      return foldl(details, rowCompilation, []).sort((detailA, detailB) => detailA.row - detailB.row);\n    };\n    const opInsertRowsBefore = (grid, details, comparator, genWrappers) => {\n      const targetIndex = details[0].row;\n      const rows = uniqueRows(details);\n      const newGrid = foldr(rows, (acc, row) => {\n        const newG = insertRowAt(acc.grid, targetIndex, row.row + acc.delta, comparator, genWrappers.getOrInit);\n        return {\n          grid: newG,\n          delta: acc.delta + 1\n        };\n      }, {\n        grid,\n        delta: 0\n      }).grid;\n      return bundle(newGrid, targetIndex, details[0].column);\n    };\n    const opInsertRowsAfter = (grid, details, comparator, genWrappers) => {\n      const rows = uniqueRows(details);\n      const target = rows[rows.length - 1];\n      const targetIndex = target.row + target.rowspan;\n      const newGrid = foldr(rows, (newG, row) => {\n        return insertRowAt(newG, targetIndex, row.row, comparator, genWrappers.getOrInit);\n      }, grid);\n      return bundle(newGrid, targetIndex, details[0].column);\n    };\n    const opInsertColumnsBefore = (grid, extractDetail, comparator, genWrappers) => {\n      const details = extractDetail.details;\n      const columns = uniqueColumns(details);\n      const targetIndex = columns[0].column;\n      const newGrid = foldr(columns, (acc, col) => {\n        const newG = insertColumnAt(acc.grid, targetIndex, col.column + acc.delta, comparator, genWrappers.getOrInit);\n        return {\n          grid: newG,\n          delta: acc.delta + 1\n        };\n      }, {\n        grid,\n        delta: 0\n      }).grid;\n      return bundle(newGrid, details[0].row, targetIndex);\n    };\n    const opInsertColumnsAfter = (grid, extractDetail, comparator, genWrappers) => {\n      const details = extractDetail.details;\n      const target = details[details.length - 1];\n      const targetIndex = target.column + target.colspan;\n      const columns = uniqueColumns(details);\n      const newGrid = foldr(columns, (newG, col) => {\n        return insertColumnAt(newG, targetIndex, col.column, comparator, genWrappers.getOrInit);\n      }, grid);\n      return bundle(newGrid, details[0].row, targetIndex);\n    };\n    const opMakeColumnsHeader = (initialGrid, details, comparator, genWrappers) => {\n      const columns = uniqueColumns(details);\n      const columnIndexes = map$1(columns, detail => detail.column);\n      const newGrid = replaceColumns(initialGrid, columnIndexes, true, comparator, genWrappers.replaceOrInit);\n      return bundle(newGrid, details[0].row, details[0].column);\n    };\n    const opMakeCellsHeader = (initialGrid, details, comparator, genWrappers) => {\n      const newGrid = replaceCells(initialGrid, details, comparator, genWrappers.replaceOrInit);\n      return bundle(newGrid, details[0].row, details[0].column);\n    };\n    const opUnmakeColumnsHeader = (initialGrid, details, comparator, genWrappers) => {\n      const columns = uniqueColumns(details);\n      const columnIndexes = map$1(columns, detail => detail.column);\n      const newGrid = replaceColumns(initialGrid, columnIndexes, false, comparator, genWrappers.replaceOrInit);\n      return bundle(newGrid, details[0].row, details[0].column);\n    };\n    const opUnmakeCellsHeader = (initialGrid, details, comparator, genWrappers) => {\n      const newGrid = replaceCells(initialGrid, details, comparator, genWrappers.replaceOrInit);\n      return bundle(newGrid, details[0].row, details[0].column);\n    };\n    const makeRowsSection = (section, applyScope) => (initialGrid, details, comparator, genWrappers, tableSection) => {\n      const rows = uniqueRows(details);\n      const rowIndexes = map$1(rows, detail => detail.row);\n      const newGrid = replaceRows(initialGrid, rowIndexes, section, applyScope, comparator, genWrappers.replaceOrInit, tableSection);\n      return bundle(newGrid, details[0].row, details[0].column);\n    };\n    const opMakeRowsHeader = makeRowsSection('thead', true);\n    const opMakeRowsBody = makeRowsSection('tbody', false);\n    const opMakeRowsFooter = makeRowsSection('tfoot', false);\n    const opEraseColumns = (grid, extractDetail, _comparator, _genWrappers) => {\n      const columns = uniqueColumns(extractDetail.details);\n      const newGrid = deleteColumnsAt(grid, map$1(columns, column => column.column));\n      const maxColIndex = newGrid.length > 0 ? newGrid[0].cells.length - 1 : 0;\n      return bundle(newGrid, columns[0].row, Math.min(columns[0].column, maxColIndex));\n    };\n    const opEraseRows = (grid, details, _comparator, _genWrappers) => {\n      const rows = uniqueRows(details);\n      const newGrid = deleteRowsAt(grid, rows[0].row, rows[rows.length - 1].row);\n      const maxRowIndex = newGrid.length > 0 ? newGrid.length - 1 : 0;\n      return bundle(newGrid, Math.min(details[0].row, maxRowIndex), details[0].column);\n    };\n    const opMergeCells = (grid, mergable, comparator, genWrappers) => {\n      const cells = mergable.cells;\n      merge(cells);\n      const newGrid = merge$2(grid, mergable.bounds, comparator, genWrappers.merge(cells));\n      return outcome(newGrid, Optional.from(cells[0]));\n    };\n    const opUnmergeCells = (grid, unmergable, comparator, genWrappers) => {\n      const unmerge$1 = (b, cell) => unmerge(b, cell, comparator, genWrappers.unmerge(cell));\n      const newGrid = foldr(unmergable, unmerge$1, grid);\n      return outcome(newGrid, Optional.from(unmergable[0]));\n    };\n    const opPasteCells = (grid, pasteDetails, comparator, _genWrappers) => {\n      const gridify = (table, generators) => {\n        const wh = Warehouse.fromTable(table);\n        return toGrid(wh, generators, true);\n      };\n      const gridB = gridify(pasteDetails.clipboard, pasteDetails.generators);\n      const startAddress = address(pasteDetails.row, pasteDetails.column);\n      const mergedGrid = merge$1(startAddress, grid, gridB, pasteDetails.generators, comparator);\n      return mergedGrid.fold(() => outcome(grid, Optional.some(pasteDetails.element)), newGrid => {\n        return bundle(newGrid, pasteDetails.row, pasteDetails.column);\n      });\n    };\n    const gridifyRows = (rows, generators, context) => {\n      const pasteDetails = fromPastedRows(rows, context.section);\n      const wh = Warehouse.generate(pasteDetails);\n      return toGrid(wh, generators, true);\n    };\n    const opPasteColsBefore = (grid, pasteDetails, comparator, _genWrappers) => {\n      const rows = extractGridDetails(grid).rows;\n      const index = pasteDetails.cells[0].column;\n      const context = rows[pasteDetails.cells[0].row];\n      const gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);\n      const mergedGrid = insertCols(index, grid, gridB, pasteDetails.generators, comparator);\n      return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);\n    };\n    const opPasteColsAfter = (grid, pasteDetails, comparator, _genWrappers) => {\n      const rows = extractGridDetails(grid).rows;\n      const index = pasteDetails.cells[pasteDetails.cells.length - 1].column + pasteDetails.cells[pasteDetails.cells.length - 1].colspan;\n      const context = rows[pasteDetails.cells[0].row];\n      const gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);\n      const mergedGrid = insertCols(index, grid, gridB, pasteDetails.generators, comparator);\n      return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);\n    };\n    const opPasteRowsBefore = (grid, pasteDetails, comparator, _genWrappers) => {\n      const rows = extractGridDetails(grid).rows;\n      const index = pasteDetails.cells[0].row;\n      const context = rows[index];\n      const gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);\n      const mergedGrid = insertRows(index, grid, gridB, pasteDetails.generators, comparator);\n      return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);\n    };\n    const opPasteRowsAfter = (grid, pasteDetails, comparator, _genWrappers) => {\n      const rows = extractGridDetails(grid).rows;\n      const index = pasteDetails.cells[pasteDetails.cells.length - 1].row + pasteDetails.cells[pasteDetails.cells.length - 1].rowspan;\n      const context = rows[pasteDetails.cells[0].row];\n      const gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);\n      const mergedGrid = insertRows(index, grid, gridB, pasteDetails.generators, comparator);\n      return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);\n    };\n    const opGetColumnsType = (table, target) => {\n      const house = Warehouse.fromTable(table);\n      const details = onCells(house, target);\n      return details.bind(selectedCells => {\n        const lastSelectedCell = selectedCells[selectedCells.length - 1];\n        const minColRange = selectedCells[0].column;\n        const maxColRange = lastSelectedCell.column + lastSelectedCell.colspan;\n        const selectedColumnCells = flatten(map$1(house.all, row => filter$2(row.cells, cell => cell.column >= minColRange && cell.column < maxColRange)));\n        return findCommonCellType(selectedColumnCells);\n      }).getOr('');\n    };\n    const opGetCellsType = (table, target) => {\n      const house = Warehouse.fromTable(table);\n      const details = onCells(house, target);\n      return details.bind(findCommonCellType).getOr('');\n    };\n    const opGetRowsType = (table, target) => {\n      const house = Warehouse.fromTable(table);\n      const details = onCells(house, target);\n      return details.bind(selectedCells => {\n        const lastSelectedCell = selectedCells[selectedCells.length - 1];\n        const minRowRange = selectedCells[0].row;\n        const maxRowRange = lastSelectedCell.row + lastSelectedCell.rowspan;\n        const selectedRows = house.all.slice(minRowRange, maxRowRange);\n        return findCommonRowType(selectedRows);\n      }).getOr('');\n    };\n    const resize = (table, list, details, behaviours) => adjustWidthTo(table, list, details, behaviours.sizing);\n    const adjustAndRedistributeWidths = (table, list, details, behaviours) => adjustAndRedistributeWidths$1(table, list, details, behaviours.sizing, behaviours.resize);\n    const firstColumnIsLocked = (_warehouse, details) => exists(details, detail => detail.column === 0 && detail.isLocked);\n    const lastColumnIsLocked = (warehouse, details) => exists(details, detail => detail.column + detail.colspan >= warehouse.grid.columns && detail.isLocked);\n    const getColumnsWidth = (warehouse, details) => {\n      const columns$1 = columns(warehouse);\n      const uniqueCols = uniqueColumns(details);\n      return foldl(uniqueCols, (acc, detail) => {\n        const column = columns$1[detail.column];\n        const colWidth = column.map(getOuter$2).getOr(0);\n        return acc + colWidth;\n      }, 0);\n    };\n    const insertColumnsExtractor = before => (warehouse, target) => onCells(warehouse, target).filter(details => {\n      const checkLocked = before ? firstColumnIsLocked : lastColumnIsLocked;\n      return !checkLocked(warehouse, details);\n    }).map(details => ({\n      details,\n      pixelDelta: getColumnsWidth(warehouse, details)\n    }));\n    const eraseColumnsExtractor = (warehouse, target) => onUnlockedCells(warehouse, target).map(details => ({\n      details,\n      pixelDelta: -getColumnsWidth(warehouse, details)\n    }));\n    const pasteColumnsExtractor = before => (warehouse, target) => onPasteByEditor(warehouse, target).filter(details => {\n      const checkLocked = before ? firstColumnIsLocked : lastColumnIsLocked;\n      return !checkLocked(warehouse, details.cells);\n    });\n    const headerCellGenerator = Generators.transform('th');\n    const bodyCellGenerator = Generators.transform('td');\n    const insertRowsBefore = run(opInsertRowsBefore, onCells, noop, noop, Generators.modification);\n    const insertRowsAfter = run(opInsertRowsAfter, onCells, noop, noop, Generators.modification);\n    const insertColumnsBefore = run(opInsertColumnsBefore, insertColumnsExtractor(true), adjustAndRedistributeWidths, noop, Generators.modification);\n    const insertColumnsAfter = run(opInsertColumnsAfter, insertColumnsExtractor(false), adjustAndRedistributeWidths, noop, Generators.modification);\n    const eraseColumns = run(opEraseColumns, eraseColumnsExtractor, adjustAndRedistributeWidths, prune, Generators.modification);\n    const eraseRows = run(opEraseRows, onCells, noop, prune, Generators.modification);\n    const makeColumnsHeader = run(opMakeColumnsHeader, onUnlockedCells, noop, noop, headerCellGenerator);\n    const unmakeColumnsHeader = run(opUnmakeColumnsHeader, onUnlockedCells, noop, noop, bodyCellGenerator);\n    const makeRowsHeader = run(opMakeRowsHeader, onUnlockedCells, noop, noop, headerCellGenerator);\n    const makeRowsBody = run(opMakeRowsBody, onUnlockedCells, noop, noop, bodyCellGenerator);\n    const makeRowsFooter = run(opMakeRowsFooter, onUnlockedCells, noop, noop, bodyCellGenerator);\n    const makeCellsHeader = run(opMakeCellsHeader, onUnlockedCells, noop, noop, headerCellGenerator);\n    const unmakeCellsHeader = run(opUnmakeCellsHeader, onUnlockedCells, noop, noop, bodyCellGenerator);\n    const mergeCells = run(opMergeCells, onUnlockedMergable, resize, noop, Generators.merging);\n    const unmergeCells = run(opUnmergeCells, onUnlockedUnmergable, resize, noop, Generators.merging);\n    const pasteCells = run(opPasteCells, onPaste, resize, noop, Generators.modification);\n    const pasteColsBefore = run(opPasteColsBefore, pasteColumnsExtractor(true), noop, noop, Generators.modification);\n    const pasteColsAfter = run(opPasteColsAfter, pasteColumnsExtractor(false), noop, noop, Generators.modification);\n    const pasteRowsBefore = run(opPasteRowsBefore, onPasteByEditor, noop, noop, Generators.modification);\n    const pasteRowsAfter = run(opPasteRowsAfter, onPasteByEditor, noop, noop, Generators.modification);\n    const getColumnsType = opGetColumnsType;\n    const getCellsType = opGetCellsType;\n    const getRowsType = opGetRowsType;\n\n    const fireNewRow = (editor, row) => editor.dispatch('NewRow', { node: row });\n    const fireNewCell = (editor, cell) => editor.dispatch('NewCell', { node: cell });\n    const fireTableModified = (editor, table, data) => {\n      editor.dispatch('TableModified', {\n        ...data,\n        table\n      });\n    };\n    const fireTableSelectionChange = (editor, cells, start, finish, otherCells) => {\n      editor.dispatch('TableSelectionChange', {\n        cells,\n        start,\n        finish,\n        otherCells\n      });\n    };\n    const fireTableSelectionClear = editor => {\n      editor.dispatch('TableSelectionClear');\n    };\n    const fireObjectResizeStart = (editor, target, width, height, origin) => {\n      editor.dispatch('ObjectResizeStart', {\n        target,\n        width,\n        height,\n        origin\n      });\n    };\n    const fireObjectResized = (editor, target, width, height, origin) => {\n      editor.dispatch('ObjectResized', {\n        target,\n        width,\n        height,\n        origin\n      });\n    };\n    const styleModified = {\n      structure: false,\n      style: true\n    };\n    const structureModified = {\n      structure: true,\n      style: false\n    };\n    const styleAndStructureModified = {\n      structure: true,\n      style: true\n    };\n\n    const get$5 = (editor, table) => {\n      if (isTablePercentagesForced(editor)) {\n        return TableSize.percentageSize(table);\n      } else if (isTablePixelsForced(editor)) {\n        return TableSize.pixelSize(table);\n      } else {\n        return TableSize.getTableSize(table);\n      }\n    };\n\n    const TableActions = (editor, resizeHandler, cellSelectionHandler) => {\n      const isTableBody = editor => name(getBody(editor)) === 'table';\n      const lastRowGuard = table => !isTableBody(editor) || getGridSize(table).rows > 1;\n      const lastColumnGuard = table => !isTableBody(editor) || getGridSize(table).columns > 1;\n      const cloneFormats = getTableCloneElements(editor);\n      const colMutationOp = isResizeTableColumnResizing(editor) ? noop : halve;\n      const getTableSectionType = table => {\n        switch (getTableHeaderType(editor)) {\n        case 'section':\n          return TableSection.section();\n        case 'sectionCells':\n          return TableSection.sectionCells();\n        case 'cells':\n          return TableSection.cells();\n        default:\n          return TableSection.getTableSectionType(table, 'section');\n        }\n      };\n      const setSelectionFromAction = (table, result) => result.cursor.fold(() => {\n        const cells = cells$1(table);\n        return head(cells).filter(inBody).map(firstCell => {\n          cellSelectionHandler.clearSelectedCells(table.dom);\n          const rng = editor.dom.createRng();\n          rng.selectNode(firstCell.dom);\n          editor.selection.setRng(rng);\n          set$2(firstCell, 'data-mce-selected', '1');\n          return rng;\n        });\n      }, cell => {\n        const des = freefallRtl(cell);\n        const rng = editor.dom.createRng();\n        rng.setStart(des.element.dom, des.offset);\n        rng.setEnd(des.element.dom, des.offset);\n        editor.selection.setRng(rng);\n        cellSelectionHandler.clearSelectedCells(table.dom);\n        return Optional.some(rng);\n      });\n      const execute = (operation, guard, mutate, effect) => (table, target, noEvents = false) => {\n        removeDataStyle(table);\n        const doc = SugarElement.fromDom(editor.getDoc());\n        const generators = cellOperations(mutate, doc, cloneFormats);\n        const behaviours = {\n          sizing: get$5(editor, table),\n          resize: isResizeTableColumnResizing(editor) ? resizeTable() : preserveTable(),\n          section: getTableSectionType(table)\n        };\n        return guard(table) ? operation(table, target, generators, behaviours).bind(result => {\n          resizeHandler.refresh(table.dom);\n          each$2(result.newRows, row => {\n            fireNewRow(editor, row.dom);\n          });\n          each$2(result.newCells, cell => {\n            fireNewCell(editor, cell.dom);\n          });\n          const range = setSelectionFromAction(table, result);\n          if (inBody(table)) {\n            removeDataStyle(table);\n            if (!noEvents) {\n              fireTableModified(editor, table.dom, effect);\n            }\n          }\n          return range.map(rng => ({\n            rng,\n            effect\n          }));\n        }) : Optional.none();\n      };\n      const deleteRow = execute(eraseRows, lastRowGuard, noop, structureModified);\n      const deleteColumn = execute(eraseColumns, lastColumnGuard, noop, structureModified);\n      const insertRowsBefore$1 = execute(insertRowsBefore, always, noop, structureModified);\n      const insertRowsAfter$1 = execute(insertRowsAfter, always, noop, structureModified);\n      const insertColumnsBefore$1 = execute(insertColumnsBefore, always, colMutationOp, structureModified);\n      const insertColumnsAfter$1 = execute(insertColumnsAfter, always, colMutationOp, structureModified);\n      const mergeCells$1 = execute(mergeCells, always, noop, structureModified);\n      const unmergeCells$1 = execute(unmergeCells, always, noop, structureModified);\n      const pasteColsBefore$1 = execute(pasteColsBefore, always, noop, structureModified);\n      const pasteColsAfter$1 = execute(pasteColsAfter, always, noop, structureModified);\n      const pasteRowsBefore$1 = execute(pasteRowsBefore, always, noop, structureModified);\n      const pasteRowsAfter$1 = execute(pasteRowsAfter, always, noop, structureModified);\n      const pasteCells$1 = execute(pasteCells, always, noop, styleAndStructureModified);\n      const makeCellsHeader$1 = execute(makeCellsHeader, always, noop, structureModified);\n      const unmakeCellsHeader$1 = execute(unmakeCellsHeader, always, noop, structureModified);\n      const makeColumnsHeader$1 = execute(makeColumnsHeader, always, noop, structureModified);\n      const unmakeColumnsHeader$1 = execute(unmakeColumnsHeader, always, noop, structureModified);\n      const makeRowsHeader$1 = execute(makeRowsHeader, always, noop, structureModified);\n      const makeRowsBody$1 = execute(makeRowsBody, always, noop, structureModified);\n      const makeRowsFooter$1 = execute(makeRowsFooter, always, noop, structureModified);\n      const getTableCellType = getCellsType;\n      const getTableColType = getColumnsType;\n      const getTableRowType = getRowsType;\n      return {\n        deleteRow,\n        deleteColumn,\n        insertRowsBefore: insertRowsBefore$1,\n        insertRowsAfter: insertRowsAfter$1,\n        insertColumnsBefore: insertColumnsBefore$1,\n        insertColumnsAfter: insertColumnsAfter$1,\n        mergeCells: mergeCells$1,\n        unmergeCells: unmergeCells$1,\n        pasteColsBefore: pasteColsBefore$1,\n        pasteColsAfter: pasteColsAfter$1,\n        pasteRowsBefore: pasteRowsBefore$1,\n        pasteRowsAfter: pasteRowsAfter$1,\n        pasteCells: pasteCells$1,\n        makeCellsHeader: makeCellsHeader$1,\n        unmakeCellsHeader: unmakeCellsHeader$1,\n        makeColumnsHeader: makeColumnsHeader$1,\n        unmakeColumnsHeader: unmakeColumnsHeader$1,\n        makeRowsHeader: makeRowsHeader$1,\n        makeRowsBody: makeRowsBody$1,\n        makeRowsFooter: makeRowsFooter$1,\n        getTableRowType,\n        getTableCellType,\n        getTableColType\n      };\n    };\n\n    const constrainSpan = (element, property, value) => {\n      const currentColspan = getAttrValue(element, property, 1);\n      if (value === 1 || currentColspan <= 1) {\n        remove$7(element, property);\n      } else {\n        set$2(element, property, Math.min(value, currentColspan));\n      }\n    };\n    const isColInRange = (minColRange, maxColRange) => cell => {\n      const endCol = cell.column + cell.colspan - 1;\n      const startCol = cell.column;\n      return endCol >= minColRange && startCol < maxColRange;\n    };\n    const generateColGroup = (house, minColRange, maxColRange) => {\n      if (Warehouse.hasColumns(house)) {\n        const colsToCopy = filter$2(Warehouse.justColumns(house), isColInRange(minColRange, maxColRange));\n        const copiedCols = map$1(colsToCopy, c => {\n          const clonedCol = deep(c.element);\n          constrainSpan(clonedCol, 'span', maxColRange - minColRange);\n          return clonedCol;\n        });\n        const fakeColgroup = SugarElement.fromTag('colgroup');\n        append(fakeColgroup, copiedCols);\n        return [fakeColgroup];\n      } else {\n        return [];\n      }\n    };\n    const generateRows = (house, minColRange, maxColRange) => map$1(house.all, row => {\n      const cellsToCopy = filter$2(row.cells, isColInRange(minColRange, maxColRange));\n      const copiedCells = map$1(cellsToCopy, cell => {\n        const clonedCell = deep(cell.element);\n        constrainSpan(clonedCell, 'colspan', maxColRange - minColRange);\n        return clonedCell;\n      });\n      const fakeTR = SugarElement.fromTag('tr');\n      append(fakeTR, copiedCells);\n      return fakeTR;\n    });\n    const copyCols = (table, target) => {\n      const house = Warehouse.fromTable(table);\n      const details = onUnlockedCells(house, target);\n      return details.map(selectedCells => {\n        const lastSelectedCell = selectedCells[selectedCells.length - 1];\n        const minColRange = selectedCells[0].column;\n        const maxColRange = lastSelectedCell.column + lastSelectedCell.colspan;\n        const fakeColGroups = generateColGroup(house, minColRange, maxColRange);\n        const fakeRows = generateRows(house, minColRange, maxColRange);\n        return [\n          ...fakeColGroups,\n          ...fakeRows\n        ];\n      });\n    };\n\n    const copyRows = (table, target, generators) => {\n      const warehouse = Warehouse.fromTable(table);\n      const details = onCells(warehouse, target);\n      return details.bind(selectedCells => {\n        const grid = toGrid(warehouse, generators, false);\n        const rows = extractGridDetails(grid).rows;\n        const slicedGrid = rows.slice(selectedCells[0].row, selectedCells[selectedCells.length - 1].row + selectedCells[selectedCells.length - 1].rowspan);\n        const filteredGrid = bind$2(slicedGrid, row => {\n          const newCells = filter$2(row.cells, cell => !cell.isLocked);\n          return newCells.length > 0 ? [{\n              ...row,\n              cells: newCells\n            }] : [];\n        });\n        const slicedDetails = toDetailList(filteredGrid);\n        return someIf(slicedDetails.length > 0, slicedDetails);\n      }).map(slicedDetails => copy(slicedDetails));\n    };\n\n    const adt$5 = Adt.generate([\n      { invalid: ['raw'] },\n      { pixels: ['value'] },\n      { percent: ['value'] }\n    ]);\n    const validateFor = (suffix, type, value) => {\n      const rawAmount = value.substring(0, value.length - suffix.length);\n      const amount = parseFloat(rawAmount);\n      return rawAmount === amount.toString() ? type(amount) : adt$5.invalid(value);\n    };\n    const from = value => {\n      if (endsWith(value, '%')) {\n        return validateFor('%', adt$5.percent, value);\n      }\n      if (endsWith(value, 'px')) {\n        return validateFor('px', adt$5.pixels, value);\n      }\n      return adt$5.invalid(value);\n    };\n    const Size = {\n      ...adt$5,\n      from\n    };\n\n    const redistributeToPercent = (widths, totalWidth) => {\n      return map$1(widths, w => {\n        const colType = Size.from(w);\n        return colType.fold(() => {\n          return w;\n        }, px => {\n          const ratio = px / totalWidth * 100;\n          return ratio + '%';\n        }, pc => {\n          return pc + '%';\n        });\n      });\n    };\n    const redistributeToPx = (widths, totalWidth, newTotalWidth) => {\n      const scale = newTotalWidth / totalWidth;\n      return map$1(widths, w => {\n        const colType = Size.from(w);\n        return colType.fold(() => {\n          return w;\n        }, px => {\n          return px * scale + 'px';\n        }, pc => {\n          return pc / 100 * newTotalWidth + 'px';\n        });\n      });\n    };\n    const redistributeEmpty = (newWidthType, columns) => {\n      const f = newWidthType.fold(() => constant(''), pixels => {\n        const num = pixels / columns;\n        return constant(num + 'px');\n      }, () => {\n        const num = 100 / columns;\n        return constant(num + '%');\n      });\n      return range$1(columns, f);\n    };\n    const redistributeValues = (newWidthType, widths, totalWidth) => {\n      return newWidthType.fold(() => {\n        return widths;\n      }, px => {\n        return redistributeToPx(widths, totalWidth, px);\n      }, _pc => {\n        return redistributeToPercent(widths, totalWidth);\n      });\n    };\n    const redistribute$1 = (widths, totalWidth, newWidth) => {\n      const newType = Size.from(newWidth);\n      const floats = forall(widths, s => {\n        return s === '0px';\n      }) ? redistributeEmpty(newType, widths.length) : redistributeValues(newType, widths, totalWidth);\n      return normalize(floats);\n    };\n    const sum = (values, fallback) => {\n      if (values.length === 0) {\n        return fallback;\n      }\n      return foldr(values, (rest, v) => {\n        return Size.from(v).fold(constant(0), identity, identity) + rest;\n      }, 0);\n    };\n    const roundDown = (num, unit) => {\n      const floored = Math.floor(num);\n      return {\n        value: floored + unit,\n        remainder: num - floored\n      };\n    };\n    const add$3 = (value, amount) => {\n      return Size.from(value).fold(constant(value), px => {\n        return px + amount + 'px';\n      }, pc => {\n        return pc + amount + '%';\n      });\n    };\n    const normalize = values => {\n      if (values.length === 0) {\n        return values;\n      }\n      const scan = foldr(values, (rest, value) => {\n        const info = Size.from(value).fold(() => ({\n          value,\n          remainder: 0\n        }), num => roundDown(num, 'px'), num => ({\n          value: num + '%',\n          remainder: 0\n        }));\n        return {\n          output: [info.value].concat(rest.output),\n          remainder: rest.remainder + info.remainder\n        };\n      }, {\n        output: [],\n        remainder: 0\n      });\n      const r = scan.output;\n      return r.slice(0, r.length - 1).concat([add$3(r[r.length - 1], Math.round(scan.remainder))]);\n    };\n    const validate = Size.from;\n\n    const redistributeToW = (newWidths, cells, unit) => {\n      each$2(cells, cell => {\n        const widths = newWidths.slice(cell.column, cell.colspan + cell.column);\n        const w = sum(widths, minWidth());\n        set$1(cell.element, 'width', w + unit);\n      });\n    };\n    const redistributeToColumns = (newWidths, columns, unit) => {\n      each$2(columns, (column, index) => {\n        const width = sum([newWidths[index]], minWidth());\n        set$1(column.element, 'width', width + unit);\n      });\n    };\n    const redistributeToH = (newHeights, rows, cells, unit) => {\n      each$2(cells, cell => {\n        const heights = newHeights.slice(cell.row, cell.rowspan + cell.row);\n        const h = sum(heights, minHeight());\n        set$1(cell.element, 'height', h + unit);\n      });\n      each$2(rows, (row, i) => {\n        set$1(row.element, 'height', newHeights[i]);\n      });\n    };\n    const getUnit = newSize => {\n      return validate(newSize).fold(constant('px'), constant('px'), constant('%'));\n    };\n    const redistribute = (table, optWidth, optHeight) => {\n      const warehouse = Warehouse.fromTable(table);\n      const rows = warehouse.all;\n      const cells = Warehouse.justCells(warehouse);\n      const columns = Warehouse.justColumns(warehouse);\n      optWidth.each(newWidth => {\n        const widthUnit = getUnit(newWidth);\n        const totalWidth = get$9(table);\n        const oldWidths = getRawWidths(warehouse, table);\n        const nuWidths = redistribute$1(oldWidths, totalWidth, newWidth);\n        if (Warehouse.hasColumns(warehouse)) {\n          redistributeToColumns(nuWidths, columns, widthUnit);\n        } else {\n          redistributeToW(nuWidths, cells, widthUnit);\n        }\n        set$1(table, 'width', newWidth);\n      });\n      optHeight.each(newHeight => {\n        const hUnit = getUnit(newHeight);\n        const totalHeight = get$8(table);\n        const oldHeights = getRawHeights(warehouse, table, height);\n        const nuHeights = redistribute$1(oldHeights, totalHeight, newHeight);\n        redistributeToH(nuHeights, rows, cells, hUnit);\n        set$1(table, 'height', newHeight);\n      });\n    };\n    const isPercentSizing = isPercentSizing$1;\n    const isPixelSizing = isPixelSizing$1;\n    const isNoneSizing = isNoneSizing$1;\n\n    const cleanupLegacyAttributes = element => {\n      remove$7(element, 'width');\n    };\n    const convertToPercentSize = table => {\n      const newWidth = getPercentTableWidth(table);\n      redistribute(table, Optional.some(newWidth), Optional.none());\n      cleanupLegacyAttributes(table);\n    };\n    const convertToPixelSize = table => {\n      const newWidth = getPixelTableWidth(table);\n      redistribute(table, Optional.some(newWidth), Optional.none());\n      cleanupLegacyAttributes(table);\n    };\n    const convertToNoneSize = table => {\n      remove$5(table, 'width');\n      const columns = columns$1(table);\n      const rowElements = columns.length > 0 ? columns : cells$1(table);\n      each$2(rowElements, cell => {\n        remove$5(cell, 'width');\n        cleanupLegacyAttributes(cell);\n      });\n      cleanupLegacyAttributes(table);\n    };\n\n    const DefaultRenderOptions = {\n      styles: {\n        'border-collapse': 'collapse',\n        'width': '100%'\n      },\n      attributes: { border: '1' },\n      colGroups: false\n    };\n    const tableHeaderCell = () => SugarElement.fromTag('th');\n    const tableCell = () => SugarElement.fromTag('td');\n    const tableColumn = () => SugarElement.fromTag('col');\n    const createRow = (columns, rowHeaders, columnHeaders, rowIndex) => {\n      const tr = SugarElement.fromTag('tr');\n      for (let j = 0; j < columns; j++) {\n        const td = rowIndex < rowHeaders || j < columnHeaders ? tableHeaderCell() : tableCell();\n        if (j < columnHeaders) {\n          set$2(td, 'scope', 'row');\n        }\n        if (rowIndex < rowHeaders) {\n          set$2(td, 'scope', 'col');\n        }\n        append$1(td, SugarElement.fromTag('br'));\n        append$1(tr, td);\n      }\n      return tr;\n    };\n    const createGroupRow = columns => {\n      const columnGroup = SugarElement.fromTag('colgroup');\n      range$1(columns, () => append$1(columnGroup, tableColumn()));\n      return columnGroup;\n    };\n    const createRows = (rows, columns, rowHeaders, columnHeaders) => range$1(rows, r => createRow(columns, rowHeaders, columnHeaders, r));\n    const render = (rows, columns, rowHeaders, columnHeaders, headerType, renderOpts = DefaultRenderOptions) => {\n      const table = SugarElement.fromTag('table');\n      const rowHeadersGoInThead = headerType !== 'cells';\n      setAll(table, renderOpts.styles);\n      setAll$1(table, renderOpts.attributes);\n      if (renderOpts.colGroups) {\n        append$1(table, createGroupRow(columns));\n      }\n      const actualRowHeaders = Math.min(rows, rowHeaders);\n      if (rowHeadersGoInThead && rowHeaders > 0) {\n        const thead = SugarElement.fromTag('thead');\n        append$1(table, thead);\n        const theadRowHeaders = headerType === 'sectionCells' ? actualRowHeaders : 0;\n        const theadRows = createRows(rowHeaders, columns, theadRowHeaders, columnHeaders);\n        append(thead, theadRows);\n      }\n      const tbody = SugarElement.fromTag('tbody');\n      append$1(table, tbody);\n      const numRows = rowHeadersGoInThead ? rows - actualRowHeaders : rows;\n      const numRowHeaders = rowHeadersGoInThead ? 0 : rowHeaders;\n      const tbodyRows = createRows(numRows, columns, numRowHeaders, columnHeaders);\n      append(tbody, tbodyRows);\n      return table;\n    };\n\n    const get$4 = element => element.dom.innerHTML;\n    const getOuter = element => {\n      const container = SugarElement.fromTag('div');\n      const clone = SugarElement.fromDom(element.dom.cloneNode(true));\n      append$1(container, clone);\n      return get$4(container);\n    };\n\n    const placeCaretInCell = (editor, cell) => {\n      editor.selection.select(cell.dom, true);\n      editor.selection.collapse(true);\n    };\n    const selectFirstCellInTable = (editor, tableElm) => {\n      descendant(tableElm, 'td,th').each(curry(placeCaretInCell, editor));\n    };\n    const fireEvents = (editor, table) => {\n      each$2(descendants(table, 'tr'), row => {\n        fireNewRow(editor, row.dom);\n        each$2(descendants(row, 'th,td'), cell => {\n          fireNewCell(editor, cell.dom);\n        });\n      });\n    };\n    const isPercentage = width => isString(width) && width.indexOf('%') !== -1;\n    const insert = (editor, columns, rows, colHeaders, rowHeaders) => {\n      const defaultStyles = getTableDefaultStyles(editor);\n      const options = {\n        styles: defaultStyles,\n        attributes: getTableDefaultAttributes(editor),\n        colGroups: tableUseColumnGroup(editor)\n      };\n      editor.undoManager.ignore(() => {\n        const table = render(rows, columns, rowHeaders, colHeaders, getTableHeaderType(editor), options);\n        set$2(table, 'data-mce-id', '__mce');\n        const html = getOuter(table);\n        editor.insertContent(html);\n        editor.addVisual();\n      });\n      return descendant(getBody(editor), 'table[data-mce-id=\"__mce\"]').map(table => {\n        if (isTablePixelsForced(editor)) {\n          convertToPixelSize(table);\n        } else if (isTableResponsiveForced(editor)) {\n          convertToNoneSize(table);\n        } else if (isTablePercentagesForced(editor) || isPercentage(defaultStyles.width)) {\n          convertToPercentSize(table);\n        }\n        removeDataStyle(table);\n        remove$7(table, 'data-mce-id');\n        fireEvents(editor, table);\n        selectFirstCellInTable(editor, table);\n        return table.dom;\n      }).getOrNull();\n    };\n    const insertTable = (editor, rows, columns, options = {}) => {\n      const checkInput = val => isNumber(val) && val > 0;\n      if (checkInput(rows) && checkInput(columns)) {\n        const headerRows = options.headerRows || 0;\n        const headerColumns = options.headerColumns || 0;\n        return insert(editor, columns, rows, headerColumns, headerRows);\n      } else {\n        console.error('Invalid values for mceInsertTable - rows and columns values are required to insert a table.');\n        return null;\n      }\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.FakeClipboard');\n\n    const tableTypeBase = 'x-tinymce/dom-table-';\n    const tableTypeRow = tableTypeBase + 'rows';\n    const tableTypeColumn = tableTypeBase + 'columns';\n    const setData = items => {\n      const fakeClipboardItem = global.FakeClipboardItem(items);\n      global.write([fakeClipboardItem]);\n    };\n    const getData = type => {\n      var _a;\n      const items = (_a = global.read()) !== null && _a !== void 0 ? _a : [];\n      return findMap(items, item => Optional.from(item.getType(type)));\n    };\n    const clearData = type => {\n      if (getData(type).isSome()) {\n        global.clear();\n      }\n    };\n    const setRows = rowsOpt => {\n      rowsOpt.fold(clearRows, rows => setData({ [tableTypeRow]: rows }));\n    };\n    const getRows = () => getData(tableTypeRow);\n    const clearRows = () => clearData(tableTypeRow);\n    const setColumns = columnsOpt => {\n      columnsOpt.fold(clearColumns, columns => setData({ [tableTypeColumn]: columns }));\n    };\n    const getColumns = () => getData(tableTypeColumn);\n    const clearColumns = () => clearData(tableTypeColumn);\n\n    const getSelectionStartCellOrCaption = editor => getSelectionCellOrCaption(getSelectionStart(editor), getIsRoot(editor)).filter(isInEditableContext$1);\n    const getSelectionStartCell = editor => getSelectionCell(getSelectionStart(editor), getIsRoot(editor)).filter(isInEditableContext$1);\n    const registerCommands = (editor, actions) => {\n      const isRoot = getIsRoot(editor);\n      const eraseTable = () => getSelectionStartCellOrCaption(editor).each(cellOrCaption => {\n        table(cellOrCaption, isRoot).filter(not(isRoot)).each(table => {\n          const cursor = SugarElement.fromText('');\n          after$5(table, cursor);\n          remove$6(table);\n          if (editor.dom.isEmpty(editor.getBody())) {\n            editor.setContent('');\n            editor.selection.setCursorLocation();\n          } else {\n            const rng = editor.dom.createRng();\n            rng.setStart(cursor.dom, 0);\n            rng.setEnd(cursor.dom, 0);\n            editor.selection.setRng(rng);\n            editor.nodeChanged();\n          }\n        });\n      });\n      const setSizingMode = sizing => getSelectionStartCellOrCaption(editor).each(cellOrCaption => {\n        const isForcedSizing = isTableResponsiveForced(editor) || isTablePixelsForced(editor) || isTablePercentagesForced(editor);\n        if (!isForcedSizing) {\n          table(cellOrCaption, isRoot).each(table => {\n            if (sizing === 'relative' && !isPercentSizing(table)) {\n              convertToPercentSize(table);\n            } else if (sizing === 'fixed' && !isPixelSizing(table)) {\n              convertToPixelSize(table);\n            } else if (sizing === 'responsive' && !isNoneSizing(table)) {\n              convertToNoneSize(table);\n            }\n            removeDataStyle(table);\n            fireTableModified(editor, table.dom, structureModified);\n          });\n        }\n      });\n      const getTableFromCell = cell => table(cell, isRoot);\n      const performActionOnSelection = action => getSelectionStartCell(editor).bind(cell => getTableFromCell(cell).map(table => action(table, cell)));\n      const toggleTableClass = (_ui, clazz) => {\n        performActionOnSelection(table => {\n          editor.formatter.toggle('tableclass', { value: clazz }, table.dom);\n          fireTableModified(editor, table.dom, styleModified);\n        });\n      };\n      const toggleTableCellClass = (_ui, clazz) => {\n        performActionOnSelection(table => {\n          const selectedCells = getCellsFromSelection(editor);\n          const allHaveClass = forall(selectedCells, cell => editor.formatter.match('tablecellclass', { value: clazz }, cell.dom));\n          const formatterAction = allHaveClass ? editor.formatter.remove : editor.formatter.apply;\n          each$2(selectedCells, cell => formatterAction('tablecellclass', { value: clazz }, cell.dom));\n          fireTableModified(editor, table.dom, styleModified);\n        });\n      };\n      const toggleCaption = () => {\n        getSelectionStartCellOrCaption(editor).each(cellOrCaption => {\n          table(cellOrCaption, isRoot).each(table => {\n            child(table, 'caption').fold(() => {\n              const caption = SugarElement.fromTag('caption');\n              append$1(caption, SugarElement.fromText('Caption'));\n              appendAt(table, caption, 0);\n              editor.selection.setCursorLocation(caption.dom, 0);\n            }, caption => {\n              if (isTag('caption')(cellOrCaption)) {\n                one('td', table).each(td => editor.selection.setCursorLocation(td.dom, 0));\n              }\n              remove$6(caption);\n            });\n            fireTableModified(editor, table.dom, structureModified);\n          });\n        });\n      };\n      const postExecute = _data => {\n        editor.focus();\n      };\n      const actOnSelection = (execute, noEvents = false) => performActionOnSelection((table, startCell) => {\n        const targets = forMenu(getCellsFromSelection(editor), table, startCell);\n        execute(table, targets, noEvents).each(postExecute);\n      });\n      const copyRowSelection = () => performActionOnSelection((table, startCell) => {\n        const targets = forMenu(getCellsFromSelection(editor), table, startCell);\n        const generators = cellOperations(noop, SugarElement.fromDom(editor.getDoc()), Optional.none());\n        return copyRows(table, targets, generators);\n      });\n      const copyColSelection = () => performActionOnSelection((table, startCell) => {\n        const targets = forMenu(getCellsFromSelection(editor), table, startCell);\n        return copyCols(table, targets);\n      });\n      const pasteOnSelection = (execute, getRows) => getRows().each(rows => {\n        const clonedRows = map$1(rows, row => deep(row));\n        performActionOnSelection((table, startCell) => {\n          const generators = paste$1(SugarElement.fromDom(editor.getDoc()));\n          const targets = pasteRows(getCellsFromSelection(editor), startCell, clonedRows, generators);\n          execute(table, targets).each(postExecute);\n        });\n      });\n      const actOnType = getAction => (_ui, args) => get$c(args, 'type').each(type => {\n        actOnSelection(getAction(type), args.no_events);\n      });\n      each$1({\n        mceTableSplitCells: () => actOnSelection(actions.unmergeCells),\n        mceTableMergeCells: () => actOnSelection(actions.mergeCells),\n        mceTableInsertRowBefore: () => actOnSelection(actions.insertRowsBefore),\n        mceTableInsertRowAfter: () => actOnSelection(actions.insertRowsAfter),\n        mceTableInsertColBefore: () => actOnSelection(actions.insertColumnsBefore),\n        mceTableInsertColAfter: () => actOnSelection(actions.insertColumnsAfter),\n        mceTableDeleteCol: () => actOnSelection(actions.deleteColumn),\n        mceTableDeleteRow: () => actOnSelection(actions.deleteRow),\n        mceTableCutCol: () => copyColSelection().each(selection => {\n          setColumns(selection);\n          actOnSelection(actions.deleteColumn);\n        }),\n        mceTableCutRow: () => copyRowSelection().each(selection => {\n          setRows(selection);\n          actOnSelection(actions.deleteRow);\n        }),\n        mceTableCopyCol: () => copyColSelection().each(selection => setColumns(selection)),\n        mceTableCopyRow: () => copyRowSelection().each(selection => setRows(selection)),\n        mceTablePasteColBefore: () => pasteOnSelection(actions.pasteColsBefore, getColumns),\n        mceTablePasteColAfter: () => pasteOnSelection(actions.pasteColsAfter, getColumns),\n        mceTablePasteRowBefore: () => pasteOnSelection(actions.pasteRowsBefore, getRows),\n        mceTablePasteRowAfter: () => pasteOnSelection(actions.pasteRowsAfter, getRows),\n        mceTableDelete: eraseTable,\n        mceTableCellToggleClass: toggleTableCellClass,\n        mceTableToggleClass: toggleTableClass,\n        mceTableToggleCaption: toggleCaption,\n        mceTableSizingMode: (_ui, sizing) => setSizingMode(sizing),\n        mceTableCellType: actOnType(type => type === 'th' ? actions.makeCellsHeader : actions.unmakeCellsHeader),\n        mceTableColType: actOnType(type => type === 'th' ? actions.makeColumnsHeader : actions.unmakeColumnsHeader),\n        mceTableRowType: actOnType(type => {\n          switch (type) {\n          case 'header':\n            return actions.makeRowsHeader;\n          case 'footer':\n            return actions.makeRowsFooter;\n          default:\n            return actions.makeRowsBody;\n          }\n        })\n      }, (func, name) => editor.addCommand(name, func));\n      editor.addCommand('mceInsertTable', (_ui, args) => {\n        insertTable(editor, args.rows, args.columns, args.options);\n      });\n      editor.addCommand('mceTableApplyCellStyle', (_ui, args) => {\n        const getFormatName = style => 'tablecell' + style.toLowerCase().replace('-', '');\n        if (!isObject(args)) {\n          return;\n        }\n        const cells = filter$2(getCellsFromSelection(editor), isInEditableContext$1);\n        if (cells.length === 0) {\n          return;\n        }\n        const validArgs = filter$1(args, (value, style) => editor.formatter.has(getFormatName(style)) && isString(value));\n        if (isEmpty(validArgs)) {\n          return;\n        }\n        each$1(validArgs, (value, style) => {\n          const formatName = getFormatName(style);\n          each$2(cells, cell => {\n            if (value === '') {\n              editor.formatter.remove(formatName, { value: null }, cell.dom, true);\n            } else {\n              editor.formatter.apply(formatName, { value }, cell.dom);\n            }\n          });\n        });\n        getTableFromCell(cells[0]).each(table => fireTableModified(editor, table.dom, styleModified));\n      });\n    };\n\n    const registerQueryCommands = (editor, actions) => {\n      const isRoot = getIsRoot(editor);\n      const lookupOnSelection = action => getSelectionCell(getSelectionStart(editor)).bind(cell => table(cell, isRoot).map(table => {\n        const targets = forMenu(getCellsFromSelection(editor), table, cell);\n        return action(table, targets);\n      })).getOr('');\n      each$1({\n        mceTableRowType: () => lookupOnSelection(actions.getTableRowType),\n        mceTableCellType: () => lookupOnSelection(actions.getTableCellType),\n        mceTableColType: () => lookupOnSelection(actions.getTableColType)\n      }, (func, name) => editor.addQueryValueHandler(name, func));\n    };\n\n    const adt$4 = Adt.generate([\n      { before: ['element'] },\n      {\n        on: [\n          'element',\n          'offset'\n        ]\n      },\n      { after: ['element'] }\n    ]);\n    const cata$1 = (subject, onBefore, onOn, onAfter) => subject.fold(onBefore, onOn, onAfter);\n    const getStart$1 = situ => situ.fold(identity, identity, identity);\n    const before$2 = adt$4.before;\n    const on = adt$4.on;\n    const after$3 = adt$4.after;\n    const Situ = {\n      before: before$2,\n      on,\n      after: after$3,\n      cata: cata$1,\n      getStart: getStart$1\n    };\n\n    const create$4 = (selection, kill) => ({\n      selection,\n      kill\n    });\n    const Response = { create: create$4 };\n\n    const selectNode = (win, element) => {\n      const rng = win.document.createRange();\n      rng.selectNode(element.dom);\n      return rng;\n    };\n    const selectNodeContents = (win, element) => {\n      const rng = win.document.createRange();\n      selectNodeContentsUsing(rng, element);\n      return rng;\n    };\n    const selectNodeContentsUsing = (rng, element) => rng.selectNodeContents(element.dom);\n    const setStart = (rng, situ) => {\n      situ.fold(e => {\n        rng.setStartBefore(e.dom);\n      }, (e, o) => {\n        rng.setStart(e.dom, o);\n      }, e => {\n        rng.setStartAfter(e.dom);\n      });\n    };\n    const setFinish = (rng, situ) => {\n      situ.fold(e => {\n        rng.setEndBefore(e.dom);\n      }, (e, o) => {\n        rng.setEnd(e.dom, o);\n      }, e => {\n        rng.setEndAfter(e.dom);\n      });\n    };\n    const relativeToNative = (win, startSitu, finishSitu) => {\n      const range = win.document.createRange();\n      setStart(range, startSitu);\n      setFinish(range, finishSitu);\n      return range;\n    };\n    const exactToNative = (win, start, soffset, finish, foffset) => {\n      const rng = win.document.createRange();\n      rng.setStart(start.dom, soffset);\n      rng.setEnd(finish.dom, foffset);\n      return rng;\n    };\n    const toRect = rect => ({\n      left: rect.left,\n      top: rect.top,\n      right: rect.right,\n      bottom: rect.bottom,\n      width: rect.width,\n      height: rect.height\n    });\n    const getFirstRect$1 = rng => {\n      const rects = rng.getClientRects();\n      const rect = rects.length > 0 ? rects[0] : rng.getBoundingClientRect();\n      return rect.width > 0 || rect.height > 0 ? Optional.some(rect).map(toRect) : Optional.none();\n    };\n\n    const adt$3 = Adt.generate([\n      {\n        ltr: [\n          'start',\n          'soffset',\n          'finish',\n          'foffset'\n        ]\n      },\n      {\n        rtl: [\n          'start',\n          'soffset',\n          'finish',\n          'foffset'\n        ]\n      }\n    ]);\n    const fromRange = (win, type, range) => type(SugarElement.fromDom(range.startContainer), range.startOffset, SugarElement.fromDom(range.endContainer), range.endOffset);\n    const getRanges = (win, selection) => selection.match({\n      domRange: rng => {\n        return {\n          ltr: constant(rng),\n          rtl: Optional.none\n        };\n      },\n      relative: (startSitu, finishSitu) => {\n        return {\n          ltr: cached(() => relativeToNative(win, startSitu, finishSitu)),\n          rtl: cached(() => Optional.some(relativeToNative(win, finishSitu, startSitu)))\n        };\n      },\n      exact: (start, soffset, finish, foffset) => {\n        return {\n          ltr: cached(() => exactToNative(win, start, soffset, finish, foffset)),\n          rtl: cached(() => Optional.some(exactToNative(win, finish, foffset, start, soffset)))\n        };\n      }\n    });\n    const doDiagnose = (win, ranges) => {\n      const rng = ranges.ltr();\n      if (rng.collapsed) {\n        const reversed = ranges.rtl().filter(rev => rev.collapsed === false);\n        return reversed.map(rev => adt$3.rtl(SugarElement.fromDom(rev.endContainer), rev.endOffset, SugarElement.fromDom(rev.startContainer), rev.startOffset)).getOrThunk(() => fromRange(win, adt$3.ltr, rng));\n      } else {\n        return fromRange(win, adt$3.ltr, rng);\n      }\n    };\n    const diagnose = (win, selection) => {\n      const ranges = getRanges(win, selection);\n      return doDiagnose(win, ranges);\n    };\n    const asLtrRange = (win, selection) => {\n      const diagnosis = diagnose(win, selection);\n      return diagnosis.match({\n        ltr: (start, soffset, finish, foffset) => {\n          const rng = win.document.createRange();\n          rng.setStart(start.dom, soffset);\n          rng.setEnd(finish.dom, foffset);\n          return rng;\n        },\n        rtl: (start, soffset, finish, foffset) => {\n          const rng = win.document.createRange();\n          rng.setStart(finish.dom, foffset);\n          rng.setEnd(start.dom, soffset);\n          return rng;\n        }\n      });\n    };\n    adt$3.ltr;\n    adt$3.rtl;\n\n    const create$3 = (start, soffset, finish, foffset) => ({\n      start,\n      soffset,\n      finish,\n      foffset\n    });\n    const SimRange = { create: create$3 };\n\n    const create$2 = (start, soffset, finish, foffset) => {\n      return {\n        start: Situ.on(start, soffset),\n        finish: Situ.on(finish, foffset)\n      };\n    };\n    const Situs = { create: create$2 };\n\n    const convertToRange = (win, selection) => {\n      const rng = asLtrRange(win, selection);\n      return SimRange.create(SugarElement.fromDom(rng.startContainer), rng.startOffset, SugarElement.fromDom(rng.endContainer), rng.endOffset);\n    };\n    const makeSitus = Situs.create;\n\n    const sync = (container, isRoot, start, soffset, finish, foffset, selectRange) => {\n      if (!(eq$1(start, finish) && soffset === foffset)) {\n        return closest$1(start, 'td,th', isRoot).bind(s => {\n          return closest$1(finish, 'td,th', isRoot).bind(f => {\n            return detect(container, isRoot, s, f, selectRange);\n          });\n        });\n      } else {\n        return Optional.none();\n      }\n    };\n    const detect = (container, isRoot, start, finish, selectRange) => {\n      if (!eq$1(start, finish)) {\n        return identify(start, finish, isRoot).bind(cellSel => {\n          const boxes = cellSel.boxes.getOr([]);\n          if (boxes.length > 1) {\n            selectRange(container, boxes, cellSel.start, cellSel.finish);\n            return Optional.some(Response.create(Optional.some(makeSitus(start, 0, start, getEnd(start))), true));\n          } else {\n            return Optional.none();\n          }\n        });\n      } else {\n        return Optional.none();\n      }\n    };\n    const update = (rows, columns, container, selected, annotations) => {\n      const updateSelection = newSels => {\n        annotations.clearBeforeUpdate(container);\n        annotations.selectRange(container, newSels.boxes, newSels.start, newSels.finish);\n        return newSels.boxes;\n      };\n      return shiftSelection(selected, rows, columns, annotations.firstSelectedSelector, annotations.lastSelectedSelector).map(updateSelection);\n    };\n\n    const traverse = (item, mode) => ({\n      item,\n      mode\n    });\n    const backtrack = (universe, item, _direction, transition = sidestep) => {\n      return universe.property().parent(item).map(p => {\n        return traverse(p, transition);\n      });\n    };\n    const sidestep = (universe, item, direction, transition = advance) => {\n      return direction.sibling(universe, item).map(p => {\n        return traverse(p, transition);\n      });\n    };\n    const advance = (universe, item, direction, transition = advance) => {\n      const children = universe.property().children(item);\n      const result = direction.first(children);\n      return result.map(r => {\n        return traverse(r, transition);\n      });\n    };\n    const successors = [\n      {\n        current: backtrack,\n        next: sidestep,\n        fallback: Optional.none()\n      },\n      {\n        current: sidestep,\n        next: advance,\n        fallback: Optional.some(backtrack)\n      },\n      {\n        current: advance,\n        next: advance,\n        fallback: Optional.some(sidestep)\n      }\n    ];\n    const go = (universe, item, mode, direction, rules = successors) => {\n      const ruleOpt = find$1(rules, succ => {\n        return succ.current === mode;\n      });\n      return ruleOpt.bind(rule => {\n        return rule.current(universe, item, direction, rule.next).orThunk(() => {\n          return rule.fallback.bind(fb => {\n            return go(universe, item, fb, direction);\n          });\n        });\n      });\n    };\n\n    const left$1 = () => {\n      const sibling = (universe, item) => {\n        return universe.query().prevSibling(item);\n      };\n      const first = children => {\n        return children.length > 0 ? Optional.some(children[children.length - 1]) : Optional.none();\n      };\n      return {\n        sibling,\n        first\n      };\n    };\n    const right$1 = () => {\n      const sibling = (universe, item) => {\n        return universe.query().nextSibling(item);\n      };\n      const first = children => {\n        return children.length > 0 ? Optional.some(children[0]) : Optional.none();\n      };\n      return {\n        sibling,\n        first\n      };\n    };\n    const Walkers = {\n      left: left$1,\n      right: right$1\n    };\n\n    const hone = (universe, item, predicate, mode, direction, isRoot) => {\n      const next = go(universe, item, mode, direction);\n      return next.bind(n => {\n        if (isRoot(n.item)) {\n          return Optional.none();\n        } else {\n          return predicate(n.item) ? Optional.some(n.item) : hone(universe, n.item, predicate, n.mode, direction, isRoot);\n        }\n      });\n    };\n    const left = (universe, item, predicate, isRoot) => {\n      return hone(universe, item, predicate, sidestep, Walkers.left(), isRoot);\n    };\n    const right = (universe, item, predicate, isRoot) => {\n      return hone(universe, item, predicate, sidestep, Walkers.right(), isRoot);\n    };\n\n    const isLeaf = universe => element => universe.property().children(element).length === 0;\n    const before$1 = (universe, item, isRoot) => {\n      return seekLeft$1(universe, item, isLeaf(universe), isRoot);\n    };\n    const after$2 = (universe, item, isRoot) => {\n      return seekRight$1(universe, item, isLeaf(universe), isRoot);\n    };\n    const seekLeft$1 = left;\n    const seekRight$1 = right;\n\n    const universe = DomUniverse();\n    const before = (element, isRoot) => {\n      return before$1(universe, element, isRoot);\n    };\n    const after$1 = (element, isRoot) => {\n      return after$2(universe, element, isRoot);\n    };\n    const seekLeft = (element, predicate, isRoot) => {\n      return seekLeft$1(universe, element, predicate, isRoot);\n    };\n    const seekRight = (element, predicate, isRoot) => {\n      return seekRight$1(universe, element, predicate, isRoot);\n    };\n\n    const ancestor = (scope, predicate, isRoot) => ancestor$2(scope, predicate, isRoot).isSome();\n\n    const adt$2 = Adt.generate([\n      { none: ['message'] },\n      { success: [] },\n      { failedUp: ['cell'] },\n      { failedDown: ['cell'] }\n    ]);\n    const isOverlapping = (bridge, before, after) => {\n      const beforeBounds = bridge.getRect(before);\n      const afterBounds = bridge.getRect(after);\n      return afterBounds.right > beforeBounds.left && afterBounds.left < beforeBounds.right;\n    };\n    const isRow = elem => {\n      return closest$1(elem, 'tr');\n    };\n    const verify = (bridge, before, beforeOffset, after, afterOffset, failure, isRoot) => {\n      return closest$1(after, 'td,th', isRoot).bind(afterCell => {\n        return closest$1(before, 'td,th', isRoot).map(beforeCell => {\n          if (!eq$1(afterCell, beforeCell)) {\n            return sharedOne(isRow, [\n              afterCell,\n              beforeCell\n            ]).fold(() => {\n              return isOverlapping(bridge, beforeCell, afterCell) ? adt$2.success() : failure(beforeCell);\n            }, _sharedRow => {\n              return failure(beforeCell);\n            });\n          } else {\n            return eq$1(after, afterCell) && getEnd(afterCell) === afterOffset ? failure(beforeCell) : adt$2.none('in same cell');\n          }\n        });\n      }).getOr(adt$2.none('default'));\n    };\n    const cata = (subject, onNone, onSuccess, onFailedUp, onFailedDown) => {\n      return subject.fold(onNone, onSuccess, onFailedUp, onFailedDown);\n    };\n    const BeforeAfter = {\n      ...adt$2,\n      verify,\n      cata\n    };\n\n    const inParent = (parent, children, element, index) => ({\n      parent,\n      children,\n      element,\n      index\n    });\n    const indexInParent = element => parent(element).bind(parent => {\n      const children = children$2(parent);\n      return indexOf(children, element).map(index => inParent(parent, children, element, index));\n    });\n    const indexOf = (elements, element) => findIndex(elements, curry(eq$1, element));\n\n    const isBr = isTag('br');\n    const gatherer = (cand, gather, isRoot) => {\n      return gather(cand, isRoot).bind(target => {\n        return isText(target) && get$6(target).trim().length === 0 ? gatherer(target, gather, isRoot) : Optional.some(target);\n      });\n    };\n    const handleBr = (isRoot, element, direction) => {\n      return direction.traverse(element).orThunk(() => {\n        return gatherer(element, direction.gather, isRoot);\n      }).map(direction.relative);\n    };\n    const findBr = (element, offset) => {\n      return child$2(element, offset).filter(isBr).orThunk(() => {\n        return child$2(element, offset - 1).filter(isBr);\n      });\n    };\n    const handleParent = (isRoot, element, offset, direction) => {\n      return findBr(element, offset).bind(br => {\n        return direction.traverse(br).fold(() => {\n          return gatherer(br, direction.gather, isRoot).map(direction.relative);\n        }, adjacent => {\n          return indexInParent(adjacent).map(info => {\n            return Situ.on(info.parent, info.index);\n          });\n        });\n      });\n    };\n    const tryBr = (isRoot, element, offset, direction) => {\n      const target = isBr(element) ? handleBr(isRoot, element, direction) : handleParent(isRoot, element, offset, direction);\n      return target.map(tgt => {\n        return {\n          start: tgt,\n          finish: tgt\n        };\n      });\n    };\n    const process = analysis => {\n      return BeforeAfter.cata(analysis, _message => {\n        return Optional.none();\n      }, () => {\n        return Optional.none();\n      }, cell => {\n        return Optional.some(point(cell, 0));\n      }, cell => {\n        return Optional.some(point(cell, getEnd(cell)));\n      });\n    };\n\n    const moveDown = (caret, amount) => {\n      return {\n        left: caret.left,\n        top: caret.top + amount,\n        right: caret.right,\n        bottom: caret.bottom + amount\n      };\n    };\n    const moveUp = (caret, amount) => {\n      return {\n        left: caret.left,\n        top: caret.top - amount,\n        right: caret.right,\n        bottom: caret.bottom - amount\n      };\n    };\n    const translate = (caret, xDelta, yDelta) => {\n      return {\n        left: caret.left + xDelta,\n        top: caret.top + yDelta,\n        right: caret.right + xDelta,\n        bottom: caret.bottom + yDelta\n      };\n    };\n    const getTop = caret => {\n      return caret.top;\n    };\n    const getBottom = caret => {\n      return caret.bottom;\n    };\n\n    const getPartialBox = (bridge, element, offset) => {\n      if (offset >= 0 && offset < getEnd(element)) {\n        return bridge.getRangedRect(element, offset, element, offset + 1);\n      } else if (offset > 0) {\n        return bridge.getRangedRect(element, offset - 1, element, offset);\n      }\n      return Optional.none();\n    };\n    const toCaret = rect => ({\n      left: rect.left,\n      top: rect.top,\n      right: rect.right,\n      bottom: rect.bottom\n    });\n    const getElemBox = (bridge, element) => {\n      return Optional.some(bridge.getRect(element));\n    };\n    const getBoxAt = (bridge, element, offset) => {\n      if (isElement(element)) {\n        return getElemBox(bridge, element).map(toCaret);\n      } else if (isText(element)) {\n        return getPartialBox(bridge, element, offset).map(toCaret);\n      } else {\n        return Optional.none();\n      }\n    };\n    const getEntireBox = (bridge, element) => {\n      if (isElement(element)) {\n        return getElemBox(bridge, element).map(toCaret);\n      } else if (isText(element)) {\n        return bridge.getRangedRect(element, 0, element, getEnd(element)).map(toCaret);\n      } else {\n        return Optional.none();\n      }\n    };\n\n    const JUMP_SIZE = 5;\n    const NUM_RETRIES = 100;\n    const adt$1 = Adt.generate([\n      { none: [] },\n      { retry: ['caret'] }\n    ]);\n    const isOutside = (caret, box) => {\n      return caret.left < box.left || Math.abs(box.right - caret.left) < 1 || caret.left > box.right;\n    };\n    const inOutsideBlock = (bridge, element, caret) => {\n      return closest$2(element, isBlock).fold(never, cell => {\n        return getEntireBox(bridge, cell).exists(box => {\n          return isOutside(caret, box);\n        });\n      });\n    };\n    const adjustDown = (bridge, element, guessBox, original, caret) => {\n      const lowerCaret = moveDown(caret, JUMP_SIZE);\n      if (Math.abs(guessBox.bottom - original.bottom) < 1) {\n        return adt$1.retry(lowerCaret);\n      } else if (guessBox.top > caret.bottom) {\n        return adt$1.retry(lowerCaret);\n      } else if (guessBox.top === caret.bottom) {\n        return adt$1.retry(moveDown(caret, 1));\n      } else {\n        return inOutsideBlock(bridge, element, caret) ? adt$1.retry(translate(lowerCaret, JUMP_SIZE, 0)) : adt$1.none();\n      }\n    };\n    const adjustUp = (bridge, element, guessBox, original, caret) => {\n      const higherCaret = moveUp(caret, JUMP_SIZE);\n      if (Math.abs(guessBox.top - original.top) < 1) {\n        return adt$1.retry(higherCaret);\n      } else if (guessBox.bottom < caret.top) {\n        return adt$1.retry(higherCaret);\n      } else if (guessBox.bottom === caret.top) {\n        return adt$1.retry(moveUp(caret, 1));\n      } else {\n        return inOutsideBlock(bridge, element, caret) ? adt$1.retry(translate(higherCaret, JUMP_SIZE, 0)) : adt$1.none();\n      }\n    };\n    const upMovement = {\n      point: getTop,\n      adjuster: adjustUp,\n      move: moveUp,\n      gather: before\n    };\n    const downMovement = {\n      point: getBottom,\n      adjuster: adjustDown,\n      move: moveDown,\n      gather: after$1\n    };\n    const isAtTable = (bridge, x, y) => {\n      return bridge.elementFromPoint(x, y).filter(elm => {\n        return name(elm) === 'table';\n      }).isSome();\n    };\n    const adjustForTable = (bridge, movement, original, caret, numRetries) => {\n      return adjustTil(bridge, movement, original, movement.move(caret, JUMP_SIZE), numRetries);\n    };\n    const adjustTil = (bridge, movement, original, caret, numRetries) => {\n      if (numRetries === 0) {\n        return Optional.some(caret);\n      }\n      if (isAtTable(bridge, caret.left, movement.point(caret))) {\n        return adjustForTable(bridge, movement, original, caret, numRetries - 1);\n      }\n      return bridge.situsFromPoint(caret.left, movement.point(caret)).bind(guess => {\n        return guess.start.fold(Optional.none, element => {\n          return getEntireBox(bridge, element).bind(guessBox => {\n            return movement.adjuster(bridge, element, guessBox, original, caret).fold(Optional.none, newCaret => {\n              return adjustTil(bridge, movement, original, newCaret, numRetries - 1);\n            });\n          }).orThunk(() => {\n            return Optional.some(caret);\n          });\n        }, Optional.none);\n      });\n    };\n    const checkScroll = (movement, adjusted, bridge) => {\n      if (movement.point(adjusted) > bridge.getInnerHeight()) {\n        return Optional.some(movement.point(adjusted) - bridge.getInnerHeight());\n      } else if (movement.point(adjusted) < 0) {\n        return Optional.some(-movement.point(adjusted));\n      } else {\n        return Optional.none();\n      }\n    };\n    const retry = (movement, bridge, caret) => {\n      const moved = movement.move(caret, JUMP_SIZE);\n      const adjusted = adjustTil(bridge, movement, caret, moved, NUM_RETRIES).getOr(moved);\n      return checkScroll(movement, adjusted, bridge).fold(() => {\n        return bridge.situsFromPoint(adjusted.left, movement.point(adjusted));\n      }, delta => {\n        bridge.scrollBy(0, delta);\n        return bridge.situsFromPoint(adjusted.left, movement.point(adjusted) - delta);\n      });\n    };\n    const Retries = {\n      tryUp: curry(retry, upMovement),\n      tryDown: curry(retry, downMovement),\n      getJumpSize: constant(JUMP_SIZE)\n    };\n\n    const MAX_RETRIES = 20;\n    const findSpot = (bridge, isRoot, direction) => {\n      return bridge.getSelection().bind(sel => {\n        return tryBr(isRoot, sel.finish, sel.foffset, direction).fold(() => {\n          return Optional.some(point(sel.finish, sel.foffset));\n        }, brNeighbour => {\n          const range = bridge.fromSitus(brNeighbour);\n          const analysis = BeforeAfter.verify(bridge, sel.finish, sel.foffset, range.finish, range.foffset, direction.failure, isRoot);\n          return process(analysis);\n        });\n      });\n    };\n    const scan = (bridge, isRoot, element, offset, direction, numRetries) => {\n      if (numRetries === 0) {\n        return Optional.none();\n      }\n      return tryCursor(bridge, isRoot, element, offset, direction).bind(situs => {\n        const range = bridge.fromSitus(situs);\n        const analysis = BeforeAfter.verify(bridge, element, offset, range.finish, range.foffset, direction.failure, isRoot);\n        return BeforeAfter.cata(analysis, () => {\n          return Optional.none();\n        }, () => {\n          return Optional.some(situs);\n        }, cell => {\n          if (eq$1(element, cell) && offset === 0) {\n            return tryAgain(bridge, element, offset, moveUp, direction);\n          } else {\n            return scan(bridge, isRoot, cell, 0, direction, numRetries - 1);\n          }\n        }, cell => {\n          if (eq$1(element, cell) && offset === getEnd(cell)) {\n            return tryAgain(bridge, element, offset, moveDown, direction);\n          } else {\n            return scan(bridge, isRoot, cell, getEnd(cell), direction, numRetries - 1);\n          }\n        });\n      });\n    };\n    const tryAgain = (bridge, element, offset, move, direction) => {\n      return getBoxAt(bridge, element, offset).bind(box => {\n        return tryAt(bridge, direction, move(box, Retries.getJumpSize()));\n      });\n    };\n    const tryAt = (bridge, direction, box) => {\n      const browser = detect$2().browser;\n      if (browser.isChromium() || browser.isSafari() || browser.isFirefox()) {\n        return direction.retry(bridge, box);\n      } else {\n        return Optional.none();\n      }\n    };\n    const tryCursor = (bridge, isRoot, element, offset, direction) => {\n      return getBoxAt(bridge, element, offset).bind(box => {\n        return tryAt(bridge, direction, box);\n      });\n    };\n    const handle$1 = (bridge, isRoot, direction) => {\n      return findSpot(bridge, isRoot, direction).bind(spot => {\n        return scan(bridge, isRoot, spot.element, spot.offset, direction, MAX_RETRIES).map(bridge.fromSitus);\n      });\n    };\n\n    const inSameTable = (elem, table) => {\n      return ancestor(elem, e => {\n        return parent(e).exists(p => {\n          return eq$1(p, table);\n        });\n      });\n    };\n    const simulate = (bridge, isRoot, direction, initial, anchor) => {\n      return closest$1(initial, 'td,th', isRoot).bind(start => {\n        return closest$1(start, 'table', isRoot).bind(table => {\n          if (!inSameTable(anchor, table)) {\n            return Optional.none();\n          }\n          return handle$1(bridge, isRoot, direction).bind(range => {\n            return closest$1(range.finish, 'td,th', isRoot).map(finish => {\n              return {\n                start,\n                finish,\n                range\n              };\n            });\n          });\n        });\n      });\n    };\n    const navigate = (bridge, isRoot, direction, initial, anchor, precheck) => {\n      return precheck(initial, isRoot).orThunk(() => {\n        return simulate(bridge, isRoot, direction, initial, anchor).map(info => {\n          const range = info.range;\n          return Response.create(Optional.some(makeSitus(range.start, range.soffset, range.finish, range.foffset)), true);\n        });\n      });\n    };\n    const firstUpCheck = (initial, isRoot) => {\n      return closest$1(initial, 'tr', isRoot).bind(startRow => {\n        return closest$1(startRow, 'table', isRoot).bind(table => {\n          const rows = descendants(table, 'tr');\n          if (eq$1(startRow, rows[0])) {\n            return seekLeft(table, element => {\n              return last$1(element).isSome();\n            }, isRoot).map(last => {\n              const lastOffset = getEnd(last);\n              return Response.create(Optional.some(makeSitus(last, lastOffset, last, lastOffset)), true);\n            });\n          } else {\n            return Optional.none();\n          }\n        });\n      });\n    };\n    const lastDownCheck = (initial, isRoot) => {\n      return closest$1(initial, 'tr', isRoot).bind(startRow => {\n        return closest$1(startRow, 'table', isRoot).bind(table => {\n          const rows = descendants(table, 'tr');\n          if (eq$1(startRow, rows[rows.length - 1])) {\n            return seekRight(table, element => {\n              return first(element).isSome();\n            }, isRoot).map(first => {\n              return Response.create(Optional.some(makeSitus(first, 0, first, 0)), true);\n            });\n          } else {\n            return Optional.none();\n          }\n        });\n      });\n    };\n    const select = (bridge, container, isRoot, direction, initial, anchor, selectRange) => {\n      return simulate(bridge, isRoot, direction, initial, anchor).bind(info => {\n        return detect(container, isRoot, info.start, info.finish, selectRange);\n      });\n    };\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    const singleton = doRevoke => {\n      const subject = Cell(Optional.none());\n      const revoke = () => subject.get().each(doRevoke);\n      const clear = () => {\n        revoke();\n        subject.set(Optional.none());\n      };\n      const isSet = () => subject.get().isSome();\n      const get = () => subject.get();\n      const set = s => {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear,\n        isSet,\n        get,\n        set\n      };\n    };\n    const value = () => {\n      const subject = singleton(noop);\n      const on = f => subject.get().each(f);\n      return {\n        ...subject,\n        on\n      };\n    };\n\n    const findCell = (target, isRoot) => closest$1(target, 'td,th', isRoot);\n    const isInEditableContext = cell => parentElement(cell).exists(isEditable$1);\n    const MouseSelection = (bridge, container, isRoot, annotations) => {\n      const cursor = value();\n      const clearstate = cursor.clear;\n      const applySelection = event => {\n        cursor.on(start => {\n          annotations.clearBeforeUpdate(container);\n          findCell(event.target, isRoot).each(finish => {\n            identify(start, finish, isRoot).each(cellSel => {\n              const boxes = cellSel.boxes.getOr([]);\n              if (boxes.length === 1) {\n                const singleCell = boxes[0];\n                const isNonEditableCell = getRaw(singleCell) === 'false';\n                const isCellClosestContentEditable = is(closest(event.target), singleCell, eq$1);\n                if (isNonEditableCell && isCellClosestContentEditable) {\n                  annotations.selectRange(container, boxes, singleCell, singleCell);\n                  bridge.selectContents(singleCell);\n                }\n              } else if (boxes.length > 1) {\n                annotations.selectRange(container, boxes, cellSel.start, cellSel.finish);\n                bridge.selectContents(finish);\n              }\n            });\n          });\n        });\n      };\n      const mousedown = event => {\n        annotations.clear(container);\n        findCell(event.target, isRoot).filter(isInEditableContext).each(cursor.set);\n      };\n      const mouseover = event => {\n        applySelection(event);\n      };\n      const mouseup = event => {\n        applySelection(event);\n        clearstate();\n      };\n      return {\n        clearstate,\n        mousedown,\n        mouseover,\n        mouseup\n      };\n    };\n\n    const down = {\n      traverse: nextSibling,\n      gather: after$1,\n      relative: Situ.before,\n      retry: Retries.tryDown,\n      failure: BeforeAfter.failedDown\n    };\n    const up = {\n      traverse: prevSibling,\n      gather: before,\n      relative: Situ.before,\n      retry: Retries.tryUp,\n      failure: BeforeAfter.failedUp\n    };\n\n    const isKey = key => {\n      return keycode => {\n        return keycode === key;\n      };\n    };\n    const isUp = isKey(38);\n    const isDown = isKey(40);\n    const isNavigation = keycode => {\n      return keycode >= 37 && keycode <= 40;\n    };\n    const ltr = {\n      isBackward: isKey(37),\n      isForward: isKey(39)\n    };\n    const rtl = {\n      isBackward: isKey(39),\n      isForward: isKey(37)\n    };\n\n    const get$3 = _DOC => {\n      const doc = _DOC !== undefined ? _DOC.dom : document;\n      const x = doc.body.scrollLeft || doc.documentElement.scrollLeft;\n      const y = doc.body.scrollTop || doc.documentElement.scrollTop;\n      return SugarPosition(x, y);\n    };\n    const by = (x, y, _DOC) => {\n      const doc = _DOC !== undefined ? _DOC.dom : document;\n      const win = doc.defaultView;\n      if (win) {\n        win.scrollBy(x, y);\n      }\n    };\n\n    const adt = Adt.generate([\n      { domRange: ['rng'] },\n      {\n        relative: [\n          'startSitu',\n          'finishSitu'\n        ]\n      },\n      {\n        exact: [\n          'start',\n          'soffset',\n          'finish',\n          'foffset'\n        ]\n      }\n    ]);\n    const exactFromRange = simRange => adt.exact(simRange.start, simRange.soffset, simRange.finish, simRange.foffset);\n    const getStart = selection => selection.match({\n      domRange: rng => SugarElement.fromDom(rng.startContainer),\n      relative: (startSitu, _finishSitu) => Situ.getStart(startSitu),\n      exact: (start, _soffset, _finish, _foffset) => start\n    });\n    const domRange = adt.domRange;\n    const relative = adt.relative;\n    const exact = adt.exact;\n    const getWin = selection => {\n      const start = getStart(selection);\n      return defaultView(start);\n    };\n    const range = SimRange.create;\n    const SimSelection = {\n      domRange,\n      relative,\n      exact,\n      exactFromRange,\n      getWin,\n      range\n    };\n\n    const caretPositionFromPoint = (doc, x, y) => {\n      var _a, _b;\n      return Optional.from((_b = (_a = doc.dom).caretPositionFromPoint) === null || _b === void 0 ? void 0 : _b.call(_a, x, y)).bind(pos => {\n        if (pos.offsetNode === null) {\n          return Optional.none();\n        }\n        const r = doc.dom.createRange();\n        r.setStart(pos.offsetNode, pos.offset);\n        r.collapse();\n        return Optional.some(r);\n      });\n    };\n    const caretRangeFromPoint = (doc, x, y) => {\n      var _a, _b;\n      return Optional.from((_b = (_a = doc.dom).caretRangeFromPoint) === null || _b === void 0 ? void 0 : _b.call(_a, x, y));\n    };\n    const availableSearch = (() => {\n      if (document.caretPositionFromPoint) {\n        return caretPositionFromPoint;\n      } else if (document.caretRangeFromPoint) {\n        return caretRangeFromPoint;\n      } else {\n        return Optional.none;\n      }\n    })();\n    const fromPoint = (win, x, y) => {\n      const doc = SugarElement.fromDom(win.document);\n      return availableSearch(doc, x, y).map(rng => SimRange.create(SugarElement.fromDom(rng.startContainer), rng.startOffset, SugarElement.fromDom(rng.endContainer), rng.endOffset));\n    };\n\n    const beforeSpecial = (element, offset) => {\n      const name$1 = name(element);\n      if ('input' === name$1) {\n        return Situ.after(element);\n      } else if (!contains$2([\n          'br',\n          'img'\n        ], name$1)) {\n        return Situ.on(element, offset);\n      } else {\n        return offset === 0 ? Situ.before(element) : Situ.after(element);\n      }\n    };\n    const preprocessRelative = (startSitu, finishSitu) => {\n      const start = startSitu.fold(Situ.before, beforeSpecial, Situ.after);\n      const finish = finishSitu.fold(Situ.before, beforeSpecial, Situ.after);\n      return SimSelection.relative(start, finish);\n    };\n    const preprocessExact = (start, soffset, finish, foffset) => {\n      const startSitu = beforeSpecial(start, soffset);\n      const finishSitu = beforeSpecial(finish, foffset);\n      return SimSelection.relative(startSitu, finishSitu);\n    };\n\n    const makeRange = (start, soffset, finish, foffset) => {\n      const doc = owner(start);\n      const rng = doc.dom.createRange();\n      rng.setStart(start.dom, soffset);\n      rng.setEnd(finish.dom, foffset);\n      return rng;\n    };\n    const after = (start, soffset, finish, foffset) => {\n      const r = makeRange(start, soffset, finish, foffset);\n      const same = eq$1(start, finish) && soffset === foffset;\n      return r.collapsed && !same;\n    };\n\n    const getNativeSelection = win => Optional.from(win.getSelection());\n    const doSetNativeRange = (win, rng) => {\n      getNativeSelection(win).each(selection => {\n        selection.removeAllRanges();\n        selection.addRange(rng);\n      });\n    };\n    const doSetRange = (win, start, soffset, finish, foffset) => {\n      const rng = exactToNative(win, start, soffset, finish, foffset);\n      doSetNativeRange(win, rng);\n    };\n    const setLegacyRtlRange = (win, selection, start, soffset, finish, foffset) => {\n      selection.collapse(start.dom, soffset);\n      selection.extend(finish.dom, foffset);\n    };\n    const setRangeFromRelative = (win, relative) => diagnose(win, relative).match({\n      ltr: (start, soffset, finish, foffset) => {\n        doSetRange(win, start, soffset, finish, foffset);\n      },\n      rtl: (start, soffset, finish, foffset) => {\n        getNativeSelection(win).each(selection => {\n          if (selection.setBaseAndExtent) {\n            selection.setBaseAndExtent(start.dom, soffset, finish.dom, foffset);\n          } else if (selection.extend) {\n            try {\n              setLegacyRtlRange(win, selection, start, soffset, finish, foffset);\n            } catch (e) {\n              doSetRange(win, finish, foffset, start, soffset);\n            }\n          } else {\n            doSetRange(win, finish, foffset, start, soffset);\n          }\n        });\n      }\n    });\n    const setExact = (win, start, soffset, finish, foffset) => {\n      const relative = preprocessExact(start, soffset, finish, foffset);\n      setRangeFromRelative(win, relative);\n    };\n    const setRelative = (win, startSitu, finishSitu) => {\n      const relative = preprocessRelative(startSitu, finishSitu);\n      setRangeFromRelative(win, relative);\n    };\n    const readRange = selection => {\n      if (selection.rangeCount > 0) {\n        const firstRng = selection.getRangeAt(0);\n        const lastRng = selection.getRangeAt(selection.rangeCount - 1);\n        return Optional.some(SimRange.create(SugarElement.fromDom(firstRng.startContainer), firstRng.startOffset, SugarElement.fromDom(lastRng.endContainer), lastRng.endOffset));\n      } else {\n        return Optional.none();\n      }\n    };\n    const doGetExact = selection => {\n      if (selection.anchorNode === null || selection.focusNode === null) {\n        return readRange(selection);\n      } else {\n        const anchor = SugarElement.fromDom(selection.anchorNode);\n        const focus = SugarElement.fromDom(selection.focusNode);\n        return after(anchor, selection.anchorOffset, focus, selection.focusOffset) ? Optional.some(SimRange.create(anchor, selection.anchorOffset, focus, selection.focusOffset)) : readRange(selection);\n      }\n    };\n    const setToElement = (win, element, selectNodeContents$1 = true) => {\n      const rngGetter = selectNodeContents$1 ? selectNodeContents : selectNode;\n      const rng = rngGetter(win, element);\n      doSetNativeRange(win, rng);\n    };\n    const getExact = win => getNativeSelection(win).filter(sel => sel.rangeCount > 0).bind(doGetExact);\n    const get$2 = win => getExact(win).map(range => SimSelection.exact(range.start, range.soffset, range.finish, range.foffset));\n    const getFirstRect = (win, selection) => {\n      const rng = asLtrRange(win, selection);\n      return getFirstRect$1(rng);\n    };\n    const getAtPoint = (win, x, y) => fromPoint(win, x, y);\n    const clear = win => {\n      getNativeSelection(win).each(selection => selection.removeAllRanges());\n    };\n\n    const WindowBridge = win => {\n      const elementFromPoint = (x, y) => {\n        return SugarElement.fromPoint(SugarElement.fromDom(win.document), x, y);\n      };\n      const getRect = element => {\n        return element.dom.getBoundingClientRect();\n      };\n      const getRangedRect = (start, soffset, finish, foffset) => {\n        const sel = SimSelection.exact(start, soffset, finish, foffset);\n        return getFirstRect(win, sel);\n      };\n      const getSelection = () => {\n        return get$2(win).map(exactAdt => {\n          return convertToRange(win, exactAdt);\n        });\n      };\n      const fromSitus = situs => {\n        const relative = SimSelection.relative(situs.start, situs.finish);\n        return convertToRange(win, relative);\n      };\n      const situsFromPoint = (x, y) => {\n        return getAtPoint(win, x, y).map(exact => {\n          return Situs.create(exact.start, exact.soffset, exact.finish, exact.foffset);\n        });\n      };\n      const clearSelection = () => {\n        clear(win);\n      };\n      const collapseSelection = (toStart = false) => {\n        get$2(win).each(sel => sel.fold(rng => rng.collapse(toStart), (startSitu, finishSitu) => {\n          const situ = toStart ? startSitu : finishSitu;\n          setRelative(win, situ, situ);\n        }, (start, soffset, finish, foffset) => {\n          const node = toStart ? start : finish;\n          const offset = toStart ? soffset : foffset;\n          setExact(win, node, offset, node, offset);\n        }));\n      };\n      const selectNode = element => {\n        setToElement(win, element, false);\n      };\n      const selectContents = element => {\n        setToElement(win, element);\n      };\n      const setSelection = sel => {\n        setExact(win, sel.start, sel.soffset, sel.finish, sel.foffset);\n      };\n      const setRelativeSelection = (start, finish) => {\n        setRelative(win, start, finish);\n      };\n      const getInnerHeight = () => {\n        return win.innerHeight;\n      };\n      const getScrollY = () => {\n        const pos = get$3(SugarElement.fromDom(win.document));\n        return pos.top;\n      };\n      const scrollBy = (x, y) => {\n        by(x, y, SugarElement.fromDom(win.document));\n      };\n      return {\n        elementFromPoint,\n        getRect,\n        getRangedRect,\n        getSelection,\n        fromSitus,\n        situsFromPoint,\n        clearSelection,\n        collapseSelection,\n        setSelection,\n        setRelativeSelection,\n        selectNode,\n        selectContents,\n        getInnerHeight,\n        getScrollY,\n        scrollBy\n      };\n    };\n\n    const rc = (rows, cols) => ({\n      rows,\n      cols\n    });\n    const mouse = (win, container, isRoot, annotations) => {\n      const bridge = WindowBridge(win);\n      const handlers = MouseSelection(bridge, container, isRoot, annotations);\n      return {\n        clearstate: handlers.clearstate,\n        mousedown: handlers.mousedown,\n        mouseover: handlers.mouseover,\n        mouseup: handlers.mouseup\n      };\n    };\n    const isEditableNode = node => closest$2(node, isHTMLElement).exists(isEditable$1);\n    const isEditableSelection = (start, finish) => isEditableNode(start) || isEditableNode(finish);\n    const keyboard = (win, container, isRoot, annotations) => {\n      const bridge = WindowBridge(win);\n      const clearToNavigate = () => {\n        annotations.clear(container);\n        return Optional.none();\n      };\n      const keydown = (event, start, soffset, finish, foffset, direction) => {\n        const realEvent = event.raw;\n        const keycode = realEvent.which;\n        const shiftKey = realEvent.shiftKey === true;\n        const handler = retrieve$1(container, annotations.selectedSelector).fold(() => {\n          if (isNavigation(keycode) && !shiftKey) {\n            annotations.clearBeforeUpdate(container);\n          }\n          if (isNavigation(keycode) && shiftKey && !isEditableSelection(start, finish)) {\n            return Optional.none;\n          } else if (isDown(keycode) && shiftKey) {\n            return curry(select, bridge, container, isRoot, down, finish, start, annotations.selectRange);\n          } else if (isUp(keycode) && shiftKey) {\n            return curry(select, bridge, container, isRoot, up, finish, start, annotations.selectRange);\n          } else if (isDown(keycode)) {\n            return curry(navigate, bridge, isRoot, down, finish, start, lastDownCheck);\n          } else if (isUp(keycode)) {\n            return curry(navigate, bridge, isRoot, up, finish, start, firstUpCheck);\n          } else {\n            return Optional.none;\n          }\n        }, selected => {\n          const update$1 = attempts => {\n            return () => {\n              const navigation = findMap(attempts, delta => {\n                return update(delta.rows, delta.cols, container, selected, annotations);\n              });\n              return navigation.fold(() => {\n                return getEdges(container, annotations.firstSelectedSelector, annotations.lastSelectedSelector).map(edges => {\n                  const relative = isDown(keycode) || direction.isForward(keycode) ? Situ.after : Situ.before;\n                  bridge.setRelativeSelection(Situ.on(edges.first, 0), relative(edges.table));\n                  annotations.clear(container);\n                  return Response.create(Optional.none(), true);\n                });\n              }, _ => {\n                return Optional.some(Response.create(Optional.none(), true));\n              });\n            };\n          };\n          if (isNavigation(keycode) && shiftKey && !isEditableSelection(start, finish)) {\n            return Optional.none;\n          } else if (isDown(keycode) && shiftKey) {\n            return update$1([rc(+1, 0)]);\n          } else if (isUp(keycode) && shiftKey) {\n            return update$1([rc(-1, 0)]);\n          } else if (direction.isBackward(keycode) && shiftKey) {\n            return update$1([\n              rc(0, -1),\n              rc(-1, 0)\n            ]);\n          } else if (direction.isForward(keycode) && shiftKey) {\n            return update$1([\n              rc(0, +1),\n              rc(+1, 0)\n            ]);\n          } else if (isNavigation(keycode) && !shiftKey) {\n            return clearToNavigate;\n          } else {\n            return Optional.none;\n          }\n        });\n        return handler();\n      };\n      const keyup = (event, start, soffset, finish, foffset) => {\n        return retrieve$1(container, annotations.selectedSelector).fold(() => {\n          const realEvent = event.raw;\n          const keycode = realEvent.which;\n          const shiftKey = realEvent.shiftKey === true;\n          if (!shiftKey) {\n            return Optional.none();\n          }\n          if (isNavigation(keycode) && isEditableSelection(start, finish)) {\n            return sync(container, isRoot, start, soffset, finish, foffset, annotations.selectRange);\n          } else {\n            return Optional.none();\n          }\n        }, Optional.none);\n      };\n      return {\n        keydown,\n        keyup\n      };\n    };\n    const external = (win, container, isRoot, annotations) => {\n      const bridge = WindowBridge(win);\n      return (start, finish) => {\n        annotations.clearBeforeUpdate(container);\n        identify(start, finish, isRoot).each(cellSel => {\n          const boxes = cellSel.boxes.getOr([]);\n          annotations.selectRange(container, boxes, cellSel.start, cellSel.finish);\n          bridge.selectContents(finish);\n          bridge.collapseSelection();\n        });\n      };\n    };\n\n    const read = (element, attr) => {\n      const value = get$b(element, attr);\n      return value === undefined || value === '' ? [] : value.split(' ');\n    };\n    const add$2 = (element, attr, id) => {\n      const old = read(element, attr);\n      const nu = old.concat([id]);\n      set$2(element, attr, nu.join(' '));\n      return true;\n    };\n    const remove$4 = (element, attr, id) => {\n      const nu = filter$2(read(element, attr), v => v !== id);\n      if (nu.length > 0) {\n        set$2(element, attr, nu.join(' '));\n      } else {\n        remove$7(element, attr);\n      }\n      return false;\n    };\n\n    const supports = element => element.dom.classList !== undefined;\n    const get$1 = element => read(element, 'class');\n    const add$1 = (element, clazz) => add$2(element, 'class', clazz);\n    const remove$3 = (element, clazz) => remove$4(element, 'class', clazz);\n\n    const add = (element, clazz) => {\n      if (supports(element)) {\n        element.dom.classList.add(clazz);\n      } else {\n        add$1(element, clazz);\n      }\n    };\n    const cleanClass = element => {\n      const classList = supports(element) ? element.dom.classList : get$1(element);\n      if (classList.length === 0) {\n        remove$7(element, 'class');\n      }\n    };\n    const remove$2 = (element, clazz) => {\n      if (supports(element)) {\n        const classList = element.dom.classList;\n        classList.remove(clazz);\n      } else {\n        remove$3(element, clazz);\n      }\n      cleanClass(element);\n    };\n    const has = (element, clazz) => supports(element) && element.dom.classList.contains(clazz);\n\n    const remove$1 = (element, classes) => {\n      each$2(classes, x => {\n        remove$2(element, x);\n      });\n    };\n\n    const addClass = clazz => element => {\n      add(element, clazz);\n    };\n    const removeClasses = classes => element => {\n      remove$1(element, classes);\n    };\n\n    const byClass = ephemera => {\n      const addSelectionClass = addClass(ephemera.selected);\n      const removeSelectionClasses = removeClasses([\n        ephemera.selected,\n        ephemera.lastSelected,\n        ephemera.firstSelected\n      ]);\n      const clear = container => {\n        const sels = descendants(container, ephemera.selectedSelector);\n        each$2(sels, removeSelectionClasses);\n      };\n      const selectRange = (container, cells, start, finish) => {\n        clear(container);\n        each$2(cells, addSelectionClass);\n        add(start, ephemera.firstSelected);\n        add(finish, ephemera.lastSelected);\n      };\n      return {\n        clearBeforeUpdate: clear,\n        clear,\n        selectRange,\n        selectedSelector: ephemera.selectedSelector,\n        firstSelectedSelector: ephemera.firstSelectedSelector,\n        lastSelectedSelector: ephemera.lastSelectedSelector\n      };\n    };\n    const byAttr = (ephemera, onSelection, onClear) => {\n      const removeSelectionAttributes = element => {\n        remove$7(element, ephemera.selected);\n        remove$7(element, ephemera.firstSelected);\n        remove$7(element, ephemera.lastSelected);\n      };\n      const addSelectionAttribute = element => {\n        set$2(element, ephemera.selected, '1');\n      };\n      const clear = container => {\n        clearBeforeUpdate(container);\n        onClear();\n      };\n      const clearBeforeUpdate = container => {\n        const sels = descendants(container, `${ ephemera.selectedSelector },${ ephemera.firstSelectedSelector },${ ephemera.lastSelectedSelector }`);\n        each$2(sels, removeSelectionAttributes);\n      };\n      const selectRange = (container, cells, start, finish) => {\n        clear(container);\n        each$2(cells, addSelectionAttribute);\n        set$2(start, ephemera.firstSelected, '1');\n        set$2(finish, ephemera.lastSelected, '1');\n        onSelection(cells, start, finish);\n      };\n      return {\n        clearBeforeUpdate,\n        clear,\n        selectRange,\n        selectedSelector: ephemera.selectedSelector,\n        firstSelectedSelector: ephemera.firstSelectedSelector,\n        lastSelectedSelector: ephemera.lastSelectedSelector\n      };\n    };\n    const SelectionAnnotation = {\n      byClass,\n      byAttr\n    };\n\n    const fold = (subject, onNone, onMultiple, onSingle) => {\n      switch (subject.tag) {\n      case 'none':\n        return onNone();\n      case 'single':\n        return onSingle(subject.element);\n      case 'multiple':\n        return onMultiple(subject.elements);\n      }\n    };\n    const none = () => ({ tag: 'none' });\n    const multiple = elements => ({\n      tag: 'multiple',\n      elements\n    });\n    const single = element => ({\n      tag: 'single',\n      element\n    });\n\n    const Selections = (lazyRoot, getStart, selectedSelector) => {\n      const get = () => retrieve(lazyRoot(), selectedSelector).fold(() => getStart().fold(none, single), multiple);\n      return { get };\n    };\n\n    const getUpOrLeftCells = (grid, selectedCells) => {\n      const upGrid = grid.slice(0, selectedCells[selectedCells.length - 1].row + 1);\n      const upDetails = toDetailList(upGrid);\n      return bind$2(upDetails, detail => {\n        const slicedCells = detail.cells.slice(0, selectedCells[selectedCells.length - 1].column + 1);\n        return map$1(slicedCells, cell => cell.element);\n      });\n    };\n    const getDownOrRightCells = (grid, selectedCells) => {\n      const downGrid = grid.slice(selectedCells[0].row + selectedCells[0].rowspan - 1, grid.length);\n      const downDetails = toDetailList(downGrid);\n      return bind$2(downDetails, detail => {\n        const slicedCells = detail.cells.slice(selectedCells[0].column + selectedCells[0].colspan - 1, detail.cells.length);\n        return map$1(slicedCells, cell => cell.element);\n      });\n    };\n    const getOtherCells = (table, target, generators) => {\n      const warehouse = Warehouse.fromTable(table);\n      const details = onCells(warehouse, target);\n      return details.map(selectedCells => {\n        const grid = toGrid(warehouse, generators, false);\n        const {rows} = extractGridDetails(grid);\n        const upOrLeftCells = getUpOrLeftCells(rows, selectedCells);\n        const downOrRightCells = getDownOrRightCells(rows, selectedCells);\n        return {\n          upOrLeftCells,\n          downOrRightCells\n        };\n      });\n    };\n\n    const mkEvent = (target, x, y, stop, prevent, kill, raw) => ({\n      target,\n      x,\n      y,\n      stop,\n      prevent,\n      kill,\n      raw\n    });\n    const fromRawEvent$1 = rawEvent => {\n      const target = SugarElement.fromDom(getOriginalEventTarget(rawEvent).getOr(rawEvent.target));\n      const stop = () => rawEvent.stopPropagation();\n      const prevent = () => rawEvent.preventDefault();\n      const kill = compose(prevent, stop);\n      return mkEvent(target, rawEvent.clientX, rawEvent.clientY, stop, prevent, kill, rawEvent);\n    };\n    const handle = (filter, handler) => rawEvent => {\n      if (filter(rawEvent)) {\n        handler(fromRawEvent$1(rawEvent));\n      }\n    };\n    const binder = (element, event, filter, handler, useCapture) => {\n      const wrapped = handle(filter, handler);\n      element.dom.addEventListener(event, wrapped, useCapture);\n      return { unbind: curry(unbind, element, event, wrapped, useCapture) };\n    };\n    const bind$1 = (element, event, filter, handler) => binder(element, event, filter, handler, false);\n    const unbind = (element, event, handler, useCapture) => {\n      element.dom.removeEventListener(event, handler, useCapture);\n    };\n\n    const filter = always;\n    const bind = (element, event, handler) => bind$1(element, event, filter, handler);\n    const fromRawEvent = fromRawEvent$1;\n\n    const hasInternalTarget = e => !has(SugarElement.fromDom(e.target), 'ephox-snooker-resizer-bar');\n    const TableCellSelectionHandler = (editor, resizeHandler) => {\n      const cellSelection = Selections(() => SugarElement.fromDom(editor.getBody()), () => getSelectionCell(getSelectionStart(editor), getIsRoot(editor)), ephemera.selectedSelector);\n      const onSelection = (cells, start, finish) => {\n        const tableOpt = table(start);\n        tableOpt.each(table => {\n          const cloneFormats = getTableCloneElements(editor);\n          const generators = cellOperations(noop, SugarElement.fromDom(editor.getDoc()), cloneFormats);\n          const selectedCells = getCellsFromSelection(editor);\n          const otherCells = getOtherCells(table, { selection: selectedCells }, generators);\n          fireTableSelectionChange(editor, cells, start, finish, otherCells);\n        });\n      };\n      const onClear = () => fireTableSelectionClear(editor);\n      const annotations = SelectionAnnotation.byAttr(ephemera, onSelection, onClear);\n      editor.on('init', _e => {\n        const win = editor.getWin();\n        const body = getBody(editor);\n        const isRoot = getIsRoot(editor);\n        const syncSelection = () => {\n          const sel = editor.selection;\n          const start = SugarElement.fromDom(sel.getStart());\n          const end = SugarElement.fromDom(sel.getEnd());\n          const shared = sharedOne(table, [\n            start,\n            end\n          ]);\n          shared.fold(() => annotations.clear(body), noop);\n        };\n        const mouseHandlers = mouse(win, body, isRoot, annotations);\n        const keyHandlers = keyboard(win, body, isRoot, annotations);\n        const external$1 = external(win, body, isRoot, annotations);\n        const hasShiftKey = event => event.raw.shiftKey === true;\n        editor.on('TableSelectorChange', e => external$1(e.start, e.finish));\n        const handleResponse = (event, response) => {\n          if (!hasShiftKey(event)) {\n            return;\n          }\n          if (response.kill) {\n            event.kill();\n          }\n          response.selection.each(ns => {\n            const relative = SimSelection.relative(ns.start, ns.finish);\n            const rng = asLtrRange(win, relative);\n            editor.selection.setRng(rng);\n          });\n        };\n        const keyup = event => {\n          const wrappedEvent = fromRawEvent(event);\n          if (wrappedEvent.raw.shiftKey && isNavigation(wrappedEvent.raw.which)) {\n            const rng = editor.selection.getRng();\n            const start = SugarElement.fromDom(rng.startContainer);\n            const end = SugarElement.fromDom(rng.endContainer);\n            keyHandlers.keyup(wrappedEvent, start, rng.startOffset, end, rng.endOffset).each(response => {\n              handleResponse(wrappedEvent, response);\n            });\n          }\n        };\n        const keydown = event => {\n          const wrappedEvent = fromRawEvent(event);\n          resizeHandler.hide();\n          const rng = editor.selection.getRng();\n          const start = SugarElement.fromDom(rng.startContainer);\n          const end = SugarElement.fromDom(rng.endContainer);\n          const direction = onDirection(ltr, rtl)(SugarElement.fromDom(editor.selection.getStart()));\n          keyHandlers.keydown(wrappedEvent, start, rng.startOffset, end, rng.endOffset, direction).each(response => {\n            handleResponse(wrappedEvent, response);\n          });\n          resizeHandler.show();\n        };\n        const isLeftMouse = raw => raw.button === 0;\n        const isLeftButtonPressed = raw => {\n          if (raw.buttons === undefined) {\n            return true;\n          }\n          return (raw.buttons & 1) !== 0;\n        };\n        const dragStart = _e => {\n          mouseHandlers.clearstate();\n        };\n        const mouseDown = e => {\n          if (isLeftMouse(e) && hasInternalTarget(e)) {\n            mouseHandlers.mousedown(fromRawEvent(e));\n          }\n        };\n        const mouseOver = e => {\n          if (isLeftButtonPressed(e) && hasInternalTarget(e)) {\n            mouseHandlers.mouseover(fromRawEvent(e));\n          }\n        };\n        const mouseUp = e => {\n          if (isLeftMouse(e) && hasInternalTarget(e)) {\n            mouseHandlers.mouseup(fromRawEvent(e));\n          }\n        };\n        const getDoubleTap = () => {\n          const lastTarget = Cell(SugarElement.fromDom(body));\n          const lastTimeStamp = Cell(0);\n          const touchEnd = t => {\n            const target = SugarElement.fromDom(t.target);\n            if (isTag('td')(target) || isTag('th')(target)) {\n              const lT = lastTarget.get();\n              const lTS = lastTimeStamp.get();\n              if (eq$1(lT, target) && t.timeStamp - lTS < 300) {\n                t.preventDefault();\n                external$1(target, target);\n              }\n            }\n            lastTarget.set(target);\n            lastTimeStamp.set(t.timeStamp);\n          };\n          return { touchEnd };\n        };\n        const doubleTap = getDoubleTap();\n        editor.on('dragstart', dragStart);\n        editor.on('mousedown', mouseDown);\n        editor.on('mouseover', mouseOver);\n        editor.on('mouseup', mouseUp);\n        editor.on('touchend', doubleTap.touchEnd);\n        editor.on('keyup', keyup);\n        editor.on('keydown', keydown);\n        editor.on('NodeChange', syncSelection);\n      });\n      editor.on('PreInit', () => {\n        editor.serializer.addTempAttr(ephemera.firstSelected);\n        editor.serializer.addTempAttr(ephemera.lastSelected);\n      });\n      const clearSelectedCells = container => annotations.clear(SugarElement.fromDom(container));\n      const getSelectedCells = () => fold(cellSelection.get(), constant([]), cells => {\n        return map$1(cells, cell => cell.dom);\n      }, cell => [cell.dom]);\n      return {\n        getSelectedCells,\n        clearSelectedCells\n      };\n    };\n\n    const Event = fields => {\n      let handlers = [];\n      const bind = handler => {\n        if (handler === undefined) {\n          throw new Error('Event bind error: undefined handler');\n        }\n        handlers.push(handler);\n      };\n      const unbind = handler => {\n        handlers = filter$2(handlers, h => {\n          return h !== handler;\n        });\n      };\n      const trigger = (...args) => {\n        const event = {};\n        each$2(fields, (name, i) => {\n          event[name] = args[i];\n        });\n        each$2(handlers, handler => {\n          handler(event);\n        });\n      };\n      return {\n        bind,\n        unbind,\n        trigger\n      };\n    };\n\n    const create$1 = typeDefs => {\n      const registry = map(typeDefs, event => {\n        return {\n          bind: event.bind,\n          unbind: event.unbind\n        };\n      });\n      const trigger = map(typeDefs, event => {\n        return event.trigger;\n      });\n      return {\n        registry,\n        trigger\n      };\n    };\n\n    const last = (fn, rate) => {\n      let timer = null;\n      const cancel = () => {\n        if (!isNull(timer)) {\n          clearTimeout(timer);\n          timer = null;\n        }\n      };\n      const throttle = (...args) => {\n        cancel();\n        timer = setTimeout(() => {\n          timer = null;\n          fn.apply(null, args);\n        }, rate);\n      };\n      return {\n        cancel,\n        throttle\n      };\n    };\n\n    const sort = arr => {\n      return arr.slice(0).sort();\n    };\n    const reqMessage = (required, keys) => {\n      throw new Error('All required keys (' + sort(required).join(', ') + ') were not specified. Specified keys were: ' + sort(keys).join(', ') + '.');\n    };\n    const unsuppMessage = unsupported => {\n      throw new Error('Unsupported keys for object: ' + sort(unsupported).join(', '));\n    };\n    const validateStrArr = (label, array) => {\n      if (!isArray(array)) {\n        throw new Error('The ' + label + ' fields must be an array. Was: ' + array + '.');\n      }\n      each$2(array, a => {\n        if (!isString(a)) {\n          throw new Error('The value ' + a + ' in the ' + label + ' fields was not a string.');\n        }\n      });\n    };\n    const invalidTypeMessage = (incorrect, type) => {\n      throw new Error('All values need to be of type: ' + type + '. Keys (' + sort(incorrect).join(', ') + ') were not.');\n    };\n    const checkDupes = everything => {\n      const sorted = sort(everything);\n      const dupe = find$1(sorted, (s, i) => {\n        return i < sorted.length - 1 && s === sorted[i + 1];\n      });\n      dupe.each(d => {\n        throw new Error('The field: ' + d + ' occurs more than once in the combined fields: [' + sorted.join(', ') + '].');\n      });\n    };\n\n    const base = (handleUnsupported, required) => {\n      return baseWith(handleUnsupported, required, {\n        validate: isFunction,\n        label: 'function'\n      });\n    };\n    const baseWith = (handleUnsupported, required, pred) => {\n      if (required.length === 0) {\n        throw new Error('You must specify at least one required field.');\n      }\n      validateStrArr('required', required);\n      checkDupes(required);\n      return obj => {\n        const keys$1 = keys(obj);\n        const allReqd = forall(required, req => {\n          return contains$2(keys$1, req);\n        });\n        if (!allReqd) {\n          reqMessage(required, keys$1);\n        }\n        handleUnsupported(required, keys$1);\n        const invalidKeys = filter$2(required, key => {\n          return !pred.validate(obj[key], key);\n        });\n        if (invalidKeys.length > 0) {\n          invalidTypeMessage(invalidKeys, pred.label);\n        }\n        return obj;\n      };\n    };\n    const handleExact = (required, keys) => {\n      const unsupported = filter$2(keys, key => {\n        return !contains$2(required, key);\n      });\n      if (unsupported.length > 0) {\n        unsuppMessage(unsupported);\n      }\n    };\n    const exactly = required => base(handleExact, required);\n\n    const DragMode = exactly([\n      'compare',\n      'extract',\n      'mutate',\n      'sink'\n    ]);\n    const DragSink = exactly([\n      'element',\n      'start',\n      'stop',\n      'destroy'\n    ]);\n    const DragApi = exactly([\n      'forceDrop',\n      'drop',\n      'move',\n      'delayDrop'\n    ]);\n\n    const InDrag = () => {\n      let previous = Optional.none();\n      const reset = () => {\n        previous = Optional.none();\n      };\n      const update = (mode, nu) => {\n        const result = previous.map(old => {\n          return mode.compare(old, nu);\n        });\n        previous = Optional.some(nu);\n        return result;\n      };\n      const onEvent = (event, mode) => {\n        const dataOption = mode.extract(event);\n        dataOption.each(data => {\n          const offset = update(mode, data);\n          offset.each(d => {\n            events.trigger.move(d);\n          });\n        });\n      };\n      const events = create$1({ move: Event(['info']) });\n      return {\n        onEvent,\n        reset,\n        events: events.registry\n      };\n    };\n\n    const NoDrag = () => {\n      const events = create$1({ move: Event(['info']) });\n      return {\n        onEvent: noop,\n        reset: noop,\n        events: events.registry\n      };\n    };\n\n    const Movement = () => {\n      const noDragState = NoDrag();\n      const inDragState = InDrag();\n      let dragState = noDragState;\n      const on = () => {\n        dragState.reset();\n        dragState = inDragState;\n      };\n      const off = () => {\n        dragState.reset();\n        dragState = noDragState;\n      };\n      const onEvent = (event, mode) => {\n        dragState.onEvent(event, mode);\n      };\n      const isOn = () => {\n        return dragState === inDragState;\n      };\n      return {\n        on,\n        off,\n        isOn,\n        onEvent,\n        events: inDragState.events\n      };\n    };\n\n    const setup = (mutation, mode, settings) => {\n      let active = false;\n      const events = create$1({\n        start: Event([]),\n        stop: Event([])\n      });\n      const movement = Movement();\n      const drop = () => {\n        sink.stop();\n        if (movement.isOn()) {\n          movement.off();\n          events.trigger.stop();\n        }\n      };\n      const throttledDrop = last(drop, 200);\n      const go = parent => {\n        sink.start(parent);\n        movement.on();\n        events.trigger.start();\n      };\n      const mousemove = event => {\n        throttledDrop.cancel();\n        movement.onEvent(event, mode);\n      };\n      movement.events.move.bind(event => {\n        mode.mutate(mutation, event.info);\n      });\n      const on = () => {\n        active = true;\n      };\n      const off = () => {\n        active = false;\n      };\n      const isActive = () => active;\n      const runIfActive = f => {\n        return (...args) => {\n          if (active) {\n            f.apply(null, args);\n          }\n        };\n      };\n      const sink = mode.sink(DragApi({\n        forceDrop: drop,\n        drop: runIfActive(drop),\n        move: runIfActive(mousemove),\n        delayDrop: runIfActive(throttledDrop.throttle)\n      }), settings);\n      const destroy = () => {\n        sink.destroy();\n      };\n      return {\n        element: sink.element,\n        go,\n        on,\n        off,\n        isActive,\n        destroy,\n        events: events.registry\n      };\n    };\n\n    const css = namespace => {\n      const dashNamespace = namespace.replace(/\\./g, '-');\n      const resolve = str => {\n        return dashNamespace + '-' + str;\n      };\n      return { resolve };\n    };\n\n    const styles$1 = css('ephox-dragster');\n    const resolve$1 = styles$1.resolve;\n\n    const Blocker = options => {\n      const settings = {\n        layerClass: resolve$1('blocker'),\n        ...options\n      };\n      const div = SugarElement.fromTag('div');\n      set$2(div, 'role', 'presentation');\n      setAll(div, {\n        position: 'fixed',\n        left: '0px',\n        top: '0px',\n        width: '100%',\n        height: '100%'\n      });\n      add(div, resolve$1('blocker'));\n      add(div, settings.layerClass);\n      const element = constant(div);\n      const destroy = () => {\n        remove$6(div);\n      };\n      return {\n        element,\n        destroy\n      };\n    };\n\n    const compare = (old, nu) => {\n      return SugarPosition(nu.left - old.left, nu.top - old.top);\n    };\n    const extract = event => {\n      return Optional.some(SugarPosition(event.x, event.y));\n    };\n    const mutate = (mutation, info) => {\n      mutation.mutate(info.left, info.top);\n    };\n    const sink = (dragApi, settings) => {\n      const blocker = Blocker(settings);\n      const mdown = bind(blocker.element(), 'mousedown', dragApi.forceDrop);\n      const mup = bind(blocker.element(), 'mouseup', dragApi.drop);\n      const mmove = bind(blocker.element(), 'mousemove', dragApi.move);\n      const mout = bind(blocker.element(), 'mouseout', dragApi.delayDrop);\n      const destroy = () => {\n        blocker.destroy();\n        mup.unbind();\n        mmove.unbind();\n        mout.unbind();\n        mdown.unbind();\n      };\n      const start = parent => {\n        append$1(parent, blocker.element());\n      };\n      const stop = () => {\n        remove$6(blocker.element());\n      };\n      return DragSink({\n        element: blocker.element,\n        start,\n        stop,\n        destroy\n      });\n    };\n    var MouseDrag = DragMode({\n      compare,\n      extract,\n      sink,\n      mutate\n    });\n\n    const transform = (mutation, settings = {}) => {\n      var _a;\n      const mode = (_a = settings.mode) !== null && _a !== void 0 ? _a : MouseDrag;\n      return setup(mutation, mode, settings);\n    };\n\n    const styles = css('ephox-snooker');\n    const resolve = styles.resolve;\n\n    const Mutation = () => {\n      const events = create$1({\n        drag: Event([\n          'xDelta',\n          'yDelta'\n        ])\n      });\n      const mutate = (x, y) => {\n        events.trigger.drag(x, y);\n      };\n      return {\n        mutate,\n        events: events.registry\n      };\n    };\n\n    const BarMutation = () => {\n      const events = create$1({\n        drag: Event([\n          'xDelta',\n          'yDelta',\n          'target'\n        ])\n      });\n      let target = Optional.none();\n      const delegate = Mutation();\n      delegate.events.drag.bind(event => {\n        target.each(t => {\n          events.trigger.drag(event.xDelta, event.yDelta, t);\n        });\n      });\n      const assign = t => {\n        target = Optional.some(t);\n      };\n      const get = () => {\n        return target;\n      };\n      return {\n        assign,\n        get,\n        mutate: delegate.mutate,\n        events: events.registry\n      };\n    };\n\n    const col = (column, x, y, w, h) => {\n      const bar = SugarElement.fromTag('div');\n      setAll(bar, {\n        position: 'absolute',\n        left: x - w / 2 + 'px',\n        top: y + 'px',\n        height: h + 'px',\n        width: w + 'px'\n      });\n      setAll$1(bar, {\n        'data-column': column,\n        'role': 'presentation'\n      });\n      return bar;\n    };\n    const row = (r, x, y, w, h) => {\n      const bar = SugarElement.fromTag('div');\n      setAll(bar, {\n        position: 'absolute',\n        left: x + 'px',\n        top: y - h / 2 + 'px',\n        height: h + 'px',\n        width: w + 'px'\n      });\n      setAll$1(bar, {\n        'data-row': r,\n        'role': 'presentation'\n      });\n      return bar;\n    };\n\n    const resizeBar = resolve('resizer-bar');\n    const resizeRowBar = resolve('resizer-rows');\n    const resizeColBar = resolve('resizer-cols');\n    const BAR_THICKNESS = 7;\n    const resizableRows = (warehouse, isResizable) => bind$2(warehouse.all, (row, i) => isResizable(row.element) ? [i] : []);\n    const resizableColumns = (warehouse, isResizable) => {\n      const resizableCols = [];\n      range$1(warehouse.grid.columns, index => {\n        const colElmOpt = Warehouse.getColumnAt(warehouse, index).map(col => col.element);\n        if (colElmOpt.forall(isResizable)) {\n          resizableCols.push(index);\n        }\n      });\n      return filter$2(resizableCols, colIndex => {\n        const columnCells = Warehouse.filterItems(warehouse, cell => cell.column === colIndex);\n        return forall(columnCells, cell => isResizable(cell.element));\n      });\n    };\n    const destroy = wire => {\n      const previous = descendants(wire.parent(), '.' + resizeBar);\n      each$2(previous, remove$6);\n    };\n    const drawBar = (wire, positions, create) => {\n      const origin = wire.origin();\n      each$2(positions, cpOption => {\n        cpOption.each(cp => {\n          const bar = create(origin, cp);\n          add(bar, resizeBar);\n          append$1(wire.parent(), bar);\n        });\n      });\n    };\n    const refreshCol = (wire, colPositions, position, tableHeight) => {\n      drawBar(wire, colPositions, (origin, cp) => {\n        const colBar = col(cp.col, cp.x - origin.left, position.top - origin.top, BAR_THICKNESS, tableHeight);\n        add(colBar, resizeColBar);\n        return colBar;\n      });\n    };\n    const refreshRow = (wire, rowPositions, position, tableWidth) => {\n      drawBar(wire, rowPositions, (origin, cp) => {\n        const rowBar = row(cp.row, position.left - origin.left, cp.y - origin.top, tableWidth, BAR_THICKNESS);\n        add(rowBar, resizeRowBar);\n        return rowBar;\n      });\n    };\n    const refreshGrid = (warhouse, wire, table, rows, cols) => {\n      const position = absolute(table);\n      const isResizable = wire.isResizable;\n      const rowPositions = rows.length > 0 ? height.positions(rows, table) : [];\n      const resizableRowBars = rowPositions.length > 0 ? resizableRows(warhouse, isResizable) : [];\n      const resizableRowPositions = filter$2(rowPositions, (_pos, i) => exists(resizableRowBars, barIndex => i === barIndex));\n      refreshRow(wire, resizableRowPositions, position, getOuter$2(table));\n      const colPositions = cols.length > 0 ? width.positions(cols, table) : [];\n      const resizableColBars = colPositions.length > 0 ? resizableColumns(warhouse, isResizable) : [];\n      const resizableColPositions = filter$2(colPositions, (_pos, i) => exists(resizableColBars, barIndex => i === barIndex));\n      refreshCol(wire, resizableColPositions, position, getOuter$1(table));\n    };\n    const refresh = (wire, table) => {\n      destroy(wire);\n      if (wire.isResizable(table)) {\n        const warehouse = Warehouse.fromTable(table);\n        const rows$1 = rows(warehouse);\n        const cols = columns(warehouse);\n        refreshGrid(warehouse, wire, table, rows$1, cols);\n      }\n    };\n    const each = (wire, f) => {\n      const bars = descendants(wire.parent(), '.' + resizeBar);\n      each$2(bars, f);\n    };\n    const hide = wire => {\n      each(wire, bar => {\n        set$1(bar, 'display', 'none');\n      });\n    };\n    const show = wire => {\n      each(wire, bar => {\n        set$1(bar, 'display', 'block');\n      });\n    };\n    const isRowBar = element => {\n      return has(element, resizeRowBar);\n    };\n    const isColBar = element => {\n      return has(element, resizeColBar);\n    };\n\n    const resizeBarDragging = resolve('resizer-bar-dragging');\n    const BarManager = wire => {\n      const mutation = BarMutation();\n      const resizing = transform(mutation, {});\n      let hoverTable = Optional.none();\n      const getResizer = (element, type) => {\n        return Optional.from(get$b(element, type));\n      };\n      mutation.events.drag.bind(event => {\n        getResizer(event.target, 'data-row').each(_dataRow => {\n          const currentRow = getCssValue(event.target, 'top');\n          set$1(event.target, 'top', currentRow + event.yDelta + 'px');\n        });\n        getResizer(event.target, 'data-column').each(_dataCol => {\n          const currentCol = getCssValue(event.target, 'left');\n          set$1(event.target, 'left', currentCol + event.xDelta + 'px');\n        });\n      });\n      const getDelta = (target, dir) => {\n        const newX = getCssValue(target, dir);\n        const oldX = getAttrValue(target, 'data-initial-' + dir, 0);\n        return newX - oldX;\n      };\n      resizing.events.stop.bind(() => {\n        mutation.get().each(target => {\n          hoverTable.each(table => {\n            getResizer(target, 'data-row').each(row => {\n              const delta = getDelta(target, 'top');\n              remove$7(target, 'data-initial-top');\n              events.trigger.adjustHeight(table, delta, parseInt(row, 10));\n            });\n            getResizer(target, 'data-column').each(column => {\n              const delta = getDelta(target, 'left');\n              remove$7(target, 'data-initial-left');\n              events.trigger.adjustWidth(table, delta, parseInt(column, 10));\n            });\n            refresh(wire, table);\n          });\n        });\n      });\n      const handler = (target, dir) => {\n        events.trigger.startAdjust();\n        mutation.assign(target);\n        set$2(target, 'data-initial-' + dir, getCssValue(target, dir));\n        add(target, resizeBarDragging);\n        set$1(target, 'opacity', '0.2');\n        resizing.go(wire.parent());\n      };\n      const mousedown = bind(wire.parent(), 'mousedown', event => {\n        if (isRowBar(event.target)) {\n          handler(event.target, 'top');\n        }\n        if (isColBar(event.target)) {\n          handler(event.target, 'left');\n        }\n      });\n      const isRoot = e => {\n        return eq$1(e, wire.view());\n      };\n      const findClosestEditableTable = target => closest$1(target, 'table', isRoot).filter(isEditable$1);\n      const mouseover = bind(wire.view(), 'mouseover', event => {\n        findClosestEditableTable(event.target).fold(() => {\n          if (inBody(event.target)) {\n            destroy(wire);\n          }\n        }, table => {\n          if (resizing.isActive()) {\n            hoverTable = Optional.some(table);\n            refresh(wire, table);\n          }\n        });\n      });\n      const destroy$1 = () => {\n        mousedown.unbind();\n        mouseover.unbind();\n        resizing.destroy();\n        destroy(wire);\n      };\n      const refresh$1 = tbl => {\n        refresh(wire, tbl);\n      };\n      const events = create$1({\n        adjustHeight: Event([\n          'table',\n          'delta',\n          'row'\n        ]),\n        adjustWidth: Event([\n          'table',\n          'delta',\n          'column'\n        ]),\n        startAdjust: Event([])\n      });\n      return {\n        destroy: destroy$1,\n        refresh: refresh$1,\n        on: resizing.on,\n        off: resizing.off,\n        hideBars: curry(hide, wire),\n        showBars: curry(show, wire),\n        events: events.registry\n      };\n    };\n\n    const create = (wire, resizing, lazySizing) => {\n      const hdirection = height;\n      const vdirection = width;\n      const manager = BarManager(wire);\n      const events = create$1({\n        beforeResize: Event([\n          'table',\n          'type'\n        ]),\n        afterResize: Event([\n          'table',\n          'type'\n        ]),\n        startDrag: Event([])\n      });\n      manager.events.adjustHeight.bind(event => {\n        const table = event.table;\n        events.trigger.beforeResize(table, 'row');\n        const delta = hdirection.delta(event.delta, table);\n        adjustHeight(table, delta, event.row, hdirection);\n        events.trigger.afterResize(table, 'row');\n      });\n      manager.events.startAdjust.bind(_event => {\n        events.trigger.startDrag();\n      });\n      manager.events.adjustWidth.bind(event => {\n        const table = event.table;\n        events.trigger.beforeResize(table, 'col');\n        const delta = vdirection.delta(event.delta, table);\n        const tableSize = lazySizing(table);\n        adjustWidth(table, delta, event.column, resizing, tableSize);\n        events.trigger.afterResize(table, 'col');\n      });\n      return {\n        on: manager.on,\n        off: manager.off,\n        refreshBars: manager.refresh,\n        hideBars: manager.hideBars,\n        showBars: manager.showBars,\n        destroy: manager.destroy,\n        events: events.registry\n      };\n    };\n    const TableResize = { create };\n\n    const only = (element, isResizable) => {\n      const parent = isDocument(element) ? documentElement(element) : element;\n      return {\n        parent: constant(parent),\n        view: constant(element),\n        origin: constant(SugarPosition(0, 0)),\n        isResizable\n      };\n    };\n    const detached = (editable, chrome, isResizable) => {\n      const origin = () => absolute(chrome);\n      return {\n        parent: constant(chrome),\n        view: constant(editable),\n        origin,\n        isResizable\n      };\n    };\n    const body = (editable, chrome, isResizable) => {\n      return {\n        parent: constant(chrome),\n        view: constant(editable),\n        origin: constant(SugarPosition(0, 0)),\n        isResizable\n      };\n    };\n    const ResizeWire = {\n      only,\n      detached,\n      body\n    };\n\n    const createContainer = () => {\n      const container = SugarElement.fromTag('div');\n      setAll(container, {\n        position: 'static',\n        height: '0',\n        width: '0',\n        padding: '0',\n        margin: '0',\n        border: '0'\n      });\n      append$1(body$1(), container);\n      return container;\n    };\n    const get = (editor, isResizable) => {\n      return editor.inline ? ResizeWire.body(SugarElement.fromDom(editor.getBody()), createContainer(), isResizable) : ResizeWire.only(SugarElement.fromDom(editor.getDoc()), isResizable);\n    };\n    const remove = (editor, wire) => {\n      if (editor.inline) {\n        remove$6(wire.parent());\n      }\n    };\n\n    const isTable = node => isNonNullable(node) && node.nodeName === 'TABLE';\n    const barResizerPrefix = 'bar-';\n    const isResizable = elm => get$b(elm, 'data-mce-resize') !== 'false';\n    const syncPixels = table => {\n      const warehouse = Warehouse.fromTable(table);\n      if (!Warehouse.hasColumns(warehouse)) {\n        each$2(cells$1(table), cell => {\n          const computedWidth = get$a(cell, 'width');\n          set$1(cell, 'width', computedWidth);\n          remove$7(cell, 'width');\n        });\n      }\n    };\n    const TableResizeHandler = editor => {\n      const selectionRng = value();\n      const tableResize = value();\n      const resizeWire = value();\n      let startW;\n      let startRawW;\n      const lazySizing = table => get$5(editor, table);\n      const lazyResizingBehaviour = () => isPreserveTableColumnResizing(editor) ? preserveTable() : resizeTable();\n      const getNumColumns = table => getGridSize(table).columns;\n      const afterCornerResize = (table, origin, width) => {\n        const isRightEdgeResize = endsWith(origin, 'e');\n        if (startRawW === '') {\n          convertToPercentSize(table);\n        }\n        if (width !== startW && startRawW !== '') {\n          set$1(table, 'width', startRawW);\n          const resizing = lazyResizingBehaviour();\n          const tableSize = lazySizing(table);\n          const col = isPreserveTableColumnResizing(editor) || isRightEdgeResize ? getNumColumns(table) - 1 : 0;\n          adjustWidth(table, width - startW, col, resizing, tableSize);\n        } else if (isPercentage$1(startRawW)) {\n          const percentW = parseFloat(startRawW.replace('%', ''));\n          const targetPercentW = width * percentW / startW;\n          set$1(table, 'width', targetPercentW + '%');\n        }\n        if (isPixel(startRawW)) {\n          syncPixels(table);\n        }\n      };\n      const destroy = () => {\n        tableResize.on(sz => {\n          sz.destroy();\n        });\n        resizeWire.on(w => {\n          remove(editor, w);\n        });\n      };\n      editor.on('init', () => {\n        const rawWire = get(editor, isResizable);\n        resizeWire.set(rawWire);\n        if (hasTableObjectResizing(editor) && hasTableResizeBars(editor)) {\n          const resizing = lazyResizingBehaviour();\n          const sz = TableResize.create(rawWire, resizing, lazySizing);\n          sz.on();\n          sz.events.startDrag.bind(_event => {\n            selectionRng.set(editor.selection.getRng());\n          });\n          sz.events.beforeResize.bind(event => {\n            const rawTable = event.table.dom;\n            fireObjectResizeStart(editor, rawTable, getPixelWidth(rawTable), getPixelHeight(rawTable), barResizerPrefix + event.type);\n          });\n          sz.events.afterResize.bind(event => {\n            const table = event.table;\n            const rawTable = table.dom;\n            removeDataStyle(table);\n            selectionRng.on(rng => {\n              editor.selection.setRng(rng);\n              editor.focus();\n            });\n            fireObjectResized(editor, rawTable, getPixelWidth(rawTable), getPixelHeight(rawTable), barResizerPrefix + event.type);\n            editor.undoManager.add();\n          });\n          tableResize.set(sz);\n        }\n      });\n      editor.on('ObjectResizeStart', e => {\n        const targetElm = e.target;\n        if (isTable(targetElm)) {\n          const table = SugarElement.fromDom(targetElm);\n          each$2(editor.dom.select('.mce-clonedresizable'), clone => {\n            editor.dom.addClass(clone, 'mce-' + getTableColumnResizingBehaviour(editor) + '-columns');\n          });\n          if (!isPixelSizing(table) && isTablePixelsForced(editor)) {\n            convertToPixelSize(table);\n          } else if (!isPercentSizing(table) && isTablePercentagesForced(editor)) {\n            convertToPercentSize(table);\n          }\n          if (isNoneSizing(table) && startsWith(e.origin, barResizerPrefix)) {\n            convertToPercentSize(table);\n          }\n          startW = e.width;\n          startRawW = isTableResponsiveForced(editor) ? '' : getRawWidth(editor, targetElm).getOr('');\n        }\n      });\n      editor.on('ObjectResized', e => {\n        const targetElm = e.target;\n        if (isTable(targetElm)) {\n          const table = SugarElement.fromDom(targetElm);\n          const origin = e.origin;\n          if (startsWith(origin, 'corner-')) {\n            afterCornerResize(table, origin, e.width);\n          }\n          removeDataStyle(table);\n          fireTableModified(editor, table.dom, styleModified);\n        }\n      });\n      editor.on('SwitchMode', () => {\n        tableResize.on(resize => {\n          if (editor.mode.isReadOnly()) {\n            resize.hideBars();\n          } else {\n            resize.showBars();\n          }\n        });\n      });\n      editor.on('dragstart dragend', e => {\n        tableResize.on(resize => {\n          if (e.type === 'dragstart') {\n            resize.hideBars();\n            resize.off();\n          } else {\n            resize.on();\n            resize.showBars();\n          }\n        });\n      });\n      editor.on('remove', () => {\n        destroy();\n      });\n      const refresh = table => {\n        tableResize.on(resize => resize.refreshBars(SugarElement.fromDom(table)));\n      };\n      const hide = () => {\n        tableResize.on(resize => resize.hideBars());\n      };\n      const show = () => {\n        tableResize.on(resize => resize.showBars());\n      };\n      return {\n        refresh,\n        hide,\n        show\n      };\n    };\n\n    const setupTable = editor => {\n      register(editor);\n      const resizeHandler = TableResizeHandler(editor);\n      const cellSelectionHandler = TableCellSelectionHandler(editor, resizeHandler);\n      const actions = TableActions(editor, resizeHandler, cellSelectionHandler);\n      registerCommands(editor, actions);\n      registerQueryCommands(editor, actions);\n      registerEvents(editor, actions);\n      return {\n        getSelectedCells: cellSelectionHandler.getSelectedCells,\n        clearSelectedCells: cellSelectionHandler.clearSelectedCells\n      };\n    };\n\n    const DomModel = editor => {\n      const table = setupTable(editor);\n      return { table };\n    };\n    var Model = () => {\n      global$1.add('dom', DomModel);\n    };\n\n    Model();\n\n})();\n", "// Exports the \"dom\" model for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/models/dom')\n//   ES2015:\n//     import 'tinymce/models/dom'\nrequire('./model.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,sBAAsB;AAEhE,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,CAAAA,UAAQ,CAAAC,WAAS,OAAOA,MAAK,MAAMD;AACpD,YAAM,eAAe,CAAAA,UAAQ,CAAAC,WAAS,OAAOA,WAAUD;AACvD,YAAM,OAAO,OAAK,OAAK,MAAM;AAC7B,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,UAAU,SAAS,OAAO;AAChC,YAAM,SAAS,KAAK,IAAI;AACxB,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,cAAc,KAAK,MAAS;AAClC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,YAAM,aAAa,aAAa,UAAU;AAC1C,YAAM,WAAW,aAAa,QAAQ;AAEtC,YAAM,OAAO,MAAM;AAAA,MACnB;AACA,YAAM,UAAU,CAAC,IAAI,OAAO;AAC1B,eAAO,IAAI,SAAS;AAClB,iBAAO,GAAG,GAAG,MAAM,MAAM,IAAI,CAAC;AAAA,QAChC;AAAA,MACF;AACA,YAAM,WAAW,CAAC,KAAK,QAAQ,OAAK,IAAI,IAAI,CAAC,CAAC;AAC9C,YAAM,WAAW,CAAAC,WAAS;AACxB,eAAO,MAAM;AACX,iBAAOA;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,OAAK;AACpB,eAAO;AAAA,MACT;AACA,YAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,eAAO,MAAM;AAAA,MACf;AACA,eAAS,MAAM,OAAO,aAAa;AACjC,eAAO,IAAI,aAAa;AACtB,gBAAMC,OAAM,YAAY,OAAO,QAAQ;AACvC,iBAAO,GAAG,MAAM,MAAMA,IAAG;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,MAAM,OAAK,OAAK,CAAC,EAAE,CAAC;AAC1B,YAAM,MAAM,SAAO;AACjB,eAAO,MAAM;AACX,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AAAA,MACF;AACA,YAAM,QAAQ,OAAK;AACjB,eAAO,EAAE;AAAA,MACX;AACA,YAAM,QAAQ,SAAS,KAAK;AAC5B,YAAM,SAAS,SAAS,IAAI;AAE5B,YAAM,SAAS;AAAA,QACb,YAAY,KAAKD,QAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQA;AAAA,QACf;AAAA,QACA,OAAO,KAAKA,QAAO;AACjB,iBAAO,IAAI,SAAS,MAAMA,MAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAKE,SAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAOA,QAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAKF,QAAO;AACjB,iBAAO,cAAcA,MAAK,IAAI,SAAS,KAAKA,MAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,WAAY;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,cAAc,MAAM,UAAU;AACpC,YAAM,gBAAgB,MAAM,UAAU;AACtC,YAAM,aAAa,MAAM,UAAU;AACnC,YAAM,aAAa,CAAC,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC;AACtD,YAAM,aAAa,CAAC,IAAI,MAAM,WAAW,IAAI,CAAC,IAAI;AAClD,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG;AACb,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,UAAU,CAAC,KAAK,MAAM;AAC1B,cAAMG,KAAI,CAAC;AACX,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAAA,GAAE,KAAK,EAAE,CAAC,CAAC;AAAA,QACb;AACA,eAAOA;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,IAAI,MAAM;AACvB,cAAM,MAAM,GAAG;AACf,cAAMA,KAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG;AACb,UAAAA,GAAE,KAAK,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAOA;AAAA,MACT;AACA,YAAM,SAAS,CAAC,IAAI,MAAM;AACxB,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG;AACb,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,IAAI,MAAM;AACvB,iBAAS,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,gBAAM,IAAI,GAAG;AACb,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,YAAY,CAAC,IAAI,SAAS;AAC9B,cAAM,OAAO,CAAC;AACd,cAAM,OAAO,CAAC;AACd,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG;AACb,gBAAM,MAAM,KAAK,GAAG,CAAC,IAAI,OAAO;AAChC,cAAI,KAAK,CAAC;AAAA,QACZ;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW,CAAC,IAAI,SAAS;AAC7B,cAAMA,KAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG;AACb,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,YAAAA,GAAE,KAAK,CAAC;AAAA,UACV;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,cAAM,IAAI,CAAC,GAAG,MAAM;AAClB,gBAAM,EAAE,KAAK,GAAG,CAAC;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,eAAO,IAAI,CAAC,GAAG,MAAM;AACnB,gBAAM,EAAE,KAAK,GAAG,CAAC;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,YAAY,CAAC,IAAI,MAAM,UAAU;AACrC,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG;AACb,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,mBAAO,SAAS,KAAK,CAAC;AAAA,UACxB,WAAW,MAAM,GAAG,CAAC,GAAG;AACtB;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,eAAO,UAAU,IAAI,MAAM,KAAK;AAAA,MAClC;AACA,YAAM,YAAY,CAAC,IAAI,SAAS;AAC9B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG;AACb,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,mBAAO,SAAS,KAAK,CAAC;AAAA,UACxB;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,UAAU,QAAM;AACpB,cAAMA,KAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,EAAE,GAAG;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,UAC7E;AACA,qBAAW,MAAMA,IAAG,GAAG,EAAE;AAAA,QAC3B;AACA,eAAOA;AAAA,MACT;AACA,YAAM,SAAS,CAAC,IAAI,MAAM,QAAQ,MAAM,IAAI,CAAC,CAAC;AAC9C,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,gBAAM,IAAI,GAAG;AACb,cAAI,KAAK,GAAG,CAAC,MAAM,MAAM;AACvB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,UAAU,QAAM;AACpB,cAAMA,KAAI,YAAY,KAAK,IAAI,CAAC;AAChC,QAAAA,GAAE,QAAQ;AACV,eAAOA;AAAA,MACT;AACA,YAAM,cAAc,CAAC,IAAI,MAAM;AAC7B,cAAMA,KAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG;AACb,UAAAA,GAAE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AAAA,QACvB;AACA,eAAOA;AAAA,MACT;AACA,YAAM,SAAS,CAAC,IAAI,eAAe;AACjC,cAAMC,QAAO,YAAY,KAAK,IAAI,CAAC;AACnC,QAAAA,MAAK,KAAK,UAAU;AACpB,eAAOA;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,EAAE,IAAI,SAAS,KAAK;AACxF,YAAM,OAAO,QAAM,MAAM,IAAI,CAAC;AAC9B,YAAM,SAAS,QAAM,MAAM,IAAI,GAAG,SAAS,CAAC;AAC5C,YAAM,UAAU,CAAC,KAAK,MAAM;AAC1B,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAMD,KAAI,EAAE,IAAI,IAAI,CAAC;AACrB,cAAIA,GAAE,OAAO,GAAG;AACd,mBAAOA;AAAA,UACT;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,YAAM,OAAO,OAAO;AACpB,YAAM,iBAAiB,OAAO;AAC9B,YAAM,SAAS,CAAC,KAAK,MAAM;AACzB,cAAM,QAAQ,KAAK,GAAG;AACtB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,gBAAM,IAAI,MAAM;AAChB,gBAAM,IAAI,IAAI;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,MAAM,CAAC,KAAK,MAAM;AACtB,eAAO,SAAS,KAAK,CAAC,GAAG,OAAO;AAAA,UAC9B,GAAG;AAAA,UACH,GAAG,EAAE,GAAG,CAAC;AAAA,QACX,EAAE;AAAA,MACJ;AACA,YAAM,WAAW,CAAC,KAAK,MAAM;AAC3B,cAAMA,KAAI,CAAC;AACX,eAAO,KAAK,CAAC,GAAG,MAAM;AACpB,gBAAM,QAAQ,EAAE,GAAG,CAAC;AACpB,UAAAA,GAAE,MAAM,KAAK,MAAM;AAAA,QACrB,CAAC;AACD,eAAOA;AAAA,MACT;AACA,YAAM,SAAS,CAAAA,OAAK,CAAC,GAAG,MAAM;AAC5B,QAAAA,GAAE,KAAK;AAAA,MACT;AACA,YAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ,YAAY;AACrD,eAAO,KAAK,CAAC,GAAG,MAAM;AACpB,WAAC,KAAK,GAAG,CAAC,IAAI,SAAS,SAAS,GAAG,CAAC;AAAA,QACtC,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,KAAK,SAAS;AAC9B,cAAM,IAAI,CAAC;AACX,uBAAe,KAAK,MAAM,OAAO,CAAC,GAAG,IAAI;AACzC,eAAO;AAAA,MACT;AACA,YAAM,aAAa,CAAC,KAAK,MAAM;AAC7B,cAAMA,KAAI,CAAC;AACX,eAAO,KAAK,CAACH,QAAOK,UAAS;AAC3B,UAAAF,GAAE,KAAK,EAAEH,QAAOK,KAAI,CAAC;AAAA,QACvB,CAAC;AACD,eAAOF;AAAA,MACT;AACA,YAAM,SAAS,SAAO;AACpB,eAAO,WAAW,KAAK,QAAQ;AAAA,MACjC;AACA,YAAM,QAAQ,CAAC,KAAKG,SAAQ;AAC1B,eAAO,MAAM,KAAKA,IAAG,IAAI,SAAS,KAAK,IAAIA,KAAI,IAAI,SAAS,KAAK;AAAA,MACnE;AACA,YAAM,QAAQ,CAAC,KAAKA,SAAQ,eAAe,KAAK,KAAKA,IAAG;AACxD,YAAM,oBAAoB,CAAC,KAAKA,SAAQ,MAAM,KAAKA,IAAG,KAAK,IAAIA,UAAS,UAAa,IAAIA,UAAS;AAClG,YAAM,UAAU,CAAAH,OAAK;AACnB,mBAAW,KAAKA,IAAG;AACjB,cAAI,eAAe,KAAKA,IAAG,CAAC,GAAG;AAC7B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,OAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAEjF,YAAM,OAAO,CAAC,OAAO,UAAU;AAC7B,YAAI,IAAI,UAAU,UAAa,UAAU,OAAO,QAAQ;AACxD,iBAAS,IAAI,GAAG,IAAI,MAAM,UAAU,MAAM,UAAa,MAAM,MAAM,EAAE,GAAG;AACtE,cAAI,EAAE,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AACA,YAAM,YAAY,CAAC,GAAG,UAAU;AAC9B,cAAM,QAAQ,EAAE,MAAM,GAAG;AACzB,eAAO,KAAK,OAAO,KAAK;AAAA,MAC1B;AAEA,YAAM,SAAS,CAACE,OAAM,UAAU;AAC9B,eAAO,UAAUA,OAAM,KAAK;AAAA,MAC9B;AACA,YAAM,WAAW,CAACA,OAAM,UAAU;AAChC,cAAM,SAAS,OAAOA,OAAM,KAAK;AACjC,YAAI,WAAW,UAAa,WAAW,MAAM;AAC3C,gBAAM,IAAI,MAAMA,QAAO,gCAAgC;AAAA,QACzD;AACA,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,OAAO;AAC9B,YAAM,kBAAkB,WAAS;AAC/B,eAAO,SAAS,eAAe,KAAK;AAAA,MACtC;AACA,YAAM,gBAAgB,OAAK;AACzB,cAAM,QAAQ,UAAU,6BAA6B,CAAC;AACtD,eAAO,SAAS,CAAC,MAAM,gBAAgB,KAAK,EAAE,UAAU,cAAc,CAAC,KAAK,mBAAmB,KAAK,eAAe,CAAC,EAAE,YAAY,IAAI;AAAA,MACxI;AAEA,YAAM,UAAU;AAChB,YAAM,WAAW;AACjB,YAAM,oBAAoB;AAC1B,YAAM,UAAU;AAChB,YAAM,OAAO;AAEb,YAAM,OAAO,aAAW;AACtB,cAAMF,KAAI,QAAQ,IAAI;AACtB,eAAOA,GAAE,YAAY;AAAA,MACvB;AACA,YAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,YAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,YAAM,YAAY,aAAW,KAAK,OAAO,MAAM,WAAW,KAAK,OAAO,MAAM;AAC5E,YAAM,gBAAgB,aAAW,UAAU,OAAO,KAAK,cAAc,QAAQ,GAAG;AAChF,YAAM,YAAY,OAAO,OAAO;AAChC,YAAM,SAAS,OAAO,IAAI;AAC1B,YAAM,aAAa,OAAO,QAAQ;AAClC,YAAM,qBAAqB,OAAO,iBAAiB;AACnD,YAAM,QAAQ,SAAO,OAAK,UAAU,CAAC,KAAK,KAAK,CAAC,MAAM;AAEtD,YAAM,SAAS,CAAC,KAAKG,MAAKN,WAAU;AAClC,YAAI,SAASA,MAAK,KAAK,UAAUA,MAAK,KAAK,SAASA,MAAK,GAAG;AAC1D,cAAI,aAAaM,MAAKN,SAAQ,EAAE;AAAA,QAClC,OAAO;AACL,kBAAQ,MAAM,uCAAuCM,MAAK,aAAaN,QAAO,eAAe,GAAG;AAChG,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAASM,MAAKN,WAAU;AACrC,eAAO,QAAQ,KAAKM,MAAKN,MAAK;AAAA,MAChC;AACA,YAAM,WAAW,CAAC,SAAS,UAAU;AACnC,cAAM,MAAM,QAAQ;AACpB,eAAO,OAAO,CAAC,GAAG,MAAM;AACtB,iBAAO,KAAK,GAAG,CAAC;AAAA,QAClB,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,SAAS,UAAU;AACrC,eAAO,OAAO,CAAC,GAAG,MAAM;AACtB,YAAE,KAAK,MAAM;AACX,qBAAS,SAAS,CAAC;AAAA,UACrB,GAAG,CAAAA,WAAS;AACV,mBAAO,QAAQ,KAAK,GAAGA,MAAK;AAAA,UAC9B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,CAAC,SAASM,SAAQ;AAC9B,cAAM,IAAI,QAAQ,IAAI,aAAaA,IAAG;AACtC,eAAO,MAAM,OAAO,SAAY;AAAA,MAClC;AACA,YAAM,SAAS,CAAC,SAASA,SAAQ,SAAS,KAAK,MAAM,SAASA,IAAG,CAAC;AAClE,YAAM,WAAW,CAAC,SAASA,SAAQ;AACjC,gBAAQ,IAAI,gBAAgBA,IAAG;AAAA,MACjC;AACA,YAAM,UAAU,aAAW,MAAM,QAAQ,IAAI,YAAY,CAAC,KAAK,SAAS;AACtE,YAAI,KAAK,QAAQ,KAAK;AACtB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,YAAM,aAAa,CAAC,MAAM,UAAU;AAClC,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,gBAAM,UAAU;AAChB,kBAAQ,MAAM,SAAS,IAAI;AAC3B,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AACA,eAAO,UAAU,IAAI,WAAW,EAAE;AAAA,MACpC;AACA,YAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,cAAc,GAAG;AAClC,eAAO,UAAU,IAAI;AAAA,MACvB;AACA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,eAAe,IAAI;AACpC,eAAO,UAAU,IAAI;AAAA,MACvB;AACA,YAAM,YAAY,UAAQ;AACxB,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,eAAO,EAAE,KAAK,KAAK;AAAA,MACrB;AACA,YAAM,cAAc,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,SAAS;AACpG,YAAM,eAAe;AAAA,QACnB,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT,WAAW;AAAA,MACb;AAEA,YAAM,OAAO,CAAC,SAAS,aAAa;AAClC,cAAM,MAAM,QAAQ;AACpB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,OAAO;AACb,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ,QAAQ;AAAA,UAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB,QAAQ;AAAA,UACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB,QAAQ;AAAA,UAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB,QAAQ;AAAA,UACzC,OAAO;AACL,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AACA,YAAM,iBAAiB,SAAO,IAAI,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,qBAAqB,IAAI,sBAAsB;AACvJ,YAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,cAAMC,QAAO,UAAU,SAAY,WAAW,MAAM;AACpD,eAAO,eAAeA,KAAI,IAAI,CAAC,IAAI,MAAMA,MAAK,iBAAiB,QAAQ,GAAG,aAAa,OAAO;AAAA,MAChG;AACA,YAAM,MAAM,CAAC,UAAU,UAAU;AAC/B,cAAMA,QAAO,UAAU,SAAY,WAAW,MAAM;AACpD,eAAO,eAAeA,KAAI,IAAI,SAAS,KAAK,IAAI,SAAS,KAAKA,MAAK,cAAc,QAAQ,CAAC,EAAE,IAAI,aAAa,OAAO;AAAA,MACtH;AAEA,YAAM,OAAO,CAAC,IAAI,OAAO,GAAG,QAAQ,GAAG;AACvC,YAAM,aAAa,CAAC,IAAI,OAAO;AAC7B,cAAM,KAAK,GAAG;AACd,cAAM,KAAK,GAAG;AACd,eAAO,OAAO,KAAK,QAAQ,GAAG,SAAS,EAAE;AAAA,MAC3C;AACA,YAAM,OAAO;AAEb,YAAM,QAAQ,aAAW,aAAa,QAAQ,QAAQ,IAAI,aAAa;AACvE,YAAM,kBAAkB,SAAO,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAChE,YAAM,kBAAkB,aAAW,aAAa,QAAQ,gBAAgB,OAAO,EAAE,IAAI,eAAe;AACpG,YAAM,cAAc,aAAW,aAAa,QAAQ,gBAAgB,OAAO,EAAE,IAAI,WAAW;AAC5F,YAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,YAAM,gBAAgB,aAAW,SAAS,KAAK,QAAQ,IAAI,aAAa,EAAE,IAAI,aAAa,OAAO;AAClG,YAAM,UAAU,CAAC,SAAS,WAAW;AACnC,cAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,YAAI,MAAM,QAAQ;AAClB,cAAM,MAAM,CAAC;AACb,eAAO,IAAI,eAAe,QAAQ,IAAI,eAAe,QAAW;AAC9D,gBAAM,YAAY,IAAI;AACtB,gBAAM,IAAI,aAAa,QAAQ,SAAS;AACxC,cAAI,KAAK,CAAC;AACV,cAAI,KAAK,CAAC,MAAM,MAAM;AACpB;AAAA,UACF,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,eAAe,EAAE,IAAI,aAAa,OAAO;AAClG,YAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,WAAW,EAAE,IAAI,aAAa,OAAO;AAC9F,YAAM,aAAa,aAAW,MAAM,QAAQ,IAAI,YAAY,aAAa,OAAO;AAChF,YAAM,UAAU,CAAC,SAAS,UAAU;AAClC,cAAM,KAAK,QAAQ,IAAI;AACvB,eAAO,SAAS,KAAK,GAAG,MAAM,EAAE,IAAI,aAAa,OAAO;AAAA,MAC1D;AACA,YAAM,aAAa,aAAW,QAAQ,SAAS,CAAC;AAEhD,YAAM,WAAW,CAAC,QAAQ,YAAY;AACpC,cAAM,WAAW,OAAO,MAAM;AAC9B,iBAAS,KAAK,OAAK;AACjB,YAAE,IAAI,aAAa,QAAQ,KAAK,OAAO,GAAG;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAAC,QAAQ,YAAY;AACnC,cAAM,UAAU,YAAY,MAAM;AAClC,gBAAQ,KAAK,MAAM;AACjB,gBAAM,WAAW,OAAO,MAAM;AAC9B,mBAAS,KAAK,OAAK;AACjB,qBAAS,GAAG,OAAO;AAAA,UACrB,CAAC;AAAA,QACH,GAAG,OAAK;AACN,mBAAS,GAAG,OAAO;AAAA,QACrB,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAACC,SAAQ,YAAY;AACnC,cAAM,eAAe,WAAWA,OAAM;AACtC,qBAAa,KAAK,MAAM;AACtB,mBAASA,SAAQ,OAAO;AAAA,QAC1B,GAAG,OAAK;AACN,UAAAA,QAAO,IAAI,aAAa,QAAQ,KAAK,EAAE,GAAG;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAACA,SAAQ,YAAY;AACpC,QAAAA,QAAO,IAAI,YAAY,QAAQ,GAAG;AAAA,MACpC;AACA,YAAM,WAAW,CAACA,SAAQ,SAAS,UAAU;AAC3C,gBAAQA,SAAQ,KAAK,EAAE,KAAK,MAAM;AAChC,mBAASA,SAAQ,OAAO;AAAA,QAC1B,GAAG,OAAK;AACN,mBAAS,GAAG,OAAO;AAAA,QACrB,CAAC;AAAA,MACH;AACA,YAAM,OAAO,CAAC,SAAS,YAAY;AACjC,iBAAS,SAAS,OAAO;AACzB,iBAAS,SAAS,OAAO;AAAA,MAC3B;AAEA,YAAM,UAAU,CAAC,QAAQ,aAAa;AACpC,eAAO,UAAU,CAAC,GAAG,MAAM;AACzB,gBAAM,IAAI,MAAM,IAAI,SAAS,SAAS,IAAI;AAC1C,kBAAQ,GAAG,CAAC;AAAA,QACd,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAACA,SAAQ,aAAa;AACnC,eAAO,UAAU,OAAK;AACpB,mBAASA,SAAQ,CAAC;AAAA,QACpB,CAAC;AAAA,MACH;AAEA,YAAM,QAAQ,aAAW;AACvB,gBAAQ,IAAI,cAAc;AAC1B,eAAO,WAAW,OAAO,GAAG,WAAS;AACnC,mBAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH;AACA,YAAM,WAAW,aAAW;AAC1B,cAAM,MAAM,QAAQ;AACpB,YAAI,IAAI,eAAe,MAAM;AAC3B,cAAI,WAAW,YAAY,GAAG;AAAA,QAChC;AAAA,MACF;AACA,YAAM,SAAS,aAAW;AACxB,cAAMC,YAAW,WAAW,OAAO;AACnC,YAAIA,UAAS,SAAS,GAAG;AACvB,kBAAQ,SAASA,SAAQ;AAAA,QAC3B;AACA,iBAAS,OAAO;AAAA,MAClB;AAEA,YAAM,UAAU,CAAC,UAAU,WAAW,aAAa,QAAQ,SAAS,IAAI,UAAU,MAAM,CAAC;AACzF,YAAM,UAAU,cAAY,QAAQ,UAAU,KAAK;AACnD,YAAM,OAAO,cAAY,QAAQ,UAAU,IAAI;AAC/C,YAAM,YAAY,CAAC,UAAU,QAAQ;AACnC,cAAMC,MAAK,aAAa,QAAQ,GAAG;AACnC,cAAM,aAAa,QAAQ,QAAQ;AACnC,iBAASA,KAAI,UAAU;AACvB,eAAOA;AAAA,MACT;AACA,YAAM,SAAS,CAAC,UAAU,QAAQ;AAChC,cAAMA,MAAK,UAAU,UAAU,GAAG;AAClC,cAAM,gBAAgB,WAAW,KAAK,QAAQ,CAAC;AAC/C,eAAOA,KAAI,aAAa;AACxB,eAAOA;AAAA,MACT;AACA,YAAM,WAAW,CAAC,UAAU,QAAQ;AAClC,cAAMA,MAAK,UAAU,UAAU,GAAG;AAClC,gBAAQ,UAAUA,GAAE;AACpB,cAAMD,YAAW,WAAW,QAAQ;AACpC,eAAOC,KAAID,SAAQ;AACnB,iBAAS,QAAQ;AACjB,eAAOC;AAAA,MACT;AAEA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,iBAAiB,gBAAc,WAAW,kBAAkB,UAAU;AAC5E,YAAM,OAAO,CAACC,OAAMC,cAAa;AAAA,QAC/B,MAAAD;AAAA,QACA,SAAAC;AAAA,MACF;AACA,YAAM,UAAU,CAACC,MAAK,YAAY;AAAA,QAChC,KAAAA;AAAA,QACA;AAAA,MACF;AACA,YAAM,SAAS,CAAC,SAAS,SAAS,aAAa;AAAA,QAC7C;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,CAAC,SAAS,SAAS,SAAS,WAAW;AAAA,QACvD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,CAAC,SAAS,SAAS,SAASA,MAAK,QAAQ,cAAc;AAAA,QACtE;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAAA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,CAAC,SAASC,QAAOC,cAAa;AAAA,QAC9C;AAAA,QACA,OAAAD;AAAA,QACA,SAAAC;AAAA,MACF;AACA,YAAM,eAAe,CAAC,SAASD,QAAOC,UAAS,WAAW;AAAA,QACxD;AAAA,QACA,OAAAD;AAAA,QACA,SAAAC;AAAA,QACA;AAAA,MACF;AACA,YAAM,aAAa,CAAC,SAAS,OAAO,cAAc;AAAA,QAChD;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,CAAC,SAASD,QAAOC,UAAS,WAAW;AAAA,QACpD;AAAA,QACA,OAAAD;AAAA,QACA,SAAAC;AAAA,QACA;AAAA,MACF;AACA,YAAM,SAAS,CAAC,UAAU,UAAU,WAAW,eAAe;AAAA,QAC5D;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,CAAC,SAAS,SAAS,YAAY;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,CAAC,SAASH,cAAa;AAAA,QACtC;AAAA,QACA,SAAAA;AAAA,MACF;AAEA,YAAM,eAAe,SAAO,mBAAmB,GAAG,KAAK,cAAc,IAAI,IAAI,IAAI;AACjF,YAAM,YAAY,WAAW,QAAQ,UAAU,YAAY,KAAK,WAAW,KAAK,UAAU,WAAW;AACrG,YAAM,gBAAgB,SAAS,SAAS;AACxC,YAAM,cAAc,YAAY,OAAK,aAAa,QAAQ,EAAE,IAAI,YAAY,CAAC,IAAI;AACjF,YAAM,gBAAgB,OAAK;AACzB,cAAMT,KAAI,YAAY,CAAC;AACvB,eAAO,aAAaA,EAAC,IAAI,SAAS,KAAKA,EAAC,IAAI,SAAS,KAAK;AAAA,MAC5D;AACA,YAAM,gBAAgB,OAAK,aAAa,QAAQ,EAAE,IAAI,IAAI;AAC1D,YAAM,yBAAyB,WAAS;AACtC,YAAI,cAAc,KAAK,cAAc,MAAM,MAAM,GAAG;AAClD,gBAAM,KAAK,aAAa,QAAQ,MAAM,MAAM;AAC5C,cAAI,UAAU,EAAE,KAAK,iBAAiB,EAAE,GAAG;AACzC,gBAAI,MAAM,YAAY,MAAM,cAAc;AACxC,oBAAM,eAAe,MAAM,aAAa;AACxC,kBAAI,cAAc;AAChB,uBAAO,KAAK,YAAY;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK,MAAM,MAAM;AAAA,MACnC;AACA,YAAM,mBAAmB,aAAW,cAAc,QAAQ,IAAI,UAAU;AAExE,YAAM,SAAS,aAAW;AACxB,cAAM,MAAM,OAAO,OAAO,IAAI,QAAQ,IAAI,aAAa,QAAQ;AAC/D,YAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,IAAI;AAChB,eAAO,cAAc,aAAa,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG,SAAS,QAAQ,aAAa,CAAC;AAAA,MACpH;AACA,YAAM,SAAS,MAAM,UAAU,aAAa,QAAQ,QAAQ,CAAC;AAC7D,YAAM,YAAY,SAAO;AACvB,cAAM,IAAI,IAAI,IAAI;AAClB,YAAI,MAAM,QAAQ,MAAM,QAAW;AACjC,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAC7C;AACA,eAAO,aAAa,QAAQ,CAAC;AAAA,MAC/B;AAEA,YAAM,cAAc,CAAC,OAAO,WAAW,WAAW,SAAS,QAAQ,OAAO,MAAM,GAAG,SAAS;AAC5F,YAAM,aAAa,CAAC,OAAO,cAAc,SAAS,WAAW,KAAK,GAAG,SAAS;AAC9E,YAAM,gBAAgB,CAAC,OAAO,cAAc;AAC1C,YAAI,SAAS,CAAC;AACd,eAAO,WAAW,KAAK,GAAG,OAAK;AAC7B,cAAI,UAAU,CAAC,GAAG;AAChB,qBAAS,OAAO,OAAO,CAAC,CAAC,CAAC;AAAA,UAC5B;AACA,mBAAS,OAAO,OAAO,cAAc,GAAG,SAAS,CAAC;AAAA,QACpD,CAAC;AACD,eAAO;AAAA,MACT;AAEA,YAAM,cAAc,CAAC,OAAO,UAAU,WAAW,YAAY,OAAO,OAAK,KAAK,GAAG,QAAQ,GAAG,MAAM;AAClG,YAAM,WAAW,CAAC,OAAO,aAAa,WAAW,OAAO,OAAK,KAAK,GAAG,QAAQ,CAAC;AAC9E,YAAM,cAAc,CAAC,OAAO,aAAa,MAAM,UAAU,KAAK;AAE9D,UAAI,oBAAoB,CAACa,KAAIC,WAAU,OAAO,GAAG,WAAW;AAC1D,YAAID,IAAG,OAAO,CAAC,GAAG;AAChB,iBAAO,SAAS,KAAK,KAAK;AAAA,QAC5B,WAAW,WAAW,MAAM,KAAK,OAAO,KAAK,GAAG;AAC9C,iBAAO,SAAS,KAAK;AAAA,QACvB,OAAO;AACL,iBAAOC,UAAS,OAAO,GAAG,MAAM;AAAA,QAClC;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,OAAO,WAAW,WAAW;AAC/C,YAAI,UAAU,MAAM;AACpB,cAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,eAAO,QAAQ,YAAY;AACzB,oBAAU,QAAQ;AAClB,gBAAM,KAAK,aAAa,QAAQ,OAAO;AACvC,cAAI,UAAU,EAAE,GAAG;AACjB,mBAAO,SAAS,KAAK,EAAE;AAAA,UACzB,WAAW,KAAK,EAAE,GAAG;AACnB;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,YAAY,CAAC,OAAO,WAAW,WAAW;AAC9C,cAAMD,MAAK,CAAC,GAAG,SAAS,KAAK,CAAC;AAC9B,eAAO,kBAAkBA,KAAI,YAAY,OAAO,WAAW,MAAM;AAAA,MACnE;AACA,YAAM,UAAU,CAAC,OAAO,cAAc;AACpC,cAAM,OAAO,UAAQ,UAAU,aAAa,QAAQ,IAAI,CAAC;AACzD,cAAM,SAAS,OAAO,MAAM,IAAI,YAAY,IAAI;AAChD,eAAO,OAAO,IAAI,aAAa,OAAO;AAAA,MACxC;AACA,YAAM,eAAe,CAAC,OAAO,cAAc;AACzC,cAAM,UAAU,UAAQ;AACtB,mBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,kBAAME,SAAQ,aAAa,QAAQ,KAAK,WAAW,EAAE;AACrD,gBAAI,UAAUA,MAAK,GAAG;AACpB,qBAAO,SAAS,KAAKA,MAAK;AAAA,YAC5B;AACA,kBAAM,MAAM,QAAQ,KAAK,WAAW,EAAE;AACtC,gBAAI,IAAI,OAAO,GAAG;AAChB,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO,SAAS,KAAK;AAAA,QACvB;AACA,eAAO,QAAQ,MAAM,GAAG;AAAA,MAC1B;AAEA,YAAM,aAAa,CAAC,OAAO,UAAU,WAAW,WAAW,OAAO,OAAK,KAAK,GAAG,QAAQ,GAAG,MAAM;AAChG,YAAM,QAAQ,CAAC,OAAO,aAAa,QAAQ,OAAO,OAAK,KAAK,GAAG,QAAQ,CAAC;AACxE,YAAM,aAAa,CAAC,OAAO,aAAa,IAAI,UAAU,KAAK;AAC3D,YAAM,YAAY,CAAC,OAAO,UAAU,WAAW;AAC7C,cAAMF,MAAK,CAAC,SAASG,cAAa,KAAK,SAASA,SAAQ;AACxD,eAAO,kBAAkBH,KAAI,YAAY,OAAO,UAAU,MAAM;AAAA,MAClE;AAEA,YAAM,KAAK,CAAC,KAAK,KAAK,aAAa,iBAAiB,IAAI,OAAO,CAAAI,UAAQ,WAAWA,OAAM,GAAG,CAAC;AAC5F,YAAM,MAAM,SAAO;AACjB,cAAMjB,KAAI,CAAC;AACX,cAAM,OAAO,OAAK;AAChB,UAAAA,GAAE,KAAK,CAAC;AAAA,QACV;AACA,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,GAAG,KAAK,IAAI;AAAA,QAClB;AACA,eAAOA;AAAA,MACT;AACA,YAAM,WAAW,CAAC,GAAG,MAAM,MAAM,UAAa,MAAM,OAAO,EAAE,CAAC,IAAI,SAAS,KAAK;AAChF,YAAM,SAAS,CAAC,GAAG,MAAM,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAE9D,YAAM,aAAa,CAAC,KAAK,QAAQ,UAAU,WAAW,MAAM,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,MAAM,MAAM;AACxI,YAAM,WAAW,CAAC,KAAK,QAAQ,QAAQ,GAAG,QAAQ;AAChD,cAAM,MAAM,IAAI,QAAQ,QAAQ,KAAK;AACrC,YAAI,QAAQ,IAAI;AACd,iBAAO,YAAY,GAAG,IAAI,OAAO,MAAM,OAAO,UAAU;AAAA,QAC1D,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,aAAa,CAAC,KAAK,WAAW;AAClC,eAAO,WAAW,KAAK,QAAQ,CAAC;AAAA,MAClC;AACA,YAAM,WAAW,CAAC,KAAK,WAAW;AAChC,eAAO,WAAW,KAAK,QAAQ,IAAI,SAAS,OAAO,MAAM;AAAA,MAC3D;AACA,YAAM,QAAQ,CAAAA,OAAK,OAAK,EAAE,QAAQA,IAAG,EAAE;AACvC,YAAM,OAAO,MAAM,YAAY;AAC/B,YAAM,aAAa,OAAK,EAAE,SAAS;AACnC,YAAM,UAAU,CAAAH,WAAS;AACvB,cAAM,MAAM,WAAWA,MAAK;AAC5B,eAAO,MAAM,GAAG,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,GAAG;AAAA,MACzD;AAEA,YAAM,cAAc,SAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM,gBAAgB;AAE3F,YAAM,cAAc,CAAC,KAAK,UAAUA,WAAU;AAC5C,YAAI,CAAC,SAASA,MAAK,GAAG;AACpB,kBAAQ,MAAM,sCAAsC,UAAU,aAAaA,QAAO,eAAe,GAAG;AACpG,gBAAM,IAAI,MAAM,iCAAiCA,MAAK;AAAA,QACxD;AACA,YAAI,YAAY,GAAG,GAAG;AACpB,cAAI,MAAM,YAAY,UAAUA,MAAK;AAAA,QACvC;AAAA,MACF;AACA,YAAM,iBAAiB,CAAC,KAAK,aAAa;AACxC,YAAI,YAAY,GAAG,GAAG;AACpB,cAAI,MAAM,eAAe,QAAQ;AAAA,QACnC;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAAS,UAAUA,WAAU;AAC1C,cAAM,MAAM,QAAQ;AACpB,oBAAY,KAAK,UAAUA,MAAK;AAAA,MAClC;AACA,YAAM,SAAS,CAAC,SAASqB,SAAQ;AAC/B,cAAM,MAAM,QAAQ;AACpB,eAAOA,MAAK,CAAC,GAAG,MAAM;AACpB,sBAAY,KAAK,GAAG,CAAC;AAAA,QACvB,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,CAAC,SAAS,aAAa;AACnC,cAAM,MAAM,QAAQ;AACpB,cAAMC,UAAS,OAAO,iBAAiB,GAAG;AAC1C,cAAMnB,KAAImB,QAAO,iBAAiB,QAAQ;AAC1C,eAAOnB,OAAM,MAAM,CAAC,OAAO,OAAO,IAAI,kBAAkB,KAAK,QAAQ,IAAIA;AAAA,MAC3E;AACA,YAAM,oBAAoB,CAAC,KAAK,aAAa,YAAY,GAAG,IAAI,IAAI,MAAM,iBAAiB,QAAQ,IAAI;AACvG,YAAM,WAAW,CAAC,SAAS,aAAa;AACtC,cAAM,MAAM,QAAQ;AACpB,cAAM,MAAM,kBAAkB,KAAK,QAAQ;AAC3C,eAAO,SAAS,KAAK,GAAG,EAAE,OAAO,CAAAA,OAAKA,GAAE,SAAS,CAAC;AAAA,MACpD;AACA,YAAM,WAAW,CAAC,SAAS,aAAa;AACtC,cAAM,MAAM,QAAQ;AACpB,uBAAe,KAAK,QAAQ;AAC5B,YAAI,GAAG,OAAO,SAAS,OAAO,EAAE,IAAI,IAAI,GAAG,EAAE,GAAG;AAC9C,mBAAS,SAAS,OAAO;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,SAAS,CAAC,QAAQ,WAAW;AACjC,cAAM,YAAY,OAAO;AACzB,cAAM,YAAY,OAAO;AACzB,YAAI,YAAY,SAAS,KAAK,YAAY,SAAS,GAAG;AACpD,oBAAU,MAAM,UAAU,UAAU,MAAM;AAAA,QAC5C;AAAA,MACF;AAEA,YAAM,eAAe,CAACoB,OAAMlB,OAAMmB,YAAW,MAAM,OAAOD,OAAMlB,KAAI,EAAE,IAAI,CAAAL,WAAS,SAASA,QAAO,EAAE,CAAC,EAAE,MAAMwB,SAAQ;AACtH,YAAM,UAAU,CAACD,OAAMxB,UAAS,aAAawB,OAAMxB,OAAM,CAAC;AAC1D,YAAM,aAAa,eAAa;AAC9B,YAAI,MAAM,KAAK,EAAE,SAAS,GAAG;AAC3B,iBAAO,aAAa,WAAW,QAAQ,CAAC,IAAI;AAAA,QAC9C,OAAO;AACL,iBAAO,QAAQ,WAAW,SAAS,IAAI;AAAA,QACzC;AAAA,MACF;AACA,YAAM,aAAa,CAAAwB,UAAQ,QAAQA,OAAM,SAAS,IAAI;AACtD,YAAM,cAAc,CAAC,SAAS,aAAa,SAAS,MAAM,SAAS,QAAQ,GAAG,EAAE;AAChF,YAAM,WAAW,SAAS,EAAE;AAC5B,YAAM,YAAY,SAAS,EAAE;AAE7B,YAAM,aAAa,CAAC,OAAO,aAAa;AACtC,eAAO,iBAAiB,OAAO,UAAU,MAAM;AAAA,MACjD;AACA,YAAM,mBAAmB,CAAC,OAAO,UAAU,cAAc;AACvD,eAAO,OAAO,WAAW,KAAK,GAAG,OAAK;AACpC,cAAI,KAAK,GAAG,QAAQ,GAAG;AACrB,mBAAO,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAAA,UAC/B,OAAO;AACL,mBAAO,iBAAiB,GAAG,UAAU,SAAS;AAAA,UAChD;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,CAAC,MAAM,SAAS,SAAS,UAAU;AAChD,YAAI,OAAO,OAAO,GAAG;AACnB,iBAAO,SAAS,KAAK;AAAA,QACvB;AACA,YAAI,WAAW,MAAM,KAAK,OAAO,CAAC,GAAG;AACnC,iBAAO,SAAS,KAAK,OAAO;AAAA,QAC9B;AACA,cAAM,qBAAqB,SAAO,KAAK,KAAK,OAAO,KAAK,OAAO,GAAG;AAClE,eAAO,WAAW,SAAS,KAAK,KAAK,GAAG,GAAG,kBAAkB;AAAA,MAC/D;AACA,YAAM,OAAO,CAAC,SAAS,WAAW,OAAO;AAAA,QACvC;AAAA,QACA;AAAA,MACF,GAAG,SAAS,MAAM;AAClB,YAAM,UAAU,CAAAN,cAAY,WAAWA,WAAU,OAAO;AACxD,YAAM,YAAY,CAAAA,cAAY;AAC5B,YAAI,KAAKA,WAAU,UAAU,GAAG;AAC9B,iBAAO,SAASA,WAAU,KAAK;AAAA,QACjC,OAAO;AACL,iBAAO,OAAO,aAAaA,SAAQ,GAAG,iBAAe,SAAS,aAAa,KAAK,CAAC;AAAA,QACnF;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAAS,WAAW,UAAU,SAAS,SAAS,MAAM;AACrE,YAAM,SAAS,CAAAA,cAAY,WAAWA,WAAU,IAAI;AACpD,YAAM,eAAe,CAAAA,cAAY,MAAMA,SAAQ,EAAE,KAAK,SAAS,CAAC,CAAC,GAAG,CAAAQ,WAAS,SAASA,QAAO,UAAU,CAAC;AAExG,YAAM,sBAAsB,CAAC,OAAO,eAAe,MAAM,OAAO,CAAAZ,SAAO;AACrE,YAAI,KAAKA,IAAG,MAAM,YAAY;AAC5B,gBAAMC,SAAQ,MAAM,UAAUD,IAAG,GAAG,YAAU;AAC5C,kBAAM,UAAU,aAAa,QAAQ,QAAQ,CAAC;AAC9C,mBAAO,OAAO,QAAQ,GAAG,OAAO;AAAA,UAClC,CAAC;AACD,iBAAO,UAAUA,MAAKC,QAAO,UAAU;AAAA,QACzC,OAAO;AACL,gBAAMA,SAAQ,MAAM,QAAQD,IAAG,GAAG,CAAAU,UAAQ;AACxC,kBAAM,UAAU,aAAaA,OAAM,WAAW,CAAC;AAC/C,kBAAM,UAAU,aAAaA,OAAM,WAAW,CAAC;AAC/C,mBAAO,OAAOA,OAAM,SAAS,OAAO;AAAA,UACtC,CAAC;AACD,iBAAO,UAAUV,MAAKC,QAAO,WAAWD,IAAG,CAAC;AAAA,QAC9C;AAAA,MACF,CAAC;AACD,YAAM,mBAAmB,WAAS,OAAO,KAAK,EAAE,IAAI,CAAAL,YAAU;AAC5D,cAAM,aAAa,KAAKA,OAAM;AAC9B,eAAO,eAAe,UAAU,IAAI,aAAa;AAAA,MACnD,CAAC,EAAE,MAAM,OAAO;AAChB,YAAM,cAAc,CAAAiB,WAAS;AAC3B,cAAMd,QAAO,OAAOc,MAAK;AACzB,cAAM,iBAAiB,aAAaA,MAAK;AACzC,cAAM,QAAQ;AAAA,UACZ,GAAG;AAAA,UACH,GAAGd;AAAA,QACL;AACA,eAAO,oBAAoB,OAAO,gBAAgB;AAAA,MACpD;AACA,YAAM,iBAAiB,CAAC,OAAOI,aAAY,oBAAoB,OAAO,MAAMA,QAAO;AAEnF,YAAM,SAAS,OAAK;AAClB,YAAI,SAAS;AACb,YAAIZ;AACJ,eAAO,IAAI,SAAS;AAClB,cAAI,CAAC,QAAQ;AACX,qBAAS;AACT,YAAAA,KAAI,EAAE,MAAM,MAAM,IAAI;AAAA,UACxB;AACA,iBAAOA;AAAA,QACT;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,IAAI,SAAS,WAAWuB,gBAAe;AACzD,cAAM,SAAS,GAAG,MAAM,KAAK,QAAQ,KAAK,SAAS,MAAM;AACzD,cAAM,WAAW,GAAG,MAAM,KAAK,CAAC;AAChC,cAAM,WAAW,GAAG,MAAM,KAAK,GAAG,UAAU;AAC5C,cAAM,UAAU,YAAYA,YAAW,kBAAkB;AACzD,cAAM,WAAW,UAAU,CAAC,YAAY,YAAYA,YAAW,0BAA0B;AACzF,cAAM,UAAU,YAAY,YAAY,CAAC;AACzC,cAAM,aAAa,QAAQ,SAAS,KAAK,GAAG,MAAM,KAAK,UAAU,KAAK,SAAS,MAAM;AACrF,cAAM,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;AAC5C,eAAO;AAAA,UACL,QAAQ,SAAS,MAAM;AAAA,UACvB,UAAU,SAAS,QAAQ;AAAA,UAC3B,UAAU,SAAS,QAAQ;AAAA,UAC3B,SAAS,SAAS,OAAO;AAAA,UACzB,SAAS,SAAS,OAAO;AAAA,UACzB,WAAW,GAAG;AAAA,UACd,OAAO,GAAG;AAAA,UACV,WAAW,SAAS,UAAU;AAAA,UAC9B,WAAW,SAAS,SAAS;AAAA,QAC/B;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,SAAS,MAAM;AACjC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,IAAI,QAAQ;AAClB,cAAI,EAAE,KAAK,CAAC,GAAG;AACb,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,SAAS,UAAU;AAC/B,cAAMvB,KAAI,WAAW,SAAS,KAAK;AACnC,YAAI,CAACA,IAAG;AACN,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,QAAQ,OAAK;AACjB,iBAAO,OAAO,MAAM,QAAQA,IAAG,MAAM,CAAC,CAAC;AAAA,QACzC;AACA,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MAChC;AACA,YAAM,WAAW,CAAC,gBAAgB,UAAU;AAC1C,cAAM,eAAe,OAAO,KAAK,EAAE,YAAY;AAC/C,YAAI,eAAe,WAAW,GAAG;AAC/B,iBAAO,UAAU;AAAA,QACnB;AACA,eAAO,KAAK,gBAAgB,YAAY;AAAA,MAC1C;AACA,YAAM,YAAY,MAAM;AACtB,eAAO,KAAK,GAAG,CAAC;AAAA,MAClB;AACA,YAAM,OAAO,CAAC,OAAO,UAAU;AAC7B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU;AAAA,QACd,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAEA,YAAM,kBAAkB,CAACwB,WAAU,kBAAkB;AACnD,eAAO,QAAQ,cAAc,QAAQ,aAAW;AAC9C,gBAAM,UAAU,QAAQ,MAAM,YAAY;AAC1C,iBAAO,OAAOA,WAAU,aAAW;AACjC,gBAAI;AACJ,mBAAO,cAAc,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAAA,UAC/F,CAAC,EAAE,IAAI,WAAS;AAAA,YACd,SAAS,KAAK;AAAA,YACd,SAAS,QAAQ,GAAG,SAAS,QAAQ,SAAS,EAAE,GAAG,CAAC;AAAA,UACtD,EAAE;AAAA,QACJ,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,YAAY,cAAc;AAC1C,cAAM,QAAQ,OAAO,SAAS,EAAE,YAAY;AAC5C,eAAO,OAAO,YAAY,eAAa;AACrC,iBAAO,UAAU,OAAO,KAAK;AAAA,QAC/B,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,CAACA,WAAU,cAAc;AAC7C,eAAO,SAASA,WAAU,SAAS,EAAE,IAAI,aAAW;AAClD,gBAAM,UAAU,QAAQ,OAAO,QAAQ,gBAAgB,SAAS;AAChE,iBAAO;AAAA,YACL,SAAS,QAAQ;AAAA,YACjB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAACC,OAAM,cAAc;AACpC,eAAO,SAASA,OAAM,SAAS,EAAE,IAAI,QAAM;AACzC,gBAAM,UAAU,QAAQ,OAAO,GAAG,gBAAgB,SAAS;AAC3D,iBAAO;AAAA,YACL,SAAS,GAAG;AAAA,YACZ;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,qBAAqB;AAC3B,YAAM,gBAAgB,YAAU;AAC9B,eAAO,cAAY;AACjB,iBAAO,SAAS,UAAU,MAAM;AAAA,QAClC;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB,CAAC,gCAAgC;AAAA,UACjD,QAAQ,cAAY;AAClB,mBAAO,SAAS,UAAU,OAAO,KAAK,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,aAAa;AAAA,UACxI;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ,cAAY;AAClB,mBAAO,SAAS,UAAU,QAAQ,KAAK,CAAC,SAAS,UAAU,aAAa;AAAA,UAC1E;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ,cAAY;AAClB,mBAAO,SAAS,UAAU,MAAM,KAAK,SAAS,UAAU,SAAS;AAAA,UACnE;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ,cAAc,OAAO;AAAA,QAC/B;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB,CAAC,qCAAqC;AAAA,UACtD,QAAQ,cAAc,SAAS;AAAA,QACjC;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ,cAAY;AAClB,oBAAQ,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,SAAS,MAAM,SAAS,UAAU,aAAa;AAAA,UAC5G;AAAA,QACF;AAAA,MACF;AACA,YAAM,OAAO;AAAA,QACX;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,KAAK;AAAA,UAC3B,gBAAgB,CAAC,uCAAuC;AAAA,QAC1D;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAY;AAClB,mBAAO,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,MAAM;AAAA,UAClE;AAAA,UACA,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,SAAS;AAAA,UAC/B,gBAAgB,CAAC,mCAAmC;AAAA,QACtD;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,UAAU;AAAA,UAChC,gBAAgB,CAAC,qCAAqC;AAAA,QACxD;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,OAAO;AAAA,UAC7B,gBAAgB,CAAC;AAAA,QACnB;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,OAAO;AAAA,UAC7B,gBAAgB,CAAC;AAAA,QACnB;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,SAAS;AAAA,UAC/B,gBAAgB,CAAC;AAAA,QACnB;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,MAAM;AAAA,UAC5B,gBAAgB,CAAC,iCAAiC;AAAA,QACpD;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB,UAAU,SAAS,QAAQ;AAAA,QAC3B,MAAM,SAAS,IAAI;AAAA,MACrB;AAEA,YAAM,OAAO;AACb,YAAM,WAAW;AACjB,YAAM,KAAK;AACX,YAAM,QAAQ;AACd,YAAM,UAAU;AAChB,YAAM,SAAS;AACf,YAAM,YAAY,MAAM;AACtB,eAAO,KAAK;AAAA,UACV,SAAS;AAAA,UACT,SAAS,QAAQ,QAAQ;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,YAAM,OAAO,UAAQ;AACnB,cAAM,UAAU,KAAK;AACrB,cAAM,UAAU,KAAK;AACrB,cAAM,YAAY,CAAAvB,UAAQ,MAAM,YAAYA;AAC5C,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ,UAAU,IAAI;AAAA,UACtB,YAAY,UAAU,QAAQ;AAAA,UAC9B,MAAM,UAAU,EAAE;AAAA,UAClB,SAAS,UAAU,KAAK;AAAA,UACxB,WAAW,UAAU,OAAO;AAAA,UAC5B,UAAU,UAAU,MAAM;AAAA,QAC5B;AAAA,MACF;AACA,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,MAAM,SAAS,IAAI;AAAA,QACnB,UAAU,SAAS,QAAQ;AAAA,QAC3B,IAAI,SAAS,EAAE;AAAA,QACf,OAAO,SAAS,KAAK;AAAA,QACrB,SAAS,SAAS,OAAO;AAAA,QACzB,QAAQ,SAAS,MAAM;AAAA,MACzB;AAEA,YAAM,UAAU;AAChB,YAAM,MAAM;AACZ,YAAM,UAAU;AAChB,YAAM,QAAQ;AACd,YAAM,QAAQ;AACd,YAAM,UAAU;AAChB,YAAM,UAAU;AAChB,YAAM,WAAW;AACjB,YAAM,UAAU,MAAM;AACpB,eAAO,GAAG;AAAA,UACR,SAAS;AAAA,UACT,SAAS,QAAQ,QAAQ;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,YAAM,KAAK,UAAQ;AACjB,cAAM,UAAU,KAAK;AACrB,cAAM,UAAU,KAAK;AACrB,cAAM,OAAO,CAAAA,UAAQ,MAAM,YAAYA;AACvC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,WAAW,KAAK,OAAO;AAAA,UACvB,OAAO,KAAK,GAAG;AAAA,UACf,WAAW,KAAK,OAAO;AAAA,UACvB,SAAS,KAAK,KAAK;AAAA,UACnB,SAAS,KAAK,KAAK;AAAA,UACnB,WAAW,KAAK,OAAO;AAAA,UACvB,WAAW,KAAK,OAAO;AAAA,UACvB,YAAY,KAAK,QAAQ;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,SAAS,SAAS,OAAO;AAAA,QACzB,KAAK,SAAS,GAAG;AAAA,QACjB,SAAS,SAAS,OAAO;AAAA,QACzB,OAAO,SAAS,KAAK;AAAA,QACrB,OAAO,SAAS,KAAK;AAAA,QACrB,SAAS,SAAS,OAAO;AAAA,QACzB,SAAS,SAAS,OAAO;AAAA,QACzB,UAAU,SAAS,QAAQ;AAAA,MAC7B;AAEA,YAAM,WAAW,CAAC,WAAW,kBAAkBqB,gBAAe;AAC5D,cAAMC,YAAW,aAAa,SAAS;AACvC,cAAMC,QAAO,aAAa,KAAK;AAC/B,cAAM,UAAU,iBAAiB,KAAK,mBAAiB,gBAAgBD,WAAU,aAAa,CAAC,EAAE,QAAQ,MAAM,cAAcA,WAAU,SAAS,CAAC,EAAE,KAAK,QAAQ,SAAS,QAAQ,EAAE;AACnL,cAAM,KAAK,SAASC,OAAM,SAAS,EAAE,KAAK,gBAAgB,SAAS,gBAAgB,EAAE;AACrF,cAAM,aAAa,WAAW,IAAI,SAAS,WAAWF,WAAU;AAChE,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,oBAAoB,EAAE,QAAQ,SAAS;AAE7C,YAAM,aAAa,WAAS,OAAO,WAAW,KAAK,EAAE;AACrD,UAAI,WAAW,OAAO,MAAM,kBAAkB,OAAO,UAAU,WAAW,SAAS,KAAK,UAAU,aAAa,GAAG,UAAU,CAAC;AAC7H,YAAM,WAAW,MAAM,SAAS;AAEhC,YAAM,YAAY,CAACrB,OAAM,cAAc;AACrC,cAAMwB,OAAM,CAAC,SAAS,MAAM;AAC1B,cAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,MAAM,UAAU,GAAG;AACxC,kBAAM,IAAI,MAAMxB,QAAO,0DAA0D,CAAC;AAAA,UACpF;AACA,gBAAM,MAAM,QAAQ;AACpB,cAAI,YAAY,GAAG,GAAG;AACpB,gBAAI,MAAMA,SAAQ,IAAI;AAAA,UACxB;AAAA,QACF;AACA,cAAMyB,OAAM,aAAW;AACrB,gBAAM3B,KAAI,UAAU,OAAO;AAC3B,cAAIA,MAAK,KAAKA,OAAM,MAAM;AACxB,kBAAMkB,OAAM,MAAM,SAAShB,KAAI;AAC/B,mBAAO,WAAWgB,IAAG,KAAK;AAAA,UAC5B;AACA,iBAAOlB;AAAA,QACT;AACA,cAAM4B,YAAWD;AACjB,cAAM,YAAY,CAAC,SAAS,eAAe,MAAM,YAAY,CAAC,KAAK,aAAa;AAC9E,gBAAM,MAAM,MAAM,SAAS,QAAQ;AACnC,gBAAM9B,SAAQ,QAAQ,SAAY,IAAI,SAAS,KAAK,EAAE;AACtD,iBAAO,MAAMA,MAAK,IAAI,MAAM,MAAMA;AAAA,QACpC,GAAG,CAAC;AACJ,cAAM,MAAM,CAAC,SAASA,QAAO,eAAe;AAC1C,gBAAM,uBAAuB,UAAU,SAAS,UAAU;AAC1D,gBAAM,cAAcA,SAAQ,uBAAuBA,SAAQ,uBAAuB;AAClF,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,UACL,KAAA6B;AAAA,UACA,KAAAC;AAAA,UACA,UAAAC;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,WAAW,CAAC,IAAIP,cAAa,QAAQ,EAAE,EAAE,MAAMA,SAAQ;AAC7D,YAAM,UAAU,CAAC,SAASnB,OAAMmB,cAAa,SAAS,MAAM,SAASnB,KAAI,GAAGmB,SAAQ;AACpF,YAAM,qBAAqB,CAAC,SAAS,MAAM,OAAO,UAAU;AAC1D,cAAM,eAAe,QAAQ,SAAS,WAAY,SAAU,CAAC;AAC7D,cAAM,eAAe,QAAQ,SAAS,WAAY,SAAU,CAAC;AAC7D,cAAM,cAAc,QAAQ,SAAS,UAAW,eAAgB,CAAC;AACjE,cAAM,cAAc,QAAQ,SAAS,UAAW,eAAgB,CAAC;AACjE,eAAO,OAAO,eAAe,eAAe,cAAc;AAAA,MAC5D;AACA,YAAM,qBAAqB,CAAC,SAAS,cAAc;AACjD,cAAM,MAAM,QAAQ;AACpB,cAAMQ,SAAQ,IAAI,sBAAsB,EAAE,SAAS,IAAI;AACvD,eAAO,cAAc,eAAeA,SAAQ,mBAAmB,SAASA,QAAO,QAAQ,OAAO;AAAA,MAChG;AACA,YAAM,cAAc,aAAW,QAAQ,SAAS,UAAU,QAAQ,IAAI,YAAY;AAClF,YAAM,WAAW,aAAW,QAAQ,SAAS,SAAS,QAAQ,IAAI,WAAW;AAC7E,YAAM,gBAAgB,aAAW,mBAAmB,SAAS,aAAa;AAE1E,YAAM,QAAQ,UAAU,SAAS,aAAW,QAAQ,IAAI,WAAW;AACnE,YAAM,QAAQ,aAAW,MAAM,IAAI,OAAO;AAC1C,YAAM,aAAa,aAAW,MAAM,SAAS,OAAO;AACpD,YAAM,WAAW;AACjB,YAAM,eAAe;AAErB,YAAM,WAAW,CAAC,SAAS,OAAOlB,WAAU;AAC1C,cAAM,gBAAgB,QAAQ;AAC9B,cAAMmB,UAAS,cAAc,MAAM,GAAG,KAAK;AAC3C,cAAMC,SAAQ,cAAc,MAAM,KAAK;AACvC,cAAM,WAAWD,QAAO,OAAOnB,MAAK,EAAE,OAAOoB,MAAK;AAClD,eAAO,SAAS,SAAS,QAAQ;AAAA,MACnC;AACA,YAAM,UAAU,CAAC,SAAS,OAAOX,UAAS,SAAS,SAAS,OAAO,CAACA,KAAI,CAAC;AACzE,YAAM,aAAa,CAAC,SAAS,OAAOA,UAAS;AAC3C,cAAMT,SAAQ,QAAQ;AACtB,QAAAA,OAAM,SAASS;AAAA,MACjB;AACA,YAAM,WAAW,CAAC,SAAST,WAAU,SAAS,QAAQ,SAASA,QAAO,QAAQ,SAAS,QAAQ,KAAK;AACpG,YAAM,WAAW,CAAC,SAAS,MAAM;AAC/B,cAAMA,SAAQ,QAAQ;AACtB,cAAMX,KAAI,MAAMW,QAAO,CAAC;AACxB,eAAO,SAAS,QAAQ,SAASX,IAAG,QAAQ,SAAS,QAAQ,KAAK;AAAA,MACpE;AACA,YAAM,UAAU,CAAC,SAAS,UAAU,QAAQ,MAAM;AAClD,YAAM,iBAAiB,CAAC,SAAS,UAAU,QAAQ,SAAS,KAAK,EAAE;AACnE,YAAM,aAAa,aAAW,QAAQ,MAAM;AAC5C,YAAM,qBAAqB,CAAAgC,UAAQ;AACjC,cAAM,SAAS,UAAUA,OAAM,CAAAtB,SAAOA,KAAI,YAAY,UAAU;AAChE,eAAO;AAAA,UACL,MAAM,OAAO;AAAA,UACb,MAAM,OAAO;AAAA,QACf;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAASuB,WAAU,cAAc;AAC9C,cAAM,WAAW,MAAM,QAAQ,OAAO,SAAS;AAC/C,eAAO,SAASA,UAAS,QAAQ,OAAO,GAAG,UAAU,QAAQ,SAAS,IAAI;AAAA,MAC5E;AAEA,YAAM,kBAAkB;AACxB,YAAM,4BAA4B,CAAAX,WAAS,OAAOA,QAAO,eAAe,EAAE,KAAK,kBAAgB,SAAS,KAAK,aAAa,MAAM,MAAM,CAAC,CAAC,EAAE,IAAI,gBAAc,YAAY,YAAY,MAAM,CAAC;AAC3L,YAAM,2BAA2B,CAAAU,UAAQ;AACvC,cAAM,SAAS,MAAM,mBAAmBA,KAAI,EAAE,MAAM,CAAC,KAAKtB,SAAQ;AAChE,iBAAOA,KAAI,OAAO,CAACU,OAAM,QAAQ;AAC/B,gBAAIA,MAAK,UAAU;AACjB,kBAAI,OAAO;AAAA,YACb;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AACL,cAAM,YAAY,WAAW,QAAQ,CAAC,MAAMjB,SAAQ,SAASA,MAAK,EAAE,CAAC;AACrE,eAAO,OAAO,SAAS;AAAA,MACzB;AAEA,YAAM,MAAM,CAACO,MAAK,WAAW;AAC3B,eAAOA,OAAM,MAAM;AAAA,MACrB;AACA,YAAM,QAAQ,CAAC,WAAWA,MAAK,WAAW,SAAS,KAAK,UAAU,OAAO,IAAIA,MAAK,MAAM,EAAE;AAC1F,YAAM,WAAW,CAAC,WAAW,MAAM,eAAe;AAChD,cAAM,WAAW,YAAY,WAAW,CAAAwB,YAAU;AAChD,iBAAO,WAAW,MAAMA,QAAO,OAAO;AAAA,QACxC,CAAC;AACD,eAAO,SAAS,SAAS,IAAI,SAAS,KAAK,SAAS,EAAE,IAAI,SAAS,KAAK;AAAA,MAC1E;AACA,YAAM,cAAc,CAAC,WAAW,cAAc;AAC5C,cAAMpC,OAAM,OAAO,UAAU,KAAK,CAAAE,OAAK;AACrC,iBAAOA,GAAE;AAAA,QACX,CAAC;AACD,eAAO,SAASF,MAAK,SAAS;AAAA,MAChC;AACA,YAAM,kBAAkB,aAAW;AACjC,cAAM,eAAe,CAAC;AACtB,YAAI,QAAQ;AACZ,eAAO,QAAQ,OAAO,YAAU;AAC9B,gBAAM,UAAU,OAAO;AACvB,kBAAQ,SAAS,iBAAe;AAC9B,kBAAM,WAAW,QAAQ;AACzB,yBAAa,YAAY,UAAU,OAAO,SAAS,SAAS,QAAQ;AAAA,UACtE,CAAC;AACD,mBAAS;AAAA,QACX,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,aAAa,UAAQ;AACzB,cAAM,SAAS,CAAC;AAChB,cAAMa,SAAQ,CAAC;AACf,cAAM,WAAW,KAAK,IAAI,EAAE,IAAI,aAAW,QAAQ,OAAO,EAAE,KAAK,KAAK;AACtE,cAAM,gBAAgB,SAAS,KAAK,yBAAyB,EAAE,MAAM,CAAC,CAAC;AACvE,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,cAAM;AAAA,UACJ,MAAM;AAAA,UACN,MAAMH;AAAA,QACR,IAAI,UAAU,MAAM,aAAW,QAAQ,YAAY,UAAU;AAC7D,eAAOA,OAAM,aAAW;AACtB,gBAAM,aAAa,CAAC;AACpB,iBAAO,QAAQ,OAAO,aAAW;AAC/B,gBAAI,QAAQ;AACZ,mBAAO,OAAO,IAAI,UAAU,KAAK,OAAO,QAAW;AACjD;AAAA,YACF;AACA,kBAAM,WAAW,kBAAkB,eAAe,MAAM,SAAS,CAAC;AAClE,kBAAM,UAAU,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,UAAU,OAAO,QAAQ;AACrG,qBAAS,yBAAyB,GAAG,yBAAyB,QAAQ,SAAS,0BAA0B;AACvG,uBAAS,sBAAsB,GAAG,sBAAsB,QAAQ,SAAS,uBAAuB;AAC9F,sBAAM,cAAc,WAAW;AAC/B,sBAAM,iBAAiB,QAAQ;AAC/B,sBAAM,SAAS,IAAI,aAAa,cAAc;AAC9C,uBAAO,UAAU;AACjB,6BAAa,KAAK,IAAI,YAAY,iBAAiB,CAAC;AAAA,cACtD;AAAA,YACF;AACA,uBAAW,KAAK,OAAO;AAAA,UACzB,CAAC;AACD;AACA,UAAAG,OAAM,KAAK,UAAU,QAAQ,SAAS,YAAY,QAAQ,OAAO,CAAC;AAClE;AAAA,QACF,CAAC;AACD,cAAM,EAAC,SAAAF,UAAS,UAAS,IAAI,OAAO,YAAY,EAAE,IAAI,aAAW;AAC/D,gBAAMA,WAAU,gBAAgB,OAAO;AACvC,gBAAM,aAAa,SAAS,QAAQ,SAAS,OAAOA,QAAO,CAAC;AAC5D,iBAAO;AAAA,YACL,WAAW,CAAC,UAAU;AAAA,YACtB,SAAAA;AAAA,UACF;AAAA,QACF,CAAC,EAAE,WAAW,OAAO;AAAA,UACnB,WAAW,CAAC;AAAA,UACZ,SAAS,CAAC;AAAA,QACZ,EAAE;AACF,cAAM,SAAS,KAAK,SAAS,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA,UACA,KAAKE;AAAA,UACL,SAAAF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,YAAY,CAAAa,WAAS;AACzB,cAAM,OAAO,YAAYA,MAAK;AAC9B,eAAO,WAAW,IAAI;AAAA,MACxB;AACA,YAAM,YAAY,eAAa,OAAO,UAAU,KAAK,OAAK,EAAE,KAAK;AACjE,YAAM,cAAc,eAAa,OAAO,UAAU,OAAO;AACzD,YAAM,aAAa,eAAa,KAAK,UAAU,OAAO,EAAE,SAAS;AACjE,YAAM,cAAc,CAAC,WAAW,gBAAgB,SAAS,KAAK,UAAU,QAAQ,YAAY;AAC5F,YAAM,YAAY;AAAA,QAChB;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,UAAU,CAAC,WAAW,cAAc,WAAW;AACnD,cAAMU,QAAO,UAAU;AACvB,cAAM,OAAO,QAAQA,MAAK,SAAS,QAAQ;AAC3C,cAAM,UAAU,QAAQA,MAAK,MAAM,QAAQ;AAC3C,eAAO,MAAM,MAAM,CAAAG,SAAO;AACxB,gBAAM,WAAW,MAAM,OAAO,SAAS,CAAAnC,OAAK,UAAU,MAAM,WAAWA,IAAGmC,IAAG,EAAE,OAAO,CAAAD,YAAUA,QAAO,WAAWC,IAAG,EAAE,QAAQ,CAAC;AAChI,gBAAM,UAAU,CAAAD,YAAUA,QAAO,YAAY,KAAK,YAAYA,QAAO,OAAO;AAC5E,gBAAM,cAAc,MAAM,UAAU,MAAM,WAAW,GAAGC,IAAG;AAC3D,iBAAO,OAAO,UAAU,SAAS,WAAW;AAAA,QAC9C,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAAC,UAAU,SAAS,gBAAgB;AACjD,cAAM,UAAU,SAAS;AACzB,cAAM,eAAe,OAAO,SAAS,OAAO;AAC5C,cAAM,eAAe,aAAa,QAAQ,MAAM,SAAS,KAAK,QAAQ,EAAE,EAAE,QAAQ,WAAW,CAAC;AAC9F,eAAO,aAAa,IAAI,CAAAD,YAAUA,QAAO,OAAO;AAAA,MAClD;AACA,YAAM,OAAO,eAAa;AACxB,cAAMF,QAAO,UAAU;AACvB,cAAM,UAAU,QAAQA,MAAK,MAAM,QAAQ;AAC3C,cAAM,OAAO,QAAQA,MAAK,SAAS,QAAQ;AAC3C,eAAO,MAAM,SAAS,CAAAtB,SAAO;AAC3B,gBAAM,WAAW,MAAM,OAAO,MAAM,OAAK,UAAU,MAAM,WAAWA,MAAK,CAAC,EAAE,OAAO,CAAAwB,YAAUA,QAAO,QAAQxB,IAAG,EAAE,KAAK,SAAS,CAAC,CAAC,GAAG,CAAAwB,YAAU,CAACA,OAAM,CAAC,CAAC;AACvJ,gBAAM,WAAW,CAAAA,YAAUA,QAAO,YAAY;AAC9C,gBAAM,cAAc,MAAM,UAAU,MAAM,WAAWxB,MAAK,CAAC;AAC3D,iBAAO,OAAO,UAAU,UAAU,WAAW;AAAA,QAC/C,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,CAAC,IAAI,UAAU;AAC5B,YAAI,QAAQ,KAAK,SAAS,GAAG,SAAS,GAAG;AACvC,iBAAO,SAAS,KAAK;AAAA,QACvB;AACA,cAAM,UAAU,GAAG,OAAO,KAAK,MAAM;AACnC,gBAAM,OAAO,QAAQ,GAAG,MAAM,GAAG,KAAK,CAAC;AACvC,iBAAO,QAAQ,MAAM,CAAC,GAAG,MAAM,EAAE,IAAI,SAAO;AAAA,YAC1C,OAAO;AAAA,YACP,OAAO,IAAI;AAAA,UACb,EAAE,CAAC;AAAA,QACL,GAAG,OAAK,SAAS,KAAK;AAAA,UACpB,OAAO;AAAA,UACP,OAAO;AAAA,QACT,CAAC,CAAC;AACF,cAAM,OAAO,GAAG,QAAQ,GAAG,KAAK,MAAM;AACpC,gBAAM,OAAO,GAAG,MAAM,QAAQ,CAAC;AAC/B,iBAAO,QAAQ,MAAM,CAAC,GAAG,MAAM,EAAE,IAAI,SAAO;AAAA,YAC1C,OAAO;AAAA,YACP,OAAO,IAAI;AAAA,UACb,EAAE,CAAC;AAAA,QACL,GAAG,OAAK,SAAS,KAAK;AAAA,UACpB,OAAO;AAAA,UACP,OAAO;AAAA,QACT,CAAC,CAAC;AACF,eAAO,QAAQ,KAAK,OAAK,KAAK,IAAI,OAAK;AACrC,gBAAM,SAAS,EAAE,QAAQ,EAAE;AAC3B,iBAAO,KAAK,IAAI,EAAE,QAAQ,EAAE,KAAK,IAAI;AAAA,QACvC,CAAC,CAAC;AAAA,MACJ;AAEA,YAAM,cAAc,CAAC,OAAO,UAAU,aAAW,aAAa,OAAO,MAAM,QAAQ,QAAQ;AAC3F,YAAM,eAAe,aAAW,MAAM,SAAS,WAAW,MAAM,QAAQ,QAAQ;AAEhF,YAAM,QAAQ,UAAU,UAAU,aAAW;AAC3C,cAAM,MAAM,QAAQ;AACpB,eAAO,OAAO,OAAO,IAAI,IAAI,sBAAsB,EAAE,SAAS,IAAI;AAAA,MACpE,CAAC;AACD,YAAM,QAAQ,aAAW,MAAM,IAAI,OAAO;AAC1C,YAAM,aAAa,aAAW,MAAM,SAAS,OAAO;AACpD,YAAM,aAAa;AAEnB,YAAM,IAAI,CAACO,OAAM,QAAQ;AACvB,cAAMmB,aAAY,CAAC,GAAG,MAAM,EAAEnB,QAAO,GAAG,MAAM,CAAC;AAC/C,eAAO;AAAA,UACL,MAAAA;AAAA,UACA;AAAA,UACA,WAAAmB;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB;AAEtB,YAAM,cAAc,SAAO;AACzB,cAAM,MAAM,IAAI,sBAAsB;AACtC,eAAO,cAAc,IAAI,MAAM,IAAI,GAAG;AAAA,MACxC;AACA,YAAM,qBAAqB,CAAC,GAAG,MAAM;AACnC,YAAI,MAAM,QAAW;AACnB,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,MAAM,SAAY,IAAI;AAAA,QAC/B;AAAA,MACF;AACA,YAAM,WAAW,aAAW;AAC1B,cAAM,MAAM,QAAQ,IAAI;AACxB,cAAMC,QAAO,IAAI;AACjB,cAAM,MAAM,IAAI;AAChB,cAAM,OAAO,IAAI;AACjB,YAAIA,UAAS,QAAQ,KAAK;AACxB,iBAAO,cAAcA,MAAK,YAAYA,MAAK,SAAS;AAAA,QACtD;AACA,cAAM,YAAY,mBAAmB,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,aAAa,KAAK,SAAS;AAC9G,cAAM,aAAa,mBAAmB,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,aAAa,KAAK,UAAU;AAChH,cAAM,YAAY,mBAAmB,KAAK,WAAWA,MAAK,SAAS;AACnE,cAAM,aAAa,mBAAmB,KAAK,YAAYA,MAAK,UAAU;AACtE,eAAO,SAAS,OAAO,EAAE,UAAU,aAAa,YAAY,YAAY,SAAS;AAAA,MACnF;AACA,YAAM,WAAW,aAAW;AAC1B,cAAM,MAAM,QAAQ;AACpB,cAAM,MAAM,IAAI;AAChB,cAAMA,QAAO,IAAI;AACjB,YAAIA,UAAS,KAAK;AAChB,iBAAO,cAAcA,MAAK,YAAYA,MAAK,SAAS;AAAA,QACtD;AACA,YAAI,CAAC,OAAO,OAAO,GAAG;AACpB,iBAAO,cAAc,GAAG,CAAC;AAAA,QAC3B;AACA,eAAO,YAAY,GAAG;AAAA,MACxB;AAEA,YAAM,UAAU,CAAC3B,MAAK,OAAO;AAAA,QAC3B,KAAAA;AAAA,QACA;AAAA,MACF;AACA,YAAM,UAAU,CAACyB,MAAK,OAAO;AAAA,QAC3B,KAAAA;AAAA,QACA;AAAA,MACF;AACA,YAAM,UAAU,CAAAf,UAAQ;AACtB,cAAM,MAAM,SAASA,KAAI;AACzB,eAAO,IAAI,OAAO,WAAWA,KAAI;AAAA,MACnC;AACA,YAAM,UAAU,CAAAA,UAAQ;AACtB,eAAO,SAASA,KAAI,EAAE;AAAA,MACxB;AACA,YAAM,cAAc,CAAC,OAAOA,UAAS;AACnC,eAAO,QAAQ,OAAO,QAAQA,KAAI,CAAC;AAAA,MACrC;AACA,YAAM,eAAe,CAAC,OAAOA,UAAS;AACpC,eAAO,QAAQ,OAAO,QAAQA,KAAI,CAAC;AAAA,MACrC;AACA,YAAM,WAAW,CAAAA,UAAQ;AACvB,eAAO,SAASA,KAAI,EAAE;AAAA,MACxB;AACA,YAAM,aAAa,CAAC,OAAOA,UAAS;AAClC,eAAO,QAAQ,OAAO,SAASA,KAAI,CAAC;AAAA,MACtC;AACA,YAAM,gBAAgB,CAAC,OAAOA,UAAS;AACrC,eAAO,QAAQ,OAAO,SAASA,KAAI,IAAI,WAAWA,KAAI,CAAC;AAAA,MACzD;AACA,YAAM,gBAAgB,CAAC,cAAc,cAAc,UAAU;AAC3D,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO,CAAC;AAAA,QACV;AACA,cAAM,QAAQ,MAAM,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,UAAU;AACzD,iBAAO,WAAW,IAAI,CAAAA,UAAQ;AAC5B,mBAAO,aAAa,OAAOA,KAAI;AAAA,UACjC,CAAC;AAAA,QACH,CAAC;AACD,cAAM,WAAW,MAAM,MAAM,SAAS,GAAG,IAAI,CAAAA,UAAQ;AACnD,iBAAO,aAAa,MAAM,SAAS,GAAGA,KAAI;AAAA,QAC5C,CAAC;AACD,eAAO,MAAM,OAAO,CAAC,QAAQ,CAAC;AAAA,MAChC;AACA,YAAM,SAAS,UAAQ;AACrB,eAAO,CAAC;AAAA,MACV;AACA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,WAAW,iBAAe,cAAc,YAAY,eAAe,WAAW;AAAA,QAC9E,MAAM;AAAA,MACR;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW,iBAAe,cAAc,aAAa,cAAc,WAAW;AAAA,MAChF;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW,iBAAe,cAAc,cAAc,aAAa,WAAW;AAAA,MAChF;AACA,YAAM,WAAW,YAAY,OAAO,KAAK;AACzC,YAAM,QAAQ;AAAA,QACZ,OAAO,CAAC,QAAQE,WAAU,SAASA,MAAK,EAAE,MAAM,QAAQA,MAAK;AAAA,QAC7D,WAAW,CAAC,MAAMA,WAAU,SAASA,MAAK,EAAE,UAAU,MAAMA,MAAK;AAAA,QACjE,MAAM,CAAAF,UAAQ,SAASA,KAAI,EAAE,KAAKA,KAAI;AAAA,MACxC;AAEA,YAAM,QAAQ;AAAA,QACZ,mBAAmB;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU,CAAC,GAAG;AAAA,QACd,OAAO,CAAC,EAAE;AAAA,MACZ;AACA,YAAM,WAAW,MAAM;AACrB,cAAM,gBAAgB;AACtB,cAAM,gBAAgB,UAAU;AAChC,cAAM,eAAe,SAAS;AAC9B,cAAM,MAAM;AACZ,cAAM,MAAM,WAAS,MAAO;AAC5B,cAAM,yBAAyB;AAAA,UAC7B;AAAA,UACA,gBAAgB,MAAM,IAAI,aAAa,IAAI,IAAI,YAAY;AAAA,UAC3D,MAAM,gBAAgB,IAAI,YAAY;AAAA,UACtC,gBAAgB,IAAI,YAAY;AAAA,QAClC,EAAE,KAAK,GAAG;AACV,cAAM,QAAQ,WAAY;AAC1B,eAAO,IAAI,OAAO,KAAM,aAAc;AAAA,MACxC,GAAG;AACH,YAAM,SAAS,CAAC,MAAM,aAAa,OAAO,UAAU,SAAO,OAAO,MAAM,MAAM,WAAS,SAAS,KAAK,CAAC;AACtG,YAAM,QAAQ,CAAC,OAAO,aAAa;AACjC,cAAM,QAAQ,SAAS,KAAK,QAAQ,KAAK,KAAK,CAAC;AAC/C,eAAO,MAAM,KAAK,WAAS;AACzB,gBAAMvB,SAAQ,OAAO,MAAM,EAAE;AAC7B,gBAAM,UAAU,MAAM;AACtB,cAAI,OAAO,SAAS,QAAQ,GAAG;AAC7B,mBAAO,SAAS,KAAK;AAAA,cACnB,OAAAA;AAAA,cACA,MAAM;AAAA,YACR,CAAC;AAAA,UACH,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,4BAA4B;AAClC,YAAM,uBAAuB;AAC7B,YAAM,UAAU,MAAM,KAAK;AAC3B,YAAM,iBAAiB,CAAC,KAAK,aAAa,gBAAgB;AACxD,cAAM,iBAAiB,cAAc,GAAG,EAAE,WAAW,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC;AAChF,eAAO,YAAY,GAAG,IAAI,YAAY,cAAc,IAAI;AAAA,MAC1D;AACA,YAAM,gBAAgB,CAACuB,OAAM,WAAW;AACtC,cAAMA,OAAM,SAAS,SAAS,IAAI;AAAA,MACpC;AACA,YAAM,qBAAqB,CAACA,OAAM,WAAW;AAC3C,cAAMA,OAAM,SAAS,SAAS,GAAG;AAAA,MACnC;AACA,YAAM,YAAY,CAACA,OAAM,WAAW;AAClC,cAAMA,OAAM,UAAU,SAAS,IAAI;AAAA,MACrC;AACA,YAAM,iBAAiB,CAAAA,UAAQ,WAAWA,KAAI,IAAI;AAClD,YAAM,UAAU,CAACA,OAAM,QAAQ,QAAQ,WAAW;AAChD,cAAM,UAAU,MAAMA,KAAI,EAAE,IAAI,CAAAE,WAAS;AACvC,gBAAMgB,SAAQ,OAAOhB,MAAK;AAC1B,iBAAO,KAAK,MAAM,SAAS,MAAMgB,MAAK;AAAA,QACxC,CAAC,EAAE,MAAM,MAAM;AACf,eAAOlB,OAAM,OAAO;AACpB,eAAO;AAAA,MACT;AACA,YAAM,qBAAqB,CAACvB,QAAOuB,OAAM,QAAQ,WAAW;AAC1D,cAAM,SAAS,WAAWvB,MAAK;AAC/B,eAAO,SAASA,QAAO,GAAG,KAAK,KAAKuB,KAAI,MAAM,UAAU,QAAQA,OAAM,QAAQ,QAAQ,MAAM,IAAI;AAAA,MAClG;AACA,YAAM,iBAAiB,CAAAA,UAAQ;AAC7B,cAAMvB,SAAQ,eAAeuB,KAAI;AACjC,YAAI,CAACvB,QAAO;AACV,iBAAO,MAAMuB,KAAI;AAAA,QACnB;AACA,eAAO,mBAAmBvB,QAAOuB,OAAM,OAAO,SAAS;AAAA,MACzD;AACA,YAAM,QAAQ,CAACA,OAAMxB,OAAM,MAAM;AAC/B,cAAM,IAAI,EAAEwB,KAAI;AAChB,cAAM,OAAO,QAAQA,OAAMxB,KAAI;AAC/B,eAAO,IAAI;AAAA,MACb;AACA,YAAM,WAAW,CAAC,SAAS,SAAS;AAClC,eAAO,SAAS,SAAS,IAAI,EAAE,QAAQ,MAAM;AAC3C,iBAAO,OAAO,SAAS,IAAI,EAAE,IAAI,SAAO,MAAM,IAAI;AAAA,QACpD,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,aAAW,SAAS,SAAS,OAAO;AAC1D,YAAM,eAAe,aAAW,SAAS,SAAS,QAAQ;AAC1D,YAAM,qBAAqB,CAAAwB,UAAQ,eAAeA,OAAM,OAAO,QAAQ;AACvE,YAAM,kBAAkB,CAAAA,UAAQ,QAAQA,KAAI,IAAI,MAAMA,KAAI,IAAI,aAAaA,KAAI;AAC/E,YAAM,YAAY,CAAAA,UAAQ;AACxB,eAAO,MAAMA,OAAM,WAAW,cAAc;AAAA,MAC9C;AACA,YAAM,kBAAkB,CAAAA,UAAQ;AAC9B,cAAMS,SAAQ,cAAcT,KAAI;AAChC,eAAOS,OAAM,KAAK,OAAK,MAAM,GAAG;AAAA,UAC9B;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AACA,YAAM,kBAAkB,CAACT,OAAM,QAAQ,SAAS;AAC9C,cAAMA,OAAM,SAAS,SAAS,IAAI;AAAA,MACpC;AACA,YAAM,qBAAqB,CAAAE,WAAS,MAAMA,MAAK,IAAI;AACnD,YAAM,uBAAuB,CAAAA,WAAS,eAAeA,QAAO,OAAO,QAAQ,IAAI;AAC/E,YAAM,oBAAoB,CAAAA,WAAS,cAAcA,MAAK,EAAE,OAAO,UAAQ,0BAA0B,KAAK,IAAI,CAAC;AAC3G,YAAM,kBAAkB,CAAAA,WAAS,cAAcA,MAAK,EAAE,OAAO,UAAQ,qBAAqB,KAAK,IAAI,CAAC;AACpG,YAAM,iBAAiB,CAAAA,WAAS,cAAcA,MAAK,EAAE,OAAO;AAC5D,YAAM,2BAA2B,SAAS,yBAAyB;AAEnE,YAAM,UAAU,MAAM,KAAK;AAC3B,YAAM,UAAU,CAAAF,UAAQ;AACtB,eAAO,cAAcA,KAAI,EAAE,WAAW,MAAM,gBAAgBA,KAAI,IAAI,IAAI;AAAA,MAC1E;AACA,YAAM,UAAU,CAAAA,UAAQ;AACtB,eAAO,aAAaA,KAAI,EAAE,WAAW,MAAM,UAAUA,KAAI,IAAI,IAAI;AAAA,MACnE;AACA,YAAM,WAAW,eAAa,MAAM,UAAU,YAAY,SAAS,GAAG,YAAU,SAAS,KAAK,OAAO,OAAO,CAAC;AAC7G,YAAM,gBAAgB,CAAAA,UAAQ;AAC5B,cAAM,UAAU,SAAS,EAAE;AAC3B,cAAM,oBAAoB,QAAQ,WAAW,KAAK,QAAQ,UAAU;AACpE,eAAO,QAAQA,KAAI,IAAI,oBAAoB;AAAA,MAC7C;AACA,YAAM,eAAe,CAAC,SAAS,OAAO,SAASmB,SAAQ,QAAQlB,cAAa,QAAQ,OAAOkB,OAAM,EAAE,KAAK,MAAMlB,UAAS,OAAO,SAAS,KAAK,CAAC,GAAG,CAAAD,UAAQ,OAAOA,KAAI,CAAC;AACpK,YAAM,eAAe,CAAC,WAAWE,QAAOkB,WAAUnB,cAAa;AAC7D,cAAM,cAAc,QAAQ,SAAS;AACrC,cAAMoB,aAAY,UAAU,WAAW,SAAS,IAAI,SAAS,SAAS,IAAI;AAC1E,cAAM,UAAU,CAAC,SAAS,KAAK,MAAM,KAAKnB,MAAK,CAAC,CAAC,EAAE,OAAO,MAAM,MAAM,UAAU,aAAaA,MAAK,GAAG,SAAO,IAAI,IAAI,OAAK,EAAE,CAAC,CAAC,CAAC;AAC9H,cAAM,YAAY,IAAI,UAAU;AAChC,eAAO,MAAMmB,YAAW,CAAC,YAAY,MAAM;AACzC,iBAAO,aAAa,YAAY,GAAG,SAAS,WAAW,YAAU;AAC/D,gBAAI,cAAc,MAAM,GAAG;AACzB,qBAAOD,UAAS,MAAM;AAAA,YACxB,OAAO;AACL,oBAAMpB,QAAO,SAAS,YAAY,IAAI,QAAQ;AAC9C,qBAAO,aAAaA,OAAM,GAAG,SAAS,WAAW,CAAAA,UAAQC,UAAS,SAAS,KAAK,MAAMD,KAAI,CAAC,CAAC,GAAGC,SAAQ;AAAA,YACzG;AAAA,UACF,GAAGA,SAAQ;AAAA,QACb,CAAC;AAAA,MACH;AACA,YAAM,aAAa,aAAW;AAC5B,eAAO,QAAQ,IAAI,OAAK;AACtB,iBAAO,IAAI;AAAA,QACb,CAAC,EAAE,MAAM,EAAE;AAAA,MACb;AACA,YAAM,eAAe,CAAC,WAAWC,WAAU;AACzC,eAAO,aAAa,WAAWA,QAAO,SAAS,UAAU;AAAA,MAC3D;AACA,YAAM,sBAAsB,CAAC,WAAWA,QAAO,cAAc;AAC3D,eAAO,aAAa,WAAWA,QAAO,oBAAoB,aAAW;AACnE,iBAAO,QAAQ,KAAK,MAAM;AACxB,mBAAO,UAAU,aAAa;AAAA,UAChC,GAAG,eAAa;AACd,mBAAO,YAAY,UAAU,WAAW,IAAI;AAAA,UAC9C,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,iBAAiB,CAAC,WAAWA,QAAO,cAAc;AACtD,eAAO,aAAa,WAAWA,QAAO,iBAAiB,aAAW;AAChE,iBAAO,QAAQ,WAAW,UAAU,YAAY;AAAA,QAClD,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,CAAC,WAAWA,QAAO,WAAWoB,YAAWrB,cAAa;AAC1E,cAAMsB,UAAS,KAAK,SAAS;AAC7B,cAAM,UAAU,CAAC,SAAS,KAAK,UAAU,KAAKrB,MAAK,CAAC,CAAC,EAAE,OAAO,MAAM,UAAU,UAAUqB,SAAQrB,MAAK,GAAG,SAAO,IAAI,IAAI,OAAK,EAAE,CAAC,CAAC,CAAC;AACjI,eAAO,MAAMqB,SAAQ,CAAC,YAAY,MAAM;AACtC,iBAAO,aAAa,YAAY,GAAG,SAAS,IAAI,UAAU,GAAGD,YAAWrB,SAAQ;AAAA,QAClF,CAAC;AAAA,MACH;AACA,YAAM,kBAAkB,CAAC,WAAWC,QAAO,cAAc;AACvD,eAAO,cAAc,WAAWA,QAAO,WAAW,WAAW,aAAW;AACtE,iBAAO,QAAQ,WAAW,SAAS;AAAA,QACrC,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,CAAC,WAAWA,QAAO,cAAc;AACrD,eAAO,cAAc,WAAWA,QAAO,WAAW,SAAS,UAAU;AAAA,MACvE;AAEA,YAAM,cAAc,CAACA,QAAO,WAAW,MAAM;AAC3C,YAAI,OAAOA,MAAK,GAAG;AACjB,iBAAO,OAAOA,MAAK;AAAA,QACrB,OAAO;AACL,iBAAO,WAAW,SAASA,QAAO,OAAO,EAAE,MAAM,GAAG,CAAC;AAAA,QACvD;AAAA,MACF;AACA,YAAM,WAAW,CAAAA,WAAS;AACxB,cAAMkB,YAAW,YAAYlB,QAAO,KAAK;AACzC,cAAMsB,QAAO,SAAS,CAAC;AACvB,cAAM,YAAY,CAAC,WAAW,cAAc,eAAe,WAAWtB,QAAO,SAAS;AACtF,eAAO;AAAA,UACL,OAAOkB;AAAA,UACP,YAAYA;AAAA,UACZ;AAAA,UACA,cAAcI;AAAA,UACd,mBAAmB,SAAS,CAAC,CAAC,CAAC;AAAA,UAC/B,cAAcA;AAAA,UACd,iBAAiB;AAAA,UACjB,kBAAkB;AAAA,UAClB,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,iBAAiB,CAAAtB,WAAS;AAC9B,cAAM,gBAAgB,YAAYA,QAAO,UAAQ,WAAW,qBAAqB,IAAI,CAAC,CAAC;AACvF,cAAMkB,YAAW,YAAYlB,QAAO,KAAK;AACzC,cAAM,eAAe,WAAS,QAAQkB,UAAS,IAAI;AACnD,cAAM,oBAAoB,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;AACjD,cAAM,eAAe,MAAM,SAAS,IAAIA,UAAS,IAAI;AACrD,cAAM,mBAAmB,WAAS;AAChC,gBAAM,eAAe,cAAc;AACnC,gBAAM,SAAS,QAAQ,MAAM;AAC7B,gBAAM,WAAW,eAAe;AAChC,6BAAmBlB,QAAO,QAAQ;AAAA,QACpC;AACA,cAAM,YAAY,CAAC,WAAW,cAAc,oBAAoB,WAAWA,QAAO,SAAS;AAC3F,eAAO;AAAA,UACL,OAAO;AAAA,UACP,YAAYkB;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,iBAAiB;AAAA,UACjB;AAAA,UACA,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,YAAY,CAAAlB,WAAS;AACzB,cAAMkB,YAAW,YAAYlB,QAAO,KAAK;AACzC,cAAM,eAAe;AACrB,cAAM,oBAAoB,CAAC,GAAG,UAAU;AACtC,gBAAM,UAAU,KAAK,IAAI,SAAS,GAAG,IAAI,KAAK;AAC9C,iBAAO,CAAC,UAAU,CAAC;AAAA,QACrB;AACA,cAAM,mBAAmB,WAAS;AAChC,gBAAM,WAAWkB,UAAS,IAAI;AAC9B,wBAAclB,QAAO,QAAQ;AAAA,QAC/B;AACA,cAAM,YAAY,CAAC,WAAW,cAAc,eAAe,WAAWA,QAAO,SAAS;AACtF,eAAO;AAAA,UACL,OAAOkB;AAAA,UACP,YAAYA;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc;AAAA,UACd,iBAAiB;AAAA,UACjB;AAAA,UACA,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,aAAa,CAAC,SAASX,WAAU;AACrC,cAAM,eAAe,yBAAyB,EAAE,KAAKA,MAAK;AAC1D,YAAI,iBAAiB,MAAM;AACzB,iBAAO,eAAe,OAAO;AAAA,QAC/B,OAAO;AACL,iBAAO,UAAU,OAAO;AAAA,QAC1B;AAAA,MACF;AACA,YAAM,eAAe,CAAAP,WAAS;AAC5B,cAAMO,SAAQ,cAAcP,MAAK;AACjC,eAAOO,OAAM,KAAK,MAAM,SAASP,MAAK,GAAG,OAAK,WAAWA,QAAO,CAAC,CAAC;AAAA,MACpE;AACA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,cAAc,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,UAAU,mBAAmB;AAAA,QAChF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,oBAAoB,CAAC,OAAO,eAAe;AAC/C,cAAM,eAAe,MAAM,KAAK;AAChC,cAAM,YAAY,MAAM,KAAK;AAC7B,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,SAAS;AACb,cAAM,WAAW,CAAC;AAClB,cAAM,gBAAgB,CAAC;AACvB,eAAO,MAAM,QAAQ,CAAAY,YAAU;AAC7B,mBAAS,KAAKA,OAAM;AACpB,cAAI,WAAWA,OAAM,GAAG;AACtB,0BAAc,KAAKA,OAAM;AACzB,kBAAM,WAAWA,QAAO;AACxB,kBAAM,SAAS,WAAWA,QAAO,UAAU;AAC3C,kBAAM,WAAWA,QAAO;AACxB,kBAAM,SAAS,WAAWA,QAAO,UAAU;AAC3C,gBAAI,WAAW,QAAQ;AACrB,uBAAS;AAAA,YACX,WAAW,SAAS,QAAQ;AAC1B,uBAAS;AAAA,YACX;AACA,gBAAI,WAAW,QAAQ;AACrB,uBAAS;AAAA,YACX,WAAW,SAAS,QAAQ;AAC1B,uBAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO,YAAY,QAAQ,QAAQ,QAAQ,QAAQ,UAAU,aAAa;AAAA,MAC5E;AACA,YAAM,WAAW,CAAC,MAAM,cAAc,aAAa;AACjD,cAAMxB,OAAM,KAAK,UAAU;AAC3B,cAAM,KAAK,aAAa,QAAQ,IAAI;AACpC,iBAAS,IAAI,aAAa,QAAQ,IAAI,CAAC;AACvC,cAAM,IAAI,eAAe,WAAW;AACpC,UAAEA,MAAK,EAAE;AAAA,MACX;AACA,YAAM,aAAa,CAAC,MAAM,OAAO,OAAO,eAAe;AACrD,cAAMF,QAAO,SAAS,MAAM,CAAAE,SAAOA,KAAI,YAAY,UAAU;AAC7D,cAAM,eAAe,MAAM,KAAK;AAChC,cAAM,YAAY,MAAM,KAAK;AAC7B,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAI,eAAe;AACnB,mBAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,gBAAI,EAAE,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,IAAI,MAAM,SAAS;AACnF,oBAAM,WAAW,UAAU,MAAM,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU,EAAE,OAAO;AACxE,kBAAI,UAAU;AACZ,yBAASF,OAAM,cAAc,CAAC;AAAA,cAChC,OAAO;AACL,+BAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,SAAS,OAAO,OAAO,eAAe;AACnD,eAAO,MAAM,SAAS,CAAA2B,SAAO;AAC3B,cAAIA,KAAI,SAAS,MAAM,UAAUA,KAAI,SAAS,MAAM,QAAQ;AAC1D,qBAASA,KAAI,OAAO;AAAA,UACtB;AAAA,QACF,CAAC;AACD,cAAM,YAAY,SAAS,WAAW,SAAS,IAAI,GAAG,CAAAzB,SAAOA,KAAI,IAAI,sBAAsB,CAAC;AAC5F,eAAO,WAAW,QAAQ;AAC1B,YAAI,MAAM,WAAW,MAAM,UAAU,MAAM,WAAW,MAAM,QAAQ;AAClE,iBAAO,WAAW,SAAS,OAAO,GAAG,CAAAU,UAAQ;AAC3C,qBAASA,OAAM,SAAS;AACxB,qBAASA,OAAM,SAAS;AAAA,UAC1B,CAAC;AAAA,QACH;AACA,iBAAS,SAAS,eAAe;AACjC,iBAAS,SAAS,yBAAyB;AAC3C,cAAM,YAAY,UAAU,aAAa,OAAO;AAChD,kBAAU,iBAAiB,UAAU;AAAA,MACvC;AACA,YAAM,qBAAqB,CAACE,QAAO,WAAW,WAAW,UAAU;AACjE,YAAI,MAAM,WAAW,KAAK,UAAU,KAAK,YAAY,MAAM,SAAS,GAAG;AACrE,iBAAO;AAAA,QACT;AACA,cAAM,YAAY,eAAe,WAAWA,QAAO,SAAS;AAC5D,cAAM,eAAe,MAAM,WAAW,CAAC,KAAKO,WAAU,MAAMA,QAAO,CAAC;AACpE,cAAM,oBAAoB,MAAM,UAAU,MAAM,MAAM,QAAQ,MAAM,SAAS,CAAC,GAAG,CAAC,KAAKA,WAAU,MAAMA,QAAO,CAAC;AAC/G,cAAM,WAAW,oBAAoB,eAAe,UAAU,WAAW;AACzE,cAAM,QAAQ,WAAW,UAAU,WAAW;AAC9C,eAAO,UAAU,aAAa,KAAK;AAAA,MACrC;AACA,YAAM,YAAY,CAACP,QAAO,qBAAqB;AAC7C,cAAM,aAAa,CAAAY,YAAU,KAAKA,QAAO,SAAS,gBAAgB;AAClE,cAAM,UAAU,KAAKZ,MAAK;AAC1B,cAAM,OAAO,YAAY,OAAO;AAChC,cAAM,YAAY,UAAU,aAAaA,MAAK;AAC9C,cAAM,eAAe,UAAU,SAAS,IAAI;AAC5C,cAAM,eAAe,kBAAkB,cAAc,UAAU;AAC/D,cAAM,WAAW,YAAY,mBAAmB,cAAmB,mBAAmB;AACtF,cAAM,kBAAkB,iBAAiB,SAAS,SAAS,CAAAF,UAAQ,KAAKA,OAAM,QAAQ,CAAC;AACvF,eAAO,iBAAiB,QAAQ;AAChC,mBAAW,MAAM,cAAc,cAAc,UAAU;AACvD,cAAM,QAAQ,UAAU,UAAUE,MAAK;AACvC,cAAM,aAAa,mBAAmBA,QAAO,OAAO,WAAW,YAAY;AAC3E,cAAM,SAAS,cAAc,cAAc,UAAU;AACrD,eAAO;AAAA,MACT;AAEA,YAAM,OAAO;AAEb,YAAM,YAAY,CAACT,KAAIX,UAAS;AAC9B,cAAMyB,OAAM,aAAW;AACrB,cAAI,CAACd,IAAG,OAAO,GAAG;AAChB,kBAAM,IAAI,MAAM,kBAAkBX,QAAO,iBAAiBA,QAAO,OAAO;AAAA,UAC1E;AACA,iBAAO2C,WAAU,OAAO,EAAE,MAAM,EAAE;AAAA,QACpC;AACA,cAAMA,aAAY,aAAWhC,IAAG,OAAO,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,IAAI,SAAS,KAAK;AAChG,cAAMa,OAAM,CAAC,SAAS7B,WAAU;AAC9B,cAAI,CAACgB,IAAG,OAAO,GAAG;AAChB,kBAAM,IAAI,MAAM,sBAAsBX,QAAO,iBAAiBA,QAAO,OAAO;AAAA,UAC9E;AACA,kBAAQ,IAAI,YAAYL;AAAA,QAC1B;AACA,eAAO;AAAA,UACL,KAAA8B;AAAA,UACA,WAAAkB;AAAA,UACA,KAAAnB;AAAA,QACF;AAAA,MACF;AAEA,YAAM,MAAM,UAAU,QAAQ,MAAM;AACpC,YAAM,QAAQ,aAAW,IAAI,IAAI,OAAO;AACxC,YAAM,YAAY,aAAW,IAAI,UAAU,OAAO;AAClD,YAAM,MAAM,CAAC,SAAS7B,WAAU,IAAI,IAAI,SAASA,MAAK;AAEtD,YAAM,SAAS,aAAW,KAAK,OAAO,MAAM,QAAQ,IAAI,UAAU,OAAO,EAAE,KAAK,MAAM,WAAW,OAAO,EAAE,QAAQ,OAAK,EAAE,MAAM;AAC/H,YAAM,+BAA+B,QAAM,UAAU,EAAE,EAAE,OAAO,UAAQ,KAAK,KAAK,EAAE,WAAW,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,EAAE,OAAO;AACpI,YAAM,yBAAyB,UAAQ,cAAc,IAAI,KAAK,MAAM,MAAM,iBAAiB,MAAM;AACjG,YAAM,6BAA6B;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AACA,YAAM,mBAAmB,UAAQ;AAC/B,cAAM,oBAAoB,6BAA6B,IAAI;AAC3D,eAAO,qBAAqB,WAAW,4BAA4B,KAAK,IAAI,CAAC,KAAK,uBAAuB,IAAI;AAAA,MAC/G;AAEA,YAAM,QAAQ,aAAW,aAAa,SAAS,gBAAgB;AAC/D,YAAM,SAAS,aAAW,cAAc,SAAS,gBAAgB;AACjE,YAAM,gBAAgB,CAAC,OAAO,cAAc;AAC1C,cAAM,UAAU,aAAW;AACzB,gBAAMS,YAAW,WAAW,OAAO;AACnC,mBAAS,IAAIA,UAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,kBAAMS,SAAQT,UAAS;AACvB,gBAAI,UAAUS,MAAK,GAAG;AACpB,qBAAO,SAAS,KAAKA,MAAK;AAAA,YAC5B;AACA,kBAAM,MAAM,QAAQA,MAAK;AACzB,gBAAI,IAAI,OAAO,GAAG;AAChB,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO,SAAS,KAAK;AAAA,QACvB;AACA,eAAO,QAAQ,KAAK;AAAA,MACtB;AAEA,YAAM,yBAAyB;AAAA,QAC7B,OAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa,SAAO,MAAM;AAC9B,cAAM,KAAK,aAAa,QAAQ,MAAM,IAAI,GAAG;AAC7C,iBAAS,IAAI,aAAa,QAAQ,MAAM,IAAI,GAAG,CAAC;AAChD,eAAO;AAAA,MACT;AACA,YAAM,YAAY,SAAO,MAAM;AAC7B,eAAO,aAAa,QAAQ,OAAO,IAAI,GAAG;AAAA,MAC5C;AACA,YAAM,iBAAiB,SAAO,MAAM;AAClC,eAAO,aAAa,QAAQ,YAAY,IAAI,GAAG;AAAA,MACjD;AACA,YAAM,cAAc,SAAO,MAAM;AAC/B,eAAO,aAAa,QAAQ,MAAM,IAAI,GAAG;AAAA,MAC3C;AACA,YAAM,YAAY,CAACK,OAAM,KAAK,UAAU;AACtC,cAAM,UAAU,OAAOA,OAAM,GAAG;AAChC,eAAO,OAAO,CAAC,GAAG,MAAM;AACtB,cAAI,MAAM,MAAM;AACd,qBAAS,SAAS,CAAC;AAAA,UACrB,OAAO;AACL,kBAAM,SAAS,GAAG,CAAC;AAAA,UACrB;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,eAAe,CAAAA,UAAQ;AAC3B,eAAOA;AAAA,MACT;AACA,YAAM,eAAe,CAAC,SAAS,SAAS,YAAY;AAClD,cAAM,UAAU,MAAM,OAAO;AAC7B,eAAO,QAAQ,IAAI,eAAa;AAC9B,gBAAM,iBAAiB,QAAQ,KAAK,GAAG;AACvC,gBAAM0B,WAAU,YAAY,WAAW,gBAAgB,aAAW;AAChE,mBAAO,KAAK,SAAS,OAAO;AAAA,UAC9B,CAAC;AACD,iBAAO,MAAMA,UAAS,CAACC,OAAM1C,YAAW;AACtC,kBAAM,eAAe,QAAQA,OAAM;AACnC,qBAAS0C,OAAM,YAAY;AAC3B,mBAAO;AAAA,UACT,GAAG,OAAO;AAAA,QACZ,CAAC,EAAE,MAAM,OAAO;AAAA,MAClB;AACA,YAAM,6BAA6B,CAAC,UAAUC,WAAU;AACtD,eAAO,wBAAwB,CAAC,iBAAiB,kBAAkB,OAAO,UAAU,aAAa,EAAE,OAAO,eAAa,WAAW,iBAAiB,SAAS,CAAC,EAAE,KAAK,eAAa,MAAMA,QAAO,eAAe,SAAS,CAAC,CAAC;AAAA,MAC1N;AACA,YAAM,iBAAiB,CAACC,SAAQ,KAAK,mBAAmB;AACtD,cAAM,WAAW,CAAC,MAAMD,WAAU;AAChC,iBAAO,KAAK,SAASA,MAAK;AAC1B,mBAASA,QAAO,QAAQ;AACxB,cAAI,KAAK,YAAY,GAAG;AACtB,qBAASA,QAAO,OAAO;AAAA,UACzB;AAAA,QACF;AACA,cAAM,UAAU,UAAQ;AACtB,gBAAM,KAAK,aAAa,QAAQ,KAAK,KAAK,OAAO,GAAG,IAAI,GAAG;AAC3D,gBAAM,UAAU,eAAe,MAAM;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AACD,gBAAM,WAAW,QAAQ,SAAS,IAAI,aAAa,KAAK,SAAS,IAAI,OAAO,IAAI;AAChF,mBAAS,UAAU,aAAa,QAAQ,IAAI,CAAC;AAC7C,mBAAS,MAAM,EAAE;AACjB,qCAA2B,KAAK,SAAS,EAAE;AAC3C,UAAAC,QAAO,KAAK,SAAS,EAAE;AACvB,iBAAO;AAAA,QACT;AACA,cAAM,SAAS,UAAQ;AACrB,gBAAMd,OAAM,aAAa,QAAQ,KAAK,KAAK,OAAO,GAAG,IAAI,GAAG;AAC5D,mBAAS,MAAMA,IAAG;AAClB,UAAAc,QAAO,KAAK,SAASd,IAAG;AACxB,iBAAOA;AAAA,QACT;AACA,eAAO;AAAA,UACL,KAAK;AAAA,UACL,UAAU,eAAe,GAAG;AAAA,UAC5B,KAAK,YAAY,GAAG;AAAA,UACpB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,QAAQ,UAAU,GAAG;AAAA,UACrB,KAAK,WAAW,GAAG;AAAA,QACrB;AAAA,MACF;AACA,YAAM,UAAU,SAAO;AACrB,eAAO;AAAA,UACL,KAAK,UAAU,GAAG;AAAA,UAClB,UAAU,eAAe,GAAG;AAAA,UAC5B,KAAK,YAAY,GAAG;AAAA,UACpB,MAAM,WAAW,GAAG;AAAA,UACpB,SAAS;AAAA,UACT,QAAQ,UAAU,GAAG;AAAA,UACrB,KAAK,WAAW,GAAG;AAAA,QACrB;AAAA,MACF;AAEA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,YAAY;AAChB,eAAO,WAAW,aAAa,QAAQ,GAAG,CAAC;AAAA,MAC7C;AACA,YAAM,UAAU,WAAS,MAAM,OAAO,aAAa,OAAO;AAE1D,YAAM,SAAS,CAAAjC,UAAQ,YAAU,OAAO,QAAQ,IAAIA,KAAI;AACxD,YAAM,eAAe;AACrB,YAAM,sBAAsB,YAAU;AACpC,YAAI;AACJ,cAAM,MAAM,OAAO;AACnB,cAAM,eAAe,KAAK,IAAI,UAAU,OAAO,UAAU,SAAS,GAAG,IAAI,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,OAAO,QAAQ;AACnI,eAAO,SAAS,aAAa,QAAQ,WAAW,CAAC,IAAI;AAAA,MACvD;AACA,YAAM,8BAA8B,CAAC,QAAQ,kBAAkB;AAC7D,YAAI,wBAAwB,MAAM,KAAK,CAAC,mBAAmB,MAAM,GAAG;AAClE,iBAAO;AAAA,QACT,WAAW,oBAAoB,MAAM,GAAG;AACtC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO,oBAAoB,MAAM;AAAA,UACnC;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,kCAAkC,CAAC,QAAQ,sBAAsB;AACrE,YAAI,wBAAwB,MAAM,KAAK,mBAAmB,MAAM,GAAG;AACjE,iBAAO;AAAA,QACT,WAAW,oBAAoB,MAAM,GAAG;AACtC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO,oBAAoB,MAAM;AAAA,UACnC;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW,YAAU;AACzB,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,wBAAwB,EAAE,WAAW,WAAW,CAAC;AAChE,uBAAe,uBAAuB;AAAA,UACpC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,qBAAqB;AAAA,UAClC,WAAW,CAAAL,WAAS;AAClB,kBAAM,QAAQ,WAAW;AAAA,cACvB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF,GAAGA,MAAK;AACR,mBAAO,QAAQ;AAAA,cACb,OAAAA;AAAA,cACA;AAAA,YACF,IAAI;AAAA,cACF,OAAO;AAAA,cACP,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,qBAAqB;AAAA,UAClC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,4BAA4B;AAAA,UACzC,WAAW;AAAA,UACX,SAAS,EAAE,QAAQ,IAAI;AAAA,QACzB,CAAC;AACD,uBAAe,wBAAwB;AAAA,UACrC,WAAW;AAAA,UACX,SAAS,EAAE,mBAAmB,WAAW;AAAA,QAC3C,CAAC;AACD,uBAAe,yBAAyB;AAAA,UACtC,WAAW,CAAAA,WAAS;AAClB,kBAAM,QAAQ,WAAW;AAAA,cACvB;AAAA,cACA;AAAA,YACF,GAAGA,MAAK;AACR,mBAAO,QAAQ;AAAA,cACb,OAAAA;AAAA,cACA;AAAA,YACF,IAAI;AAAA,cACF,OAAO;AAAA,cACP,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,qBAAqB;AAAA,UAClC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,sBAAsB;AAAA,UACnC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,gCAAgC;AAAA,UAC7C,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,wBAAwB,YAAU;AACtC,eAAO,SAAS,KAAK,OAAO,QAAQ,IAAI,sBAAsB,CAAC;AAAA,MACjE;AACA,YAAM,yBAAyB,YAAU;AACvC,cAAM,iBAAiB,OAAO,QAAQ,IAAI,iBAAiB;AAC3D,eAAO,WAAW,eAAe,MAAM,GAAG,GAAG,OAAO;AAAA,MACtD;AACA,YAAM,qBAAqB,OAAO,mBAAmB;AACrD,YAAM,kCAAkC,OAAO,uBAAuB;AACtE,YAAM,gCAAgC,YAAU,gCAAgC,MAAM,MAAM;AAC5F,YAAM,8BAA8B,YAAU,gCAAgC,MAAM,MAAM;AAC1F,YAAM,qBAAqB,OAAO,mBAAmB;AACrD,YAAM,2BAA2B,YAAU,mBAAmB,MAAM,MAAM;AAC1E,YAAM,sBAAsB,YAAU,mBAAmB,MAAM,MAAM;AACrE,YAAM,0BAA0B,YAAU,mBAAmB,MAAM,MAAM;AACzE,YAAM,qBAAqB,OAAO,mBAAmB;AACrD,YAAM,qBAAqB,OAAO,oBAAoB;AACtD,YAAM,4BAA4B,OAAO,8BAA8B;AACvE,YAAM,4BAA4B,YAAU;AAC1C,cAAM,UAAU,OAAO;AACvB,cAAM,oBAAoB,QAAQ,IAAI,0BAA0B;AAChE,eAAO,QAAQ,MAAM,0BAA0B,IAAI,oBAAoB,gCAAgC,QAAQ,iBAAiB;AAAA,MAClI;AACA,YAAM,wBAAwB,YAAU;AACtC,cAAM,UAAU,OAAO;AACvB,cAAM,gBAAgB,QAAQ,IAAI,sBAAsB;AACxD,eAAO,QAAQ,MAAM,sBAAsB,IAAI,gBAAgB,4BAA4B,QAAQ,aAAa;AAAA,MAClH;AACA,YAAM,sBAAsB,OAAO,qBAAqB;AAExD,YAAM,UAAU,YAAU,UAAU,QAAQ,mBAAmB;AAC/D,YAAM,eAAe,CAAC,SAAS,iBAAiB,UAAU;AACxD,YAAI,OAAO,OAAO,GAAG;AACnB,iBAAO,QAAQ,IAAI;AAAA,QACrB,OAAO;AACL,iBAAO,QAAQ,OAAO,EAAE,KAAK,SAAS,cAAc,GAAG,cAAY,OAAO,QAAQ,MAAM,MAAM;AAAA,QAChG;AAAA,MACF;AACA,YAAM,SAAS,aAAW,QAAQ,IAAI;AAEtC,YAAM,UAAU,YAAU,aAAa,QAAQ,OAAO,QAAQ,CAAC;AAC/D,YAAM,YAAY,YAAU,aAAW,KAAK,SAAS,QAAQ,MAAM,CAAC;AACpE,YAAM,kBAAkB,CAAAyB,WAAS;AAC/B,iBAASA,QAAO,gBAAgB;AAChC,cAAM,uBAAuB,aAAW,SAAS,SAAS,gBAAgB;AAC1E,eAAO,QAAQA,MAAK,GAAG,oBAAoB;AAC3C,eAAO,UAAUA,MAAK,GAAG,oBAAoB;AAC7C,eAAO,OAAOA,MAAK,GAAG,oBAAoB;AAAA,MAC5C;AACA,YAAM,oBAAoB,YAAU,aAAa,QAAQ,OAAO,UAAU,SAAS,CAAC;AACpF,YAAM,gBAAgB,SAAO,IAAI,sBAAsB,EAAE;AACzD,YAAM,iBAAiB,SAAO,IAAI,sBAAsB,EAAE;AAC1D,YAAM,cAAc,CAAC,QAAQ,QAAQ;AACnC,cAAM,MAAM,OAAO,IAAI,SAAS,KAAK,OAAO,KAAK,OAAO,IAAI,UAAU,KAAK,OAAO;AAClF,eAAO,SAAS,KAAK,GAAG,EAAE,OAAO,UAAU;AAAA,MAC7C;AACA,YAAM,iBAAiB,CAAAzB,WAAS,mBAAmB,KAAKA,MAAK;AAC7D,YAAM,UAAU,CAAAA,WAAS,oBAAoB,KAAKA,MAAK;AACvD,YAAM,wBAAwB,CAAAuB,UAAQ,UAAUA,OAAM,MAAM,OAAO,CAAC,EAAE,OAAO,YAAY;AAEzF,YAAM,cAAc,CAAC8B,SAAQhB,YAAW;AACtC,cAAM,WAAWA,QAAO;AACxB,cAAM,YAAYA,QAAO,SAASA,QAAO,UAAU;AACnD,cAAM,UAAUA,QAAO;AACvB,cAAM,aAAaA,QAAO,MAAMA,QAAO,UAAU;AACjD,eAAO,YAAYgB,QAAO,aAAa,aAAaA,QAAO,aAAa,WAAWA,QAAO,aAAa,cAAcA,QAAO;AAAA,MAC9H;AACA,YAAM,WAAW,CAACA,SAAQhB,YAAW;AACnC,eAAOA,QAAO,UAAUgB,QAAO,YAAYhB,QAAO,SAASA,QAAO,UAAU,KAAKgB,QAAO,aAAahB,QAAO,OAAOgB,QAAO,YAAYhB,QAAO,MAAMA,QAAO,UAAU,KAAKgB,QAAO;AAAA,MAClL;AACA,YAAM,gBAAgB,CAAC,WAAWA,YAAW;AAC3C,YAAI,SAAS;AACb,cAAM,iBAAiB,MAAM,UAAUA,OAAM;AAC7C,iBAAS,IAAIA,QAAO,UAAU,KAAKA,QAAO,WAAW,KAAK;AACxD,mBAAS,IAAIA,QAAO,UAAU,KAAKA,QAAO,WAAW,KAAK;AACxD,qBAAS,UAAU,UAAU,MAAM,WAAW,GAAG,CAAC,EAAE,OAAO,cAAc;AAAA,UAC3E;AAAA,QACF;AACA,eAAO,SAAS,SAAS,KAAKA,OAAM,IAAI,SAAS,KAAK;AAAA,MACxD;AAEA,YAAM,YAAY,CAAC,SAAS,YAAY;AACtC,eAAO,OAAO,KAAK,IAAI,QAAQ,KAAK,QAAQ,GAAG,GAAG,KAAK,IAAI,QAAQ,QAAQ,QAAQ,MAAM,GAAG,KAAK,IAAI,QAAQ,MAAM,QAAQ,UAAU,GAAG,QAAQ,MAAM,QAAQ,UAAU,CAAC,GAAG,KAAK,IAAI,QAAQ,SAAS,QAAQ,UAAU,GAAG,QAAQ,SAAS,QAAQ,UAAU,CAAC,CAAC;AAAA,MAClQ;AACA,YAAM,YAAY,CAAC,WAAW,WAAW,eAAe;AACtD,cAAM,cAAc,UAAU,SAAS,WAAW,WAAW,IAAI;AACjE,cAAM,eAAe,UAAU,SAAS,WAAW,YAAY,IAAI;AACnE,eAAO,YAAY,KAAK,QAAM;AAC5B,iBAAO,aAAa,IAAI,QAAM;AAC5B,mBAAO,UAAU,IAAI,EAAE;AAAA,UACzB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,WAAW,WAAW,eAAe;AACrD,eAAO,UAAU,WAAW,WAAW,UAAU,EAAE,KAAK,CAAAA,YAAU;AAChE,iBAAO,cAAc,WAAWA,OAAM;AAAA,QACxC,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,WAAW9B,OAAMV,MAAK,WAAW;AACjD,eAAO,UAAU,SAAS,WAAWU,OAAM,IAAI,EAAE,KAAK,CAAAc,YAAU;AAC9D,gBAAM,WAAWxB,OAAM,IAAIwB,QAAO,MAAMA,QAAO,UAAU,IAAIA,QAAO;AACpE,gBAAM,WAAW,SAAS,IAAIA,QAAO,SAASA,QAAO,UAAU,IAAIA,QAAO;AAC1E,gBAAM,OAAO,UAAU,MAAM,WAAW,WAAWxB,MAAK,WAAW,MAAM;AACzE,iBAAO,KAAK,IAAI,OAAK;AACnB,mBAAO,EAAE;AAAA,UACX,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,eAAe,CAAC,WAAW,OAAO,WAAW;AACjD,eAAO,UAAU,WAAW,OAAO,MAAM,EAAE,IAAI,CAAAwC,YAAU;AACvD,gBAAM,SAAS,UAAU,YAAY,WAAW,MAAM,aAAaA,OAAM,CAAC;AAC1E,iBAAO,MAAM,QAAQ,CAAAhB,YAAU;AAC7B,mBAAOA,QAAO;AAAA,UAChB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,WAAW,cAAc;AAC3C,cAAM,gBAAgB,CAAC,IAAI,OAAO;AAChC,iBAAO,WAAW,IAAI,EAAE;AAAA,QAC1B;AACA,eAAO,UAAU,SAAS,WAAW,WAAW,aAAa,EAAE,IAAI,CAAAA,YAAU;AAC3E,iBAAOA,QAAO;AAAA,QAChB,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,CAACd,OAAM,UAAU,gBAAgB;AAC9C,eAAO,MAAMA,KAAI,EAAE,KAAK,CAAAE,WAAS;AAC/B,gBAAM,YAAY,aAAaA,MAAK;AACpC,iBAAO,SAAS,WAAWF,OAAM,UAAU,WAAW;AAAA,QACxD,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAACE,QAAO6B,QAAOJ,UAAS;AACzC,cAAM,YAAY,aAAazB,MAAK;AACpC,eAAO,aAAa,WAAW6B,QAAOJ,KAAI;AAAA,MAC5C;AACA,YAAM,mBAAmB,CAACzB,QAAO6B,QAAO,YAAYJ,OAAM,cAAc;AACtE,cAAM,YAAY,aAAazB,MAAK;AACpC,cAAM,eAAe,KAAKA,QAAO,UAAU,IAAI,SAAS,KAAK6B,MAAK,IAAI,WAAW,WAAWA,MAAK;AACjG,cAAM,cAAc,KAAK7B,QAAO,SAAS,IAAI,SAAS,KAAKyB,KAAI,IAAI,WAAW,WAAWA,KAAI;AAC7F,eAAO,aAAa,KAAK,eAAa,YAAY,KAAK,cAAY,aAAa,WAAW,WAAW,QAAQ,CAAC,CAAC;AAAA,MAClH;AACA,YAAM,SAAS,CAACzB,QAAO6B,QAAOJ,UAAS;AACrC,cAAM,YAAY,aAAazB,MAAK;AACpC,eAAO,SAAS,WAAW6B,QAAOJ,KAAI;AAAA,MACxC;AACA,YAAM,eAAe,UAAU;AAE/B,UAAI,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,cAAc,MAAM;AACtB,cAAMC,SAAQ,aAAW;AACvB,iBAAO,aAAa,QAAQ,QAAQ,IAAI,UAAU,KAAK,CAAC;AAAA,QAC1D;AACA,cAAMI,YAAW,aAAW,gBAAgB,OAAO,EAAE;AACrD,cAAM,aAAa,aAAW;AAC5B,cAAI,CAAC,UAAU,OAAO,GAAG;AACvB,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,OAAO,MAAM,QAAQ;AAC5B,mBAAO;AAAA,UACT;AACA,iBAAO,WAAW,eAAe,KAAK,OAAO,CAAC;AAAA,QAChD;AACA,cAAMC,cAAa,aAAW;AAC5B,cAAI,CAAC,UAAU,OAAO,GAAG;AACvB,mBAAO;AAAA,UACT;AACA,iBAAO,WAAW;AAAA,YAChB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,GAAG,KAAK,OAAO,CAAC;AAAA,QAClB;AACA,cAAM,gBAAgB,aAAW,UAAU,OAAO,KAAK,MAAM,SAAS,iBAAiB,MAAM;AAC7F,cAAM,kBAAkB,CAAC,SAAS,UAAU;AAC1C,iBAAO,QAAQ,IAAI,wBAAwB,MAAM,GAAG;AAAA,QACtD;AACA,cAAM,mBAAmB,CAAC,QAAQ,gBAAgB;AAChD,gBAAM,KAAK,QAAQ,MAAM;AACzB,mBAAS,aAAa,EAAE;AAAA,QAC1B;AACA,cAAM,YAAY,aAAW;AAC3B,gBAAM,MAAM,KAAK,OAAO;AACxB,iBAAO,WAAW;AAAA,YAChB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,GAAG,GAAG;AAAA,QACR;AACA,cAAM,cAAc,aAAW,UAAU,OAAO,IAAI,OAAO,SAAS,MAAM,IAAI,SAAS,KAAK;AAC5F,eAAO;AAAA,UACL,IAAI,SAAS;AAAA,YACX,UAAU;AAAA,YACV,SAAS;AAAA,YACT,WAAW;AAAA,YACX,KAAK;AAAA,UACP,CAAC;AAAA,UACD,MAAM,SAAS;AAAA,YACb,UAAU;AAAA,YACV,WAAW;AAAA,UACb,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,QAAQ;AAAA,UACV,CAAC;AAAA,UACD,OAAO,SAAS;AAAA,YACd,KAAK;AAAA,YACL,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,WAAW;AAAA,YACX;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf;AAAA,YACA,QAAQ;AAAA,UACV,CAAC;AAAA,UACD,QAAQ,SAAS;AAAA,YACf,IAAI,aAAa;AAAA,YACjB,OAAAL;AAAA,YACA,MAAM,aAAa;AAAA,UACrB,CAAC;AAAA,UACD,OAAO,SAAS;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,UAAU,SAAS;AAAA,YACjB,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA,UAAAI;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS;AAAA,YACT,SAAS;AAAA,YACT;AAAA,YACA,YAAAC;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,IAAI;AAAA,UACJ,IAAI;AAAA,QACN;AAAA,MACF;AAEA,YAAM,MAAM,CAACC,WAAU,MAAM,UAAU,MAAM;AAC3C,cAAMC,QAAO,SAAS;AACtB,cAAM,OAAO,SAAS,MAAM,CAAC;AAC7B,eAAO,EAAED,WAAU,MAAMC,OAAM,IAAI;AAAA,MACrC;AACA,YAAM,SAAS,CAACD,WAAU,MAAM,aAAa;AAC3C,eAAO,SAAS,SAAS,IAAI,IAAIA,WAAU,MAAM,UAAU,SAAS,IAAI,SAAS,KAAK;AAAA,MACxF;AACA,YAAM,YAAY,CAACA,WAAU,MAAMC,OAAM,SAAS;AAChD,cAAM,QAAQ,KAAKD,WAAUC,KAAI;AACjC,eAAO,MAAM,MAAM,CAAC,GAAG,MAAM;AAC3B,gBAAM,UAAU,KAAKD,WAAU,CAAC;AAChC,iBAAO,cAAcA,WAAU,GAAG,OAAO;AAAA,QAC3C,GAAG,KAAK;AAAA,MACV;AACA,YAAM,gBAAgB,CAACA,WAAU,OAAO,QAAQ;AAC9C,eAAO,MAAM,KAAK,OAAK;AACrB,iBAAO,IAAI,OAAO,MAAMA,UAAS,IAAI,CAAC,CAAC;AAAA,QACzC,CAAC;AAAA,MACH;AAEA,YAAM,KAAK,CAACA,WAAU,SAAS;AAC7B,eAAO,MAAMA,UAAS,IAAI,IAAI;AAAA,MAChC;AACA,YAAM,cAAc,CAACA,WAAU,OAAO,KAAK,SAAS,UAAU;AAC5D,cAAM,MAAM,CAAC,KAAK,EAAE,OAAOA,UAAS,GAAG,EAAE,IAAI,KAAK,CAAC;AACnD,cAAM,MAAM,CAAC,GAAG,EAAE,OAAOA,UAAS,GAAG,EAAE,IAAI,GAAG,CAAC;AAC/C,cAAME,SAAQ,CAAAC,UAAQ;AACpB,gBAAM,QAAQ,UAAUA,OAAM,MAAM;AACpC,iBAAO,MAAM,KAAK,MAAM;AACtB,mBAAOA;AAAA,UACT,GAAG,SAAO;AACR,mBAAOA,MAAK,MAAM,GAAG,MAAM,CAAC;AAAA,UAC9B,CAAC;AAAA,QACH;AACA,cAAM,UAAUD,OAAM,GAAG;AACzB,cAAM,UAAUA,OAAM,GAAG;AACzB,cAAM,SAAS,OAAO,SAAS,OAAK;AAClC,iBAAO,OAAO,SAAS,GAAGF,WAAU,CAAC,CAAC;AAAA,QACxC,CAAC;AACD,eAAO;AAAA,UACL,WAAW;AAAA,UACX,YAAY;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AACpB,YAAM,cAAc;AAEpB,YAAM,aAAa,YAAY;AAC/B,YAAM,YAAY,CAAC,MAAM,aAAa;AACpC,eAAO,YAAY,YAAY,CAAC,WAAW,YAAY;AACrD,iBAAO,KAAK,OAAO;AAAA,QACrB,GAAG,QAAQ;AAAA,MACb;AACA,YAAM,YAAY,CAAC,OAAO,QAAQ,WAAW;AAC3C,eAAO,YAAY,YAAY,OAAO,QAAQ,MAAM;AAAA,MACtD;AAEA,YAAM,cAAc,eAAa;AAC/B,eAAO,WAAW,WAAW,OAAO;AAAA,MACtC;AACA,YAAM,WAAW,CAAC,OAAO,QAAQ,WAAW;AAC1C,cAAMI,aAAY,eAAa;AAC7B,iBAAO,aAAW;AAChB,mBAAO,WAAW,UAAa,OAAO,OAAO,KAAK,KAAK,SAAS,SAAS;AAAA,UAC3E;AAAA,QACF;AACA,YAAI,KAAK,OAAO,MAAM,GAAG;AACvB,iBAAO,SAAS,KAAK;AAAA,YACnB,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC;AAAA,YAC5B;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,YAAY,KAAK,EAAE,KAAK,gBAAc;AAC3C,mBAAO,YAAY,MAAM,EAAE,KAAK,iBAAe;AAC7C,kBAAI,KAAK,YAAY,WAAW,GAAG;AACjC,uBAAO,SAAS,KAAK;AAAA,kBACnB,OAAO,WAAW,YAAY,OAAO,MAAM;AAAA,kBAC3C;AAAA,kBACA;AAAA,gBACF,CAAC;AAAA,cACH,WAAW,WAAW,YAAY,WAAW,GAAG;AAC9C,sBAAM,gBAAgB,YAAY,QAAQ,SAASA,WAAU,UAAU,CAAC;AACxE,sBAAM,aAAa,cAAc,SAAS,IAAI,cAAc,cAAc,SAAS,KAAK;AACxF,uBAAO,SAAS,KAAK;AAAA,kBACnB,OAAO,iBAAiB,YAAY,OAAO,YAAY,QAAQ,WAAW;AAAA,kBAC1E;AAAA,kBACA,QAAQ;AAAA,gBACV,CAAC;AAAA,cACH,WAAW,WAAW,aAAa,UAAU,GAAG;AAC9C,sBAAM,gBAAgB,YAAY,OAAO,SAASA,WAAU,WAAW,CAAC;AACxE,sBAAM,YAAY,cAAc,SAAS,IAAI,cAAc,cAAc,SAAS,KAAK;AACvF,uBAAO,SAAS,KAAK;AAAA,kBACnB,OAAO,iBAAiB,aAAa,OAAO,YAAY,QAAQ,WAAW;AAAA,kBAC3E;AAAA,kBACA,QAAQ;AAAA,gBACV,CAAC;AAAA,cACH,OAAO;AACL,uBAAO,UAAU,OAAO,MAAM,EAAE,OAAO,KAAK,SAAO;AACjD,yBAAO,UAAU,KAAK,SAAS,MAAM,EAAE,KAAK,cAAY;AACtD,0BAAM,sBAAsB,YAAY,QAAQ,SAASA,WAAU,QAAQ,CAAC;AAC5E,0BAAM,aAAa,oBAAoB,SAAS,IAAI,oBAAoB,oBAAoB,SAAS,KAAK;AAC1G,0BAAM,qBAAqB,YAAY,OAAO,SAASA,WAAU,QAAQ,CAAC;AAC1E,0BAAM,YAAY,mBAAmB,SAAS,IAAI,mBAAmB,mBAAmB,SAAS,KAAK;AACtG,2BAAO,SAAS,KAAK;AAAA,sBACnB,OAAO,iBAAiB,UAAU,OAAO,YAAY,QAAQ,WAAW;AAAA,sBACxE,OAAO;AAAA,sBACP,QAAQ;AAAA,oBACV,CAAC;AAAA,kBACH,CAAC;AAAA,gBACH,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,aAAa,CAAC,WAAW,aAAa;AAC1C,cAAM,OAAO,YAAY,WAAW,QAAQ;AAC5C,eAAO,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK;AAAA,MAC/D;AACA,YAAM,UAAU,CAAC,OAAO,yBAAyB;AAC/C,eAAO,OAAO,OAAO,SAAO;AAC1B,iBAAO,KAAK,KAAK,oBAAoB;AAAA,QACvC,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,WAAW,uBAAuB,yBAAyB;AAC3E,eAAO,WAAW,WAAW,qBAAqB,EAAE,KAAK,CAAAP,WAAS;AAChE,iBAAO,WAAW,WAAW,oBAAoB,EAAE,KAAK,CAAAJ,UAAQ;AAC9D,mBAAO,UAAU,aAAa;AAAA,cAC5BI;AAAA,cACAJ;AAAA,YACF,CAAC,EAAE,IAAI,CAAAzB,WAAS;AACd,qBAAO;AAAA,gBACL,OAAA6B;AAAA,gBACA,MAAAJ;AAAA,gBACA,OAAAzB;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,QAAQ,0BAA0B;AAClD,eAAO,WAAW,QAAQ,OAAO,EAAE,KAAK,CAAAA,WAAS;AAC/C,iBAAO,WAAWA,QAAO,qBAAqB,EAAE,KAAK,WAAS;AAC5D,mBAAO,SAAS,OAAO,MAAM,EAAE,KAAK,gBAAc;AAChD,qBAAO,WAAW,MAAM,IAAI,WAAS;AACnC,uBAAO;AAAA,kBACL;AAAA,kBACA,OAAO,WAAW;AAAA,kBAClB,QAAQ,WAAW;AAAA,gBACrB;AAAA,cACF,CAAC;AAAA,YACH,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,iBAAiB,CAAC,OAAO,UAAU,aAAa,uBAAuB,yBAAyB;AACpG,eAAO,QAAQ,OAAO,oBAAoB,EAAE,KAAK,CAAAyB,UAAQ;AACvD,iBAAO,OAAOA,OAAM,UAAU,WAAW,EAAE,KAAK,YAAU;AACxD,mBAAO,SAAS,QAAQ,qBAAqB;AAAA,UAC/C,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,WAAW,aAAa;AACxC,eAAO,WAAW,WAAW,QAAQ;AAAA,MACvC;AACA,YAAM,cAAc,CAAC,WAAW,uBAAuB,yBAAyB;AAC9E,eAAO,SAAS,WAAW,uBAAuB,oBAAoB,EAAE,KAAK,WAAS;AACpF,gBAAM,SAAS,CAAAjC,cAAY;AACzB,mBAAO,KAAK,WAAWA,SAAQ;AAAA,UACjC;AACA,gBAAM,kBAAkB;AACxB,gBAAM,gBAAgB,WAAW,MAAM,OAAO,iBAAiB,MAAM;AACrE,gBAAM,eAAe,WAAW,MAAM,MAAM,iBAAiB,MAAM;AACnE,iBAAO,cAAc,KAAK,QAAM;AAC9B,mBAAO,aAAa,KAAK,QAAM;AAC7B,qBAAO,KAAK,IAAI,EAAE,IAAI,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,IAAI,IAAI,SAAS,KAAK;AAAA,YACrF,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,YAAM,YAAY;AAClB,YAAM,aAAa,mBAAiB;AAClC,cAAM,UAAU,CAAC,MAAMlB,UAAS,OAAO,MAAMA,KAAI,EAAE,OAAO,UAAQ,SAAS,MAAM,EAAE,IAAI,CAAC;AACxF,cAAM,kBAAkB,UAAQ,QAAQ,MAAM,SAAS,KAAK,QAAQ,MAAM,SAAS;AACnF,eAAO,cAAc,SAAS,KAAK,OAAO,eAAe,eAAe,IAAI,SAAS,KAAK,aAAa,IAAI,SAAS,KAAK;AAAA,MAC3H;AACA,YAAM,WAAW,CAAC0B,QAAO,eAAeqC,cAAa;AACnD,YAAI,cAAc,UAAU,GAAG;AAC7B,iBAAO,SAAS,KAAK;AAAA,QACvB,OAAO;AACL,iBAAO,YAAYrC,QAAOqC,UAAS,uBAAuBA,UAAS,oBAAoB,EAAE,IAAI,CAAAT,aAAW;AAAA,YACtG,QAAAA;AAAA,YACA,OAAO;AAAA,UACT,EAAE;AAAA,QACJ;AAAA,MACF;AAEA,YAAM,cAAc;AACpB,YAAM,sBAAsB,QAAQ,cAAc,UAAU,cAAc;AAC1E,YAAM,uBAAuB,MAAM,cAAc;AACjD,YAAM,mBAAmB;AACzB,YAAM,2BAA2B,QAAQ,mBAAmB,UAAU,mBAAmB;AACzF,YAAM,kBAAkB;AACxB,YAAM,0BAA0B,QAAQ,kBAAkB,UAAU,kBAAkB;AACtF,YAAM,oBAAoB;AAC1B,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,uBAAuB;AAAA,QACvB,cAAc;AAAA,QACd,sBAAsB;AAAA,MACxB;AAEA,YAAM,UAAU,CAAC,eAAe5B,QAAOF,WAAU;AAAA,QAC/C,SAASA;AAAA,QACT,UAAU,SAASE,QAAO,eAAe,QAAQ;AAAA,QACjD,YAAY,WAAW,aAAa;AAAA,QACpC,WAAW,UAAU,aAAa;AAAA,MACpC;AACA,YAAM,QAAQ,CAAC,SAAS,WAAW,gBAAgB;AAAA,QACjD;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,CAAC,eAAe,OAAO,WAAW,gBAAgB;AAAA,QAClE,WAAW,UAAU,aAAa;AAAA,QAClC;AAAA,QACA;AAAA,MACF;AAEA,YAAM,2BAA2B,aAAW,MAAM,OAAO,EAAE,KAAK,CAAAA,WAAS,SAASA,QAAO,SAAS,qBAAqB,CAAC,EAAE,KAAK,SAAS,OAAO,GAAG,CAAAX,WAASA,OAAM,EAAE;AACnK,YAAM,2BAA2B,cAAY,CAAC,UAAU,WAAW;AACjE,cAAM,WAAW,KAAK,QAAQ;AAC9B,cAAMS,QAAO,aAAa,SAAS,aAAa,aAAa,yBAAyB,QAAQ,IAAI;AAClG,eAAO,UAAUA,OAAM,UAAU,MAAM;AAAA,MACzC;AACA,YAAM,4BAA4B,yBAAyB,eAAe;AAC1E,YAAM,mBAAmB,yBAAyB,OAAO;AACzD,YAAM,wBAAwB,YAAU,QAAQ,OAAO,MAAM,MAAM,iBAAiB,CAAC;AACrF,YAAM,4BAA4B,YAAU,SAAS,sBAAsB,MAAM,GAAG,CAAAA,UAAQ,KAAKA,OAAM,SAAS,gBAAgB,CAAC;AAEjI,YAAM,kBAAkB,CAAAT,WAAS;AAC/B,eAAO,MAAMA,OAAM,EAAE,EAAE,IAAI,CAAAW,WAAS;AAClC,gBAAM,UAAU,UAAUA,QAAO,iBAAiB;AAClD,0BAAgB,OAAO;AACvB,iBAAO,CAAC,OAAO;AAAA,QACjB,CAAC;AAAA,MACH;AACA,YAAM,oBAAoB,CAAC,QAAQ,aAAa,MAAM,UAAU,SAAO,OAAO,UAAU,WAAW,UAAU,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE;AAClI,YAAM,iBAAiB,cAAY,MAAM,UAAU,aAAW,QAAQ,IAAI,SAAS,EAAE,KAAK,EAAE;AAC5F,YAAM,iBAAiB,CAAC,QAAQ,YAAY;AAC1C,eAAO,GAAG,oBAAoB,OAAK;AACjC,gBAAM,mBAAmB,CAAAX,WAAS;AAChC,cAAE,eAAe;AACjB,4BAAgBA,MAAK,EAAE,KAAK,cAAY;AACtC,gBAAE,UAAU,EAAE,WAAW,SAAS,eAAe,QAAQ,IAAI,kBAAkB,QAAQ,QAAQ;AAAA,YACjG,CAAC;AAAA,UACH;AACA,cAAI,EAAE,cAAc,MAAM;AACxB,kBAAMA,SAAQ,0BAA0B,MAAM;AAC9C,gBAAIA,OAAM,UAAU,GAAG;AACrB,+BAAiBA,MAAK;AAAA,YACxB;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO,GAAG,oBAAoB,OAAK;AACjC,cAAI,EAAE,cAAc,QAAQ,EAAE,UAAU,MAAM;AAC5C,kBAAM,gBAAgB,sBAAsB,MAAM;AAClD,iBAAK,aAAa,EAAE,KAAK,CAAAS,UAAQ;AAC/B,oBAAMA,KAAI,EAAE,KAAK,CAAAE,WAAS;AACxB,sBAAM,WAAW,SAAS,SAAS,EAAE,OAAO,GAAG,aAAW;AACxD,yBAAO,KAAK,OAAO,MAAM;AAAA,gBAC3B,CAAC;AACD,sBAAMsC,WAAU,MAAM,OAAO;AAC7B,oBAAI,0BAA0B,MAAM,KAAK,SAAS,WAAW,KAAKA,SAAQ,SAAS,EAAE,GAAG;AACtF,oBAAE,eAAe;AACjB,wBAAM,MAAM,aAAa,QAAQ,OAAO,OAAO,CAAC;AAChD,wBAAM,aAAa,QAAQ,GAAG;AAC9B,wBAAM,UAAU,MAAMxC,OAAM,SAAS,IAAI,UAAU;AACnD,0BAAQ,WAAWE,QAAO,OAAO,EAAE,KAAK,MAAM;AAC5C,2BAAO,MAAM;AAAA,kBACf,CAAC;AAAA,gBACH;AAAA,cACF,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,QAAQ,CAAC,SAAS,YAAY;AAAA,QAClC;AAAA,QACA;AAAA,MACF;AAEA,YAAM,SAAS,CAACgC,WAAU,SAAS,cAAc;AAC/C,YAAIA,UAAS,SAAS,EAAE,OAAO,OAAO,KAAKA,UAAS,SAAS,EAAE,QAAQ,OAAO,EAAE,KAAK,EAAE,WAAW,KAAKA,UAAS,SAAS,EAAE,UAAU,OAAO,GAAG;AAC7I,iBAAO,UAAU,OAAO,EAAE,KAAK,UAAQ;AACrC,mBAAO,OAAOA,WAAU,MAAM,SAAS,EAAE,QAAQ,MAAM;AACrD,qBAAO,SAAS,KAAK,IAAI;AAAA,YAC3B,CAAC;AAAA,UACH,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,QAAQ,CAACA,WAAU,YAAY;AACnC,YAAIA,UAAS,SAAS,EAAE,OAAO,OAAO,GAAG;AACvC,iBAAOA,UAAS,SAAS,EAAE,QAAQ,OAAO,EAAE;AAAA,QAC9C;AACA,cAAMhD,YAAWgD,UAAS,SAAS,EAAE,SAAS,OAAO;AACrD,eAAOhD,UAAS;AAAA,MAClB;AACA,YAAM,gBAAgB,CAACgD,WAAU,YAAY;AAC3C,cAAM,YAAY,OAAOA,WAAU,SAASA,UAAS,MAAM,EAAE,WAAW,EAAE,MAAM,OAAO;AACvF,YAAIA,UAAS,SAAS,EAAE,OAAO,SAAS,GAAG;AACzC,iBAAO,MAAM,WAAW,MAAMA,WAAU,SAAS,CAAC;AAAA,QACpD;AACA,cAAMhD,YAAWgD,UAAS,SAAS,EAAE,SAAS,SAAS;AACvD,eAAOhD,UAAS,SAAS,IAAI,cAAcgD,WAAUhD,UAASA,UAAS,SAAS,EAAE,IAAI,MAAM,WAAW,MAAMgD,WAAU,SAAS,CAAC;AAAA,MACnI;AAEA,YAAM,gBAAgB;AAEtB,YAAM,aAAa,YAAY;AAC/B,YAAM,cAAc,aAAW;AAC7B,eAAO,cAAc,YAAY,OAAO;AAAA,MAC1C;AAEA,YAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,YAAI,CAAC,WAAW,IAAI,GAAG;AACrB,gBAAMzB,SAAQ,gBAAgB,IAAI;AAClC,UAAAA,OAAM,KAAK,OAAK;AACd,kBAAM,WAAW,EAAE,QAAQ;AAC3B,4BAAgB,MAAM,UAAU,EAAE,IAAI;AACtC,4BAAgB,OAAO,UAAU,EAAE,IAAI;AAAA,UACzC,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,OAAO,WAAS,MAAM,OAAO,SAAS,CAAC,CAAC;AAC9C,YAAM,WAAW,CAAC,OAAO,YAAY,UAAU,SAAS,MAAM,EAAE,MAAM,MAAM,GAAG,UAAU,CAAC,EAAE,OAAO,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,QAAQ,CAAC,CAAC;AAC3I,YAAM,mBAAmB,eAAa,CAAC,OAAO,OAAO,OAAO,gBAAgB;AAC1E,YAAI,CAAC,UAAU,KAAK,GAAG;AACrB,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,UAAU,KAAK,IAAI,aAAa,MAAM,SAAS,KAAK,IAAI,KAAK,CAAC;AACpE,gBAAM,OAAO,KAAK,IAAI,UAAU,MAAM,MAAM;AAC5C,iBAAO,SAAS,IAAI,OAAO,CAAC;AAAA,QAC9B;AAAA,MACF;AACA,YAAM,qBAAqB,iBAAiB,WAAS,QAAQ,CAAC;AAC9D,YAAM,aAAa,iBAAiB,MAAM;AAC1C,YAAM,cAAc,MAAM;AACxB,cAAM,kBAAkB,CAAC,OAAO,OAAO,MAAM,OAAO,gBAAgB;AAClE,gBAAM,eAAe,mBAAmB,OAAO,OAAO,OAAO,WAAW;AACxE,iBAAO,SAAS,OAAO,OAAO,OAAO,GAAG;AAAA,YACtC;AAAA,YACA;AAAA,UACF,GAAG,IAAI;AAAA,QACT;AACA,cAAM,qBAAqB,CAAC,OAAO,OAAO,OAAO,gBAAgB;AAC/D,gBAAM,SAAS,MAAM,SAAS;AAC9B,gBAAM,UAAU,KAAK,IAAI,cAAc,MAAM,SAAS,SAAS,KAAK;AACpE,iBAAO,MAAM,OAAO,CAAC,MAAM,QAAQ;AACjC,kBAAM,UAAU,QAAQ,QAAQ,UAAU,OAAO;AACjD,mBAAO,UAAU;AAAA,UACnB,CAAC;AAAA,QACH;AACA,cAAM,qBAAqB,CAAC,OAAO,OAAO,MAAM,OAAO,aAAa,eAAe;AACjF,cAAI,YAAY;AACd,mBAAO,mBAAmB,OAAO,OAAO,OAAO,WAAW;AAAA,UAC5D,OAAO;AACL,mBAAO,gBAAgB,OAAO,OAAO,MAAM,OAAO,WAAW;AAAA,UAC/D;AAAA,QACF;AACA,cAAM,mBAAmB,CAAC,OAAO,OAAO,OAAO,MAAM,OAAO,aAAa,eAAe,mBAAmB,OAAO,OAAO,MAAM,OAAO,aAAa,UAAU;AAC7J,cAAMgC,eAAc,CAAC,SAAS,UAAU,QAAQ,KAAK;AACrD,cAAM,sBAAsB,CAAC,OAAO,OAAO,OAAO,OAAO,aAAa,eAAe;AACnF,cAAI,YAAY;AACd,mBAAO,mBAAmB,OAAO,OAAO,OAAO,WAAW;AAAA,UAC5D,OAAO;AACL,kBAAM,eAAe,mBAAmB,OAAO,OAAO,OAAO,WAAW;AACxE,mBAAO,KAAK,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC;AAAA,UAC1D;AAAA,QACF;AACA,cAAM,0BAA0B,CAAC,OAAO,YAAY,YAAY,eAAe;AAC7E,cAAI,YAAY;AACd,kBAAM,aAAa,aAAa;AAChC,kBAAM,QAAQ,aAAa;AAC3B,kBAAM,WAAW,MAAM,OAAO,UAAQ,OAAO,KAAK;AAClD,mBAAO;AAAA,cACL,OAAO,QAAQ,MAAM;AAAA,cACrB;AAAA,YACF;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,cACL,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,UACL,aAAAA;AAAA,UACA,iBAAiB;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB,MAAM;AAC1B,cAAM,qBAAqB,CAAC,OAAO,OAAO,MAAM,OAAO,gBAAgB;AACrE,gBAAM,MAAM,SAAS,IAAI,OAAO;AAChC,gBAAM,eAAe,WAAW,OAAO,KAAK,OAAO,WAAW;AAC9D,iBAAO,SAAS,OAAO,OAAO,OAAO,GAAG;AAAA,YACtC;AAAA,YACA,CAAC;AAAA,UACH,GAAG,IAAI;AAAA,QACT;AACA,cAAM,mBAAmB,CAAC,OAAO,OAAO,OAAO,MAAM,OAAO,gBAAgB,mBAAmB,OAAO,OAAO,MAAM,OAAO,WAAW;AACrI,cAAMA,eAAc,CAAC,SAAS,OAAO,iBAAiB;AACpD,cAAI,cAAc;AAChB,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF;AACA,cAAM,sBAAsB,CAAC,OAAO,OAAO,QAAQ,OAAO,cAAc,eAAe;AACrF,cAAI,YAAY;AACd,mBAAO,KAAK,KAAK;AAAA,UACnB,OAAO;AACL,kBAAM,OAAO,QAAQ,MAAM;AAC3B,mBAAO,MAAM,OAAO,SAAS,IAAI,CAAC;AAAA,UACpC;AAAA,QACF;AACA,cAAM,kBAAkB,CAAC,OAAO,OAAO,OAAO,aAAa,iBAAiB;AAC1E,cAAI,cAAc;AAChB,gBAAI,SAAS,GAAG;AACd,qBAAO;AAAA,YACT,OAAO;AACL,oBAAM,WAAW,MAAM,OAAO,CAAC,GAAG,MAAM,IAAI,IAAI,aAAa,CAAC;AAC9D,qBAAO,KAAK,IAAI,CAAC,UAAU,KAAK;AAAA,YAClC;AAAA,UACF,OAAO;AACL,mBAAO,mBAAmB,OAAO,OAAO,OAAO,WAAW;AAAA,UAC5D;AAAA,QACF;AACA,cAAM,0BAA0B,CAAC,OAAO,aAAa,aAAa,iBAAiB;AAAA,UACjF,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AACA,eAAO;AAAA,UACL,aAAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc,CAAAvC,WAAS;AAC3B,cAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,eAAO,UAAU;AAAA,MACnB;AAEA,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,gBAAgB,CAAAX,WAAS,OAAOA,QAAO,CAAAS,UAAQ,aAAaA,MAAK,OAAO,CAAC;AAC/E,YAAM,mBAAmB,CAAC,aAAa0C,mBAAkB;AACvD,YAAI,eAAeA,gBAAe;AAChC,iBAAO;AAAA,QACT,WAAW,aAAa;AACtB,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,aAAa,CAAApD,SAAO;AACxB,cAAM,cAAcA,KAAI,YAAY;AACpC,cAAMoD,iBAAgB,GAAG,mBAAmBpD,KAAI,KAAK,GAAG,IAAI;AAC5D,YAAIA,KAAI,YAAY,SAAS;AAC3B,iBAAO,EAAE,MAAM,SAAS;AAAA,QAC1B,WAAW,eAAeoD,gBAAe;AACvC,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS,iBAAiB,aAAaA,cAAa;AAAA,UACtD;AAAA,QACF,OAAO;AACL,iBAAO,EAAE,MAAM,OAAO;AAAA,QACxB;AAAA,MACF;AACA,YAAM,qBAAqB,CAAAnD,WAAS;AAClC,cAAM,cAAc,SAASA,QAAO,CAAAS,UAAQ,aAAaA,MAAK,OAAO,CAAC;AACtE,YAAI,YAAY,WAAW,GAAG;AAC5B,iBAAO,SAAS,KAAK,IAAI;AAAA,QAC3B,WAAW,YAAY,WAAWT,OAAM,QAAQ;AAC9C,iBAAO,SAAS,KAAK,IAAI;AAAA,QAC3B,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,oBAAoB,CAAAH,UAAQ;AAChC,cAAM,WAAW,MAAMA,OAAM,CAAAE,SAAO,WAAWA,IAAG,EAAE,IAAI;AACxD,cAAM,YAAY,WAAW,UAAU,QAAQ;AAC/C,cAAM,YAAY,WAAW,UAAU,QAAQ;AAC/C,YAAI,CAAC,aAAa,CAAC,WAAW;AAC5B,iBAAO,SAAS,KAAK,MAAM;AAAA,QAC7B,OAAO;AACL,gBAAM,UAAU,WAAW,UAAU,MAAM;AAC3C,cAAI,aAAa,CAAC,WAAW,CAAC,WAAW;AACvC,mBAAO,SAAS,KAAK,QAAQ;AAAA,UAC/B,WAAW,CAAC,aAAa,CAAC,WAAW,WAAW;AAC9C,mBAAO,SAAS,KAAK,QAAQ;AAAA,UAC/B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AACA,YAAM,yBAAyB,eAAa,QAAQ,UAAU,KAAK,CAAAA,SAAO;AACxE,cAAM,UAAU,WAAWA,IAAG;AAC9B,eAAO,QAAQ,SAAS,WAAW,SAAS,KAAK,QAAQ,OAAO,IAAI,SAAS,KAAK;AAAA,MACpF,CAAC;AAED,YAAM,gBAAgB,CAACU,OAAM,YAAY,iBAAiB,WAAW,aAAaA,MAAK,SAAS,UAAU,GAAG,MAAMA,MAAK,QAAQ;AAChI,YAAM,eAAe,CAACV,MAAKE,aAAYF,KAAI,YAAYE,WAAU,SAASF,KAAI,SAASA,KAAI,OAAOE,UAASF,KAAI,KAAK,IAAIA;AACxH,YAAM,UAAU,OAAO;AAAA,QACrB;AAAA,QACA,eAAe,CAACU,OAAM,YAAY,iBAAiB;AACjD,gBAAM,UAAU,aAAaA,MAAK,SAAS,UAAU;AACrD,gBAAM,YAAY,KAAK,OAAO,MAAM,OAAO,SAAS,SAAS,IAAI,IAAI;AACrE,iBAAO,WAAW,WAAWA,MAAK,OAAOA,MAAK,QAAQ;AAAA,QACxD;AAAA,MACF;AACA,YAAM,eAAe,OAAO;AAAA,QAC1B;AAAA,QACA;AAAA,MACF;AACA,YAAM,QAAQ,OAAO;AAAA,QACnB,cAAc,CAACV,MAAKE,aAAY;AAC9B,gBAAM,aAAaA,aAAY,UAAU,UAAUA;AACnD,iBAAO,aAAaF,MAAK,UAAU;AAAA,QACrC;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,OAAO;AAAA,QACtB,cAAc;AAAA,QACd;AAAA,MACF;AACA,YAAM,sBAAsB,CAACY,QAAOD,cAAa;AAC/C,cAAM,YAAY,UAAU,UAAUC,MAAK;AAC3C,cAAM1B,QAAO,uBAAuB,SAAS,EAAE,MAAMyB,SAAQ;AAC7D,gBAAQzB,OAAM;AAAA,UACd,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AACH,mBAAO,aAAa;AAAA,UACtB,KAAK;AACH,mBAAO,MAAM;AAAA,QACf;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW,CAAC,SAAS,UAAUC,QAAO,WAAW;AACrD,YAAIA,WAAU,QAAQ;AACpB,mBAAS,SAAS,QAAQ;AAAA,QAC5B,OAAO;AACL,gBAAM,SAAS,UAAUA,MAAK;AAAA,QAChC;AAAA,MACF;AACA,YAAM,WAAW,CAACyB,QAAO,UAAU,YAAY;AAC7C,eAAO,SAASA,QAAO,QAAQ,CAAC,EAAE,KAAK,MAAM,QAAQA,QAAO,OAAO,GAAG,CAAAP,WAAS,QAAQA,QAAO,OAAO,CAAC;AAAA,MACxG;AACA,YAAM,kBAAkB,CAACO,QAAO,gBAAgB;AAC9C,cAAMV,WAAU,MAAMU,QAAO,WAAW,EAAE,WAAW,MAAM;AACzD,gBAAM,aAAa,aAAa,QAAQ,aAAa,MAAMA,MAAK,EAAE,GAAG;AACrE,cAAI,gBAAgB,SAAS;AAC3B,qBAASA,QAAO,oBAAoB,UAAU;AAAA,UAChD,WAAW,gBAAgB,YAAY;AACrC,qBAASA,QAAO,WAAW,UAAU;AAAA,UACvC,OAAO;AACL,qBAASA,QAAO,UAAU;AAAA,UAC5B;AACA,iBAAO;AAAA,QACT,CAAC;AACD,cAAMV,QAAO;AACb,eAAOA;AAAA,MACT;AACA,YAAM,WAAW,CAACU,QAAOU,UAAS;AAChC,cAAM,UAAU,CAAC;AACjB,cAAM,WAAW,CAAC;AAClB,cAAM,WAAW,iBAAe,MAAM,aAAa,CAAAtB,SAAO;AACxD,cAAIA,KAAI,OAAO;AACb,oBAAQ,KAAKA,KAAI,OAAO;AAAA,UAC1B;AACA,gBAAM,KAAKA,KAAI;AACf,gBAAM,EAAE;AACR,iBAAOA,KAAI,OAAO,CAAAU,UAAQ;AACxB,gBAAIA,MAAK,OAAO;AACd,uBAAS,KAAKA,MAAK,OAAO;AAAA,YAC5B;AACA,qBAASA,MAAK,SAAS,WAAWA,MAAK,SAAS,CAAC;AACjD,qBAASA,MAAK,SAAS,WAAWA,MAAK,SAAS,CAAC;AACjD,qBAAS,IAAIA,MAAK,OAAO;AAAA,UAC3B,CAAC;AACD,iBAAO;AAAA,QACT,CAAC;AACD,cAAM,eAAe,iBAAe,OAAO,aAAa,cAAY,MAAM,SAAS,OAAO,CAAAe,SAAO;AAC/F,mBAASA,KAAI,SAAS,QAAQA,KAAI,SAAS,CAAC;AAC5C,iBAAOA,KAAI;AAAA,QACb,CAAC,CAAC;AACF,cAAM,gBAAgB,CAAC,aAAa,gBAAgB;AAClD,gBAAMvB,WAAU,gBAAgBU,QAAO,WAAW;AAClD,gBAAMyC,QAAO,gBAAgB,aAAa,eAAe;AACzD,gBAAM,eAAeA,MAAK,WAAW;AACrC,iBAAOnD,UAAS,YAAY;AAAA,QAC9B;AACA,cAAM,gBAAgB,iBAAe;AACnC,gBAAMU,QAAO,WAAW,EAAE,KAAK,QAAQ;AAAA,QACzC;AACA,cAAM,wBAAwB,CAAC,aAAa,gBAAgB;AAC1D,cAAI,YAAY,SAAS,GAAG;AAC1B,0BAAc,aAAa,WAAW;AAAA,UACxC,OAAO;AACL,0BAAc,WAAW;AAAA,UAC3B;AAAA,QACF;AACA,cAAM,cAAc,CAAC;AACrB,cAAM,cAAc,CAAC;AACrB,cAAM,cAAc,CAAC;AACrB,cAAM,sBAAsB,CAAC;AAC7B,eAAOU,OAAM,CAAAtB,SAAO;AAClB,kBAAQA,KAAI,SAAS;AAAA,YACrB,KAAK;AACH,0BAAY,KAAKA,IAAG;AACpB;AAAA,YACF,KAAK;AACH,0BAAY,KAAKA,IAAG;AACpB;AAAA,YACF,KAAK;AACH,0BAAY,KAAKA,IAAG;AACpB;AAAA,YACF,KAAK;AACH,kCAAoB,KAAKA,IAAG;AAC5B;AAAA,UACF;AAAA,QACF,CAAC;AACD,8BAAsB,qBAAqB,UAAU;AACrD,8BAAsB,aAAa,OAAO;AAC1C,8BAAsB,aAAa,OAAO;AAC1C,8BAAsB,aAAa,OAAO;AAC1C,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,OAAO,CAAAsB,UAAQ,MAAMA,OAAM,CAAAtB,SAAO;AACtC,cAAM,KAAK,QAAQA,KAAI,OAAO;AAC9B,eAAOA,KAAI,OAAO,CAAAU,UAAQ;AACxB,gBAAM,aAAa,KAAKA,MAAK,OAAO;AACpC,mBAAS,YAAY,WAAWA,MAAK,SAAS,CAAC;AAC/C,mBAAS,YAAY,WAAWA,MAAK,SAAS,CAAC;AAC/C,mBAAS,IAAI,UAAU;AAAA,QACzB,CAAC;AACD,eAAO;AAAA,MACT,CAAC;AAED,YAAM,YAAY,CAACY,OAAM,UAAU;AACjC,eAAO,MAAMA,OAAM,CAAAtB,SAAO;AACxB,iBAAO,QAAQA,MAAK,KAAK;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAACsB,OAAM,UAAU;AAC9B,eAAOA,MAAK;AAAA,MACd;AACA,YAAM,WAAW,CAAC,IAAI,SAAS;AAC7B,YAAI,GAAG,WAAW,GAAG;AACnB,iBAAO;AAAA,QACT;AACA,cAAMmB,SAAQ,GAAG;AACjB,cAAM,QAAQ,UAAU,IAAI,OAAK;AAC/B,iBAAO,CAAC,KAAKA,OAAM,SAAS,EAAE,OAAO;AAAA,QACvC,CAAC;AACD,eAAO,MAAM,MAAM,GAAG,MAAM;AAAA,MAC9B;AACA,YAAM,UAAU,CAACnB,OAAMtB,MAAK,QAAQ,eAAe;AACjD,cAAM,UAAU,OAAOsB,OAAMtB,IAAG;AAChC,cAAM,WAAW,QAAQ,YAAY;AACrC,cAAM,UAAU,SAAS,QAAQ,MAAM,MAAM,MAAM,GAAG,UAAU;AAChE,cAAM,UAAU,WAAW,IAAI,SAAS,UAAUsB,MAAK,MAAMtB,IAAG,GAAG,MAAM,GAAG,UAAU;AACtF,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,YAAY,CAACsB,OAAM,eAAe;AACtC,cAAM,OAAO,MAAMA,OAAM,CAAAtB,SAAO,MAAMA,KAAI,OAAO,KAAK,CAAC;AACvD,cAAM,aAAa,CAAC,UAAU,aAAa,SAAS,YAAY;AAC9D,mBAASA,OAAM,UAAUA,OAAM,WAAW,SAASA,QAAO;AACxD,qBAAS,SAAS,aAAa,SAAS,cAAc,SAAS,UAAU;AACvE,mBAAKA,MAAK,UAAU;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AACA,eAAO,MAAMsB,OAAM,CAACtB,MAAK,aAAa;AACpC,gBAAM,UAAU,OAAOA,KAAI,OAAO,CAACU,OAAM,gBAAgB;AACvD,gBAAI,KAAK,UAAU,iBAAiB,OAAO;AACzC,oBAAM,SAAS,QAAQY,OAAM,UAAU,aAAa,UAAU;AAC9D,yBAAW,UAAU,aAAa,OAAO,SAAS,OAAO,OAAO;AAChE,qBAAO,CAAC,UAAUZ,MAAK,SAAS,OAAO,SAAS,OAAO,SAASA,MAAK,KAAK,CAAC;AAAA,YAC7E,OAAO;AACL,qBAAO,CAAC;AAAA,YACV;AAAA,UACF,CAAC;AACD,iBAAO,aAAaV,KAAI,SAAS,SAASA,KAAI,SAASA,KAAI,KAAK;AAAA,QAClE,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAAC,WAAW,YAAY,UAAU;AAC/C,cAAMsB,QAAO,CAAC;AACd,eAAO,UAAU,WAAW,CAAAgC,cAAY;AACtC,gBAAM,eAAe,CAAC;AACtB,mBAAS,cAAc,GAAG,cAAc,UAAU,KAAK,SAAS,eAAe;AAC7E,kBAAM,UAAU,UAAU,YAAY,WAAW,WAAW,EAAE,IAAI,YAAU,WAAW,OAAO,SAAS,OAAO,KAAK,CAAC,EAAE,WAAW,MAAM,WAAW,WAAW,OAAO,GAAG,MAAM,KAAK,CAAC;AACnL,yBAAa,KAAK,OAAO;AAAA,UAC3B;AACA,UAAAhC,MAAK,KAAK,SAASgC,UAAS,SAAS,cAAc,YAAY,KAAK,CAAC;AAAA,QACvE,CAAC;AACD,iBAAS,WAAW,GAAG,WAAW,UAAU,KAAK,MAAM,YAAY;AACjE,gBAAM,WAAW,CAAC;AAClB,mBAAS,cAAc,GAAG,cAAc,UAAU,KAAK,SAAS,eAAe;AAC7E,kBAAM,UAAU,UAAU,MAAM,WAAW,UAAU,WAAW,EAAE,IAAI,UAAQ,WAAW,KAAK,SAAS,OAAO,KAAK,QAAQ,CAAC,EAAE,WAAW,MAAM,WAAW,WAAW,IAAI,GAAG,MAAM,KAAK,CAAC;AACxL,qBAAS,KAAK,OAAO;AAAA,UACvB;AACA,gBAAM,YAAY,UAAU,IAAI;AAChC,gBAAMtD,OAAM,SAAS,UAAU,SAAS,UAAU,UAAU,SAAS,KAAK;AAC1E,UAAAsB,MAAK,KAAKtB,IAAG;AAAA,QACf;AACA,eAAOsB;AAAA,MACT;AAEA,YAAM,gBAAgB,CAAC,WAAW,eAAe,OAAO,WAAW,YAAY,KAAK;AACpF,YAAM,eAAe,CAAAA,UAAQ,UAAUA,OAAM,IAAI;AACjD,YAAM,kBAAkB,CAAC,WAAW,YAAY,QAAQ,UAAU,KAAK,CAAAhC,OAAK,OAAOA,GAAE,OAAO,OAAK,KAAK,SAAS,EAAE,OAAO,CAAC,CAAC;AAC1H,YAAM,eAAe,CAAC,WAAW,QAAQ,cAAc;AACrD,cAAM,UAAU,MAAM,OAAO,WAAW,YAAU;AAChD,iBAAO,KAAK,MAAM,EAAE,KAAK,QAAM,gBAAgB,WAAW,EAAE,CAAC,EAAE,OAAO,SAAS;AAAA,QACjF,CAAC;AACD,cAAMW,SAAQ,IAAI,OAAO;AACzB,eAAO,OAAOA,OAAM,SAAS,GAAGA,MAAK;AAAA,MACvC;AACA,YAAM,MAAM,CAAC,WAAWsD,UAAS,YAAY,YAAY,gBAAgB,CAAC3C,QAAO,QAAQ,YAAY,eAAe;AAClH,cAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,cAAM,eAAe,SAAS,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,OAAO,EAAE,WAAW,aAAa,QAAQ;AAC/I,cAAM,SAAS2C,SAAQ,WAAW,MAAM,EAAE,IAAI,UAAQ;AACpD,gBAAM,QAAQ,cAAc,WAAW,UAAU;AACjD,gBAAM,SAAS,UAAU,OAAO,MAAM,MAAM,YAAY,UAAU,GAAG,YAAY;AACjF,gBAAM,gBAAgB,yBAAyB,OAAO,IAAI;AAC1D,gBAAMjC,QAAO,aAAa,OAAO,IAAI;AACrC,iBAAO;AAAA,YACL;AAAA,YACA,MAAAA;AAAA,YACA,QAAQ,OAAO;AAAA,YACf;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO,OAAO,KAAK,SAAO;AACxB,gBAAM,cAAc,SAASV,QAAO,IAAI,IAAI;AAC5C,gBAAM,cAAc,SAAS,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,MAAM,EAAE,WAAW,MAAM,UAAU,aAAaA,MAAK,CAAC;AAC3J,gBAAM,WAAW,SAAS,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,MAAM,EAAE,WAAW,aAAa;AAClI,qBAAWA,QAAO,IAAI,MAAM,IAAI,MAAM;AAAA,YACpC,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,SAAS;AAAA,UACX,CAAC;AACD,qBAAWA,MAAK;AAChB,mBAASA,QAAO,eAAe;AAC/B,cAAI,IAAI,cAAc,SAAS,GAAG;AAChC,kBAAMA,QAAO,iBAAiB,IAAI,cAAc,KAAK,GAAG,CAAC;AAAA,UAC3D;AACA,iBAAO,SAAS,KAAK;AAAA,YACnB,QAAQ,IAAI;AAAA,YACZ,SAAS,YAAY;AAAA,YACrB,UAAU,YAAY;AAAA,UACxB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAAC,WAAW,WAAW,KAAK,OAAO,OAAO,EAAE,KAAK,CAAAF,UAAQ,gBAAgB,WAAWA,KAAI,EAAE,IAAI,aAAW;AACvH,cAAMvB,SAAQ;AAAA,UACZ,GAAG;AAAA,UACH,YAAY,OAAO;AAAA,UACnB,WAAW,OAAO;AAAA,QACpB;AACA,eAAOA;AAAA,MACT,CAAC,CAAC;AACF,YAAM,kBAAkB,CAAC,WAAW,WAAW,aAAa,WAAW,QAAQ,MAAM,EAAE,IAAI,CAAAc,YAAU;AAAA,QACnG,OAAAA;AAAA,QACA,YAAY,OAAO;AAAA,QACnB,WAAW,OAAO;AAAA,MACpB,EAAE;AACF,YAAM,aAAa,CAAC,YAAY,WAAW,OAAO;AAClD,YAAM,eAAe,CAAC,YAAY,WAAW,OAAO;AACpD,YAAM,UAAU,CAAC,WAAW,WAAW,aAAa,WAAW,QAAQ,MAAM;AAC7E,YAAM,kBAAkB,CAAC,WAAW,WAAW,aAAa,WAAW,QAAQ,CAAAuB,YAAU,CAACA,QAAO,QAAQ;AACzG,YAAM,sBAAsB,CAAC,WAAWd,UAAS,gBAAgB,WAAWA,KAAI,EAAE,OAAO,CAAAc,YAAU,CAACA,QAAO,QAAQ;AACnH,YAAM,cAAc,CAAC,WAAWvB,WAAU,OAAOA,QAAO,CAAAS,UAAQ,oBAAoB,WAAWA,KAAI,CAAC;AACpG,YAAM,qBAAqB,CAAC,WAAW,WAAW,WAAW,WAAW,MAAM,EAAE,OAAO,eAAa,YAAY,WAAW,UAAU,KAAK,CAAC;AAC3I,YAAM,uBAAuB,CAAC,WAAW,WAAW,aAAa,WAAW,MAAM,EAAE,OAAO,CAAAT,WAAS,YAAY,WAAWA,MAAK,CAAC;AAEjI,YAAM,UAAU,CAACqB,OAAMkB,SAAQ,YAAY,iBAAiB;AAC1D,cAAM1C,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,YAAIxB,MAAK,WAAW,GAAG;AACrB,iBAAOwB;AAAA,QACT;AACA,iBAAS,IAAIkB,QAAO,UAAU,KAAKA,QAAO,WAAW,KAAK;AACxD,mBAAS,IAAIA,QAAO,UAAU,KAAKA,QAAO,WAAW,KAAK;AACxD,kBAAMxC,OAAMF,MAAK;AACjB,kBAAM,WAAW,QAAQE,MAAK,CAAC,EAAE;AACjC,uBAAWA,MAAK,GAAG,WAAW,aAAa,GAAG,OAAO,QAAQ,CAAC;AAAA,UAChE;AAAA,QACF;AACA,eAAOsB;AAAA,MACT;AACA,YAAM,UAAU,CAACA,OAAM,QAAQ,YAAY,iBAAiB;AAC1D,cAAMxB,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,YAAImB,SAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI3C,MAAK,QAAQ,KAAK;AACpC,mBAAS,IAAI,GAAG,IAAI,WAAWA,MAAK,EAAE,GAAG,KAAK;AAC5C,kBAAME,OAAMF,MAAK;AACjB,kBAAM,cAAc,QAAQE,MAAK,CAAC;AAClC,kBAAM,iBAAiB,YAAY;AACnC,kBAAM,cAAc,WAAW,gBAAgB,MAAM;AACrD,gBAAI,eAAe,CAACyC,QAAO;AACzB,yBAAWzC,MAAK,GAAG,WAAW,aAAa,GAAG,MAAM,YAAY,QAAQ,CAAC;AAAA,YAC3E,WAAW,aAAa;AACtB,cAAAyC,SAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AACA,eAAOnB;AAAA,MACT;AACA,YAAM,cAAc,CAACtB,MAAK,eAAe;AACvC,eAAO,MAAMA,MAAK,CAAC,MAAMU,UAAS;AAChC,iBAAO,OAAO,MAAM,iBAAe;AACjC,mBAAO,WAAW,YAAY,SAASA,MAAK,OAAO;AAAA,UACrD,CAAC,IAAI,OAAO,KAAK,OAAO,CAACA,KAAI,CAAC;AAAA,QAChC,GAAG,CAAC,CAAC;AAAA,MACP;AACA,YAAM,YAAY,CAACY,OAAM,OAAO,YAAY,iBAAiB;AAC3D,YAAI,QAAQ,KAAK,QAAQA,MAAK,GAAG,MAAM,QAAQ;AAC7C,iBAAOA,OAAM,CAAAtB,SAAO;AAClB,kBAAM,WAAWA,KAAI,MAAM,QAAQ;AACnC,gBAAI,SAAS;AACb,kBAAM,aAAa,aAAa;AAChC,mBAAOA,KAAI,MAAM,SAAS,QAAQ,UAAU,WAAW,SAAS,SAASA,KAAI,MAAM,QAAQ,QAAQ,OAAO,GAAG;AAC3G,yBAAWA,MAAK,QAAQ,QAAQ,WAAW,YAAY,MAAMA,KAAI,MAAM,QAAQ,QAAQ,QAAQ,CAAC;AAChG;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAOsB;AAAA,MACT;AACA,YAAM,YAAY,CAACA,OAAM,OAAO,YAAY,iBAAiB;AAC3D,cAAMxB,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,YAAI,QAAQ,KAAK,QAAQxB,MAAK,QAAQ;AACpC,gBAAM,eAAeA,MAAK,QAAQ,GAAG;AACrC,gBAAMG,SAAQ,YAAY,cAAc,UAAU;AAClD,iBAAOA,QAAO,CAAAS,UAAQ;AACpB,gBAAI,cAAc,SAAS,KAAK;AAChC,qBAAS,IAAI,OAAO,IAAIZ,MAAK,QAAQ,KAAK;AACxC,uBAAS,IAAI,GAAG,IAAI,WAAWA,MAAK,EAAE,GAAG,KAAK;AAC5C,sBAAME,OAAMF,MAAK;AACjB,sBAAM,UAAU,QAAQE,MAAK,CAAC;AAC9B,sBAAM,cAAc,WAAW,QAAQ,SAASU,MAAK,OAAO;AAC5D,oBAAI,aAAa;AACf,sBAAI,YAAY,OAAO,GAAG;AACxB,kCAAc,SAAS,KAAK,aAAa,CAAC;AAAA,kBAC5C;AACA,8BAAY,KAAK,SAAO;AACtB,+BAAWV,MAAK,GAAG,WAAW,KAAK,MAAM,QAAQ,QAAQ,CAAC;AAAA,kBAC5D,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAOsB;AAAA,MACT;AAEA,YAAM,UAAU,CAAAnC,WAAS;AACvB,cAAM,cAAc,QAAM,GAAGA,MAAK;AAClC,cAAM,cAAc,SAASA,MAAK;AAClC,cAAM,eAAe,MAAM;AAC3B,cAAM,SAAS;AAAA,UACb,KAAK;AAAA,UACL,OAAOA;AAAA,UACP,MAAM,CAAC,UAAU,YAAY,QAAQA,MAAK;AAAA,UAC1C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,KAAK,YAAU,OAAO,MAAM,OAAOA,MAAK,CAAC;AAAA,UACzC,UAAU;AAAA,UACV,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,IAAI;AAAA,UACJ,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,MAAM,QAAM;AACV,eAAGA,MAAK;AAAA,UACV;AAAA,UACA,YAAY,MAAM,SAAS,KAAKA,MAAK;AAAA,QACvC;AACA,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAAqE,WAAS;AACrB,cAAM,eAAe,MAAM;AAC3B,cAAM,SAAS;AAAA,UACb,KAAK;AAAA,UACL,OAAOA;AAAA,UACP,MAAM,CAAC,SAAS,aAAa,QAAQA,MAAK;AAAA,UAC1C,SAAS;AAAA,UACT,SAAS;AAAA,UACT,KAAK;AAAA,UACL,UAAU,YAAU,OAAO,MAAM,OAAOA,MAAK,CAAC;AAAA,UAC9C,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,IAAI;AAAA,UACJ,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,UAAU,IAAI,OAAOA,MAAK,CAAC;AAAA,UAC3B,MAAM;AAAA,UACN,YAAY,SAAS;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,YAAM,aAAa,CAAC,UAAU,QAAQ,SAAS,KAAK,MAAM,MAAM,GAAG,GAAG,OAAO;AAC7E,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAEA,YAAM,UAAU,CAAC,cAAc,OAAO,UAAU;AAC9C,YAAI,aAAa,OAAO,MAAM,UAAU,aAAa,SAAS,WAAW,MAAM,EAAE,GAAG;AAClF,iBAAO,OAAO,MAAM,qDAAqD,aAAa,MAAM,eAAe,aAAa,MAAM;AAAA,QAChI;AACA,cAAM,eAAe,MAAM,MAAM,aAAa,GAAG;AACjD,cAAM,eAAe,aAAa,GAAG,MAAM,MAAM,aAAa,MAAM;AACpE,cAAM,cAAc,WAAW,MAAM,EAAE;AACvC,cAAM,cAAc,MAAM;AAC1B,eAAO,OAAO,MAAM;AAAA,UAClB,UAAU,aAAa,SAAS;AAAA,UAChC,UAAU,aAAa,SAAS;AAAA,QAClC,CAAC;AAAA,MACH;AACA,YAAM,eAAe,CAAC,OAAO,UAAU;AACrC,cAAM,aAAa,WAAW,MAAM,EAAE;AACtC,cAAM,aAAa,WAAW,MAAM,EAAE;AACtC,eAAO;AAAA,UACL,UAAU;AAAA,UACV,UAAU,aAAa;AAAA,QACzB;AAAA,MACF;AACA,YAAM,gBAAgB,CAAC,OAAO,UAAU;AACtC,cAAM,aAAa,MAAM;AACzB,cAAM,aAAa,MAAM;AACzB,eAAO;AAAA,UACL,UAAU,aAAa;AAAA,UACvB,UAAU;AAAA,QACZ;AAAA,MACF;AACA,YAAM,mBAAmB,CAAC,QAAQxD,MAAK,YAAY,aAAa;AAC9D,cAAM,YAAYA,KAAI,YAAY,aAAa,WAAW,MAAM,WAAW;AAC3E,eAAO,QAAQ,QAAQ,SAAO,WAAW,UAAU,GAAG,MAAM,SAAS,GAAG,CAAC,CAAC;AAAA,MAC5E;AACA,YAAM,UAAU,CAACsB,OAAM,QAAQ,YAAY,kBAAkB;AAC3D,cAAM,aAAaA,MAAKA,MAAK,SAAS;AACtC,eAAOA,MAAK,OAAO,QAAQ,QAAQ,MAAM;AACvC,gBAAM,YAAY,WAAW,YAAY,aAAa,WAAW,WAAW,WAAW;AACvF,gBAAMtB,OAAM,MAAM,YAAY,WAAW,QAAQ;AACjD,gBAAM,WAAW,iBAAiBA,KAAI,MAAM,QAAQA,MAAK,YAAY,SAAO,MAAM,eAAe,IAAI,SAAS,CAAC,CAAC;AAChH,iBAAO,SAASA,MAAK,QAAQ;AAAA,QAC/B,CAAC,CAAC;AAAA,MACJ;AACA,YAAM,UAAU,CAACsB,OAAM,QAAQ,YAAY,eAAe,MAAMA,OAAM,CAAAtB,SAAO;AAC3E,cAAM,cAAc,iBAAiB,QAAQA,MAAK,YAAY,KAAK;AACnE,eAAO,SAASA,MAAK,YAAY,WAAW;AAAA,MAC9C,CAAC;AACD,YAAM,gBAAgB,CAACsB,OAAM,YAAY,kBAAkB,MAAMA,OAAM,CAAAtB,SAAO;AAC5E,eAAO,MAAM,eAAe,CAAC,KAAK,WAAW;AAC3C,gBAAM,WAAW,iBAAiB,GAAGA,MAAK,YAAY,MAAM,EAAE;AAC9D,iBAAO,QAAQ,KAAK,QAAQ,QAAQ;AAAA,QACtC,GAAGA,IAAG;AAAA,MACR,CAAC;AACD,YAAM,SAAS,CAAC,OAAO,OAAO,eAAe;AAC3C,cAAM,WAAW,MAAM,WAAW,IAAI,UAAU;AAChD,cAAM,WAAW,MAAM,WAAW,IAAI,UAAU;AAChD,cAAM,gBAAgB,yBAAyB,KAAK;AACpD,cAAM,YAAY,WAAW,MAAM,EAAE;AACrC,cAAM,kBAAkB,OAAO,eAAe,YAAU,WAAW,YAAY,CAAC;AAChF,cAAM,eAAe,SAAS,OAAO,KAAK,IAAI,MAAM,QAAQ,GAAG,YAAY,kBAAkB,YAAY,IAAI,SAAS;AACtH,cAAM,mBAAmB,yBAAyB,YAAY;AAC9D,eAAO,SAAS,cAAc,KAAK,IAAI,MAAM,QAAQ,GAAG,YAAY,YAAY,kBAAkB,MAAM,CAAC;AAAA,MAC3G;AAEA,YAAM,aAAa,CAACsB,OAAMtB,MAAKyB,MAAK,eAAe;AACjD,cAAM,YAAY,QAAQH,MAAKtB,OAAMyB,IAAG;AACxC,cAAM,WAAW,MAAM,YAAY,UAAU,OAAO;AACpD,cAAM,aAAaH,MAAKtB;AACxB,eAAOsB,MAAK,SAAS,KAAK,WAAW,UAAU,IAAI,MAAMG,OAAM,KAAK,SAAS,eAAe,YAAYA,OAAM,CAAC,CAAC,KAAKA,OAAM,WAAW,MAAM,SAAS,KAAK,SAAS,eAAe,YAAYA,OAAM,CAAC,CAAC,KAAKzB,OAAM,KAAK,SAAS,eAAesB,MAAKtB,OAAM,IAAIyB,IAAG,CAAC,KAAKzB,OAAMsB,MAAK,SAAS,KAAK,SAAS,eAAeA,MAAKtB,OAAM,IAAIyB,IAAG,CAAC;AAAA,MAC5U;AACA,YAAM,cAAc,CAAC,cAAc,OAAO,WAAW,WAAW,YAAY,kBAAkB;AAC5F,cAAM,WAAW,aAAa;AAC9B,cAAM,WAAW,aAAa;AAC9B,cAAM,cAAc,UAAU;AAC9B,cAAM,aAAa,WAAW,UAAU,EAAE;AAC1C,cAAM,SAAS,WAAW;AAC1B,cAAM,SAAS,WAAW,aAAa,cAAc;AACrD,cAAM,kBAAkB,YAAY,eAAe,MAAM;AACzD,iBAASnC,KAAI,UAAUA,KAAI,QAAQA,MAAK;AACtC,cAAI,aAAa;AACjB,mBAAS,IAAI,UAAU,IAAI,QAAQ,KAAK;AACtC,gBAAI,gBAAgB,IAAI;AACtB;AACA;AAAA,YACF;AACA,gBAAI,WAAW,OAAOA,IAAG,GAAG,UAAU,GAAG;AACvC,sBAAQ,OAAO,eAAe,MAAMA,KAAI,CAAC,GAAG,YAAY,UAAU,IAAI;AAAA,YACxE;AACA,kBAAM,gBAAgB,IAAI,WAAW;AACrC,kBAAM,UAAU,QAAQ,UAAUA,KAAI,WAAW,aAAa;AAC9D,kBAAM,aAAa,QAAQ;AAC3B,kBAAM,cAAc,UAAU,QAAQ,UAAU;AAChD,uBAAW,MAAMA,KAAI,GAAG,WAAW,aAAa,MAAM,QAAQ,QAAQ,CAAC;AAAA,UACzE;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,uBAAuB,CAAC,qBAAqBgC,OAAM,kBAAkB;AACzE,cAAM,gBAAgB,WAAWA,MAAK,EAAE;AACxC,cAAM,qBAAqB,mBAAmBA,KAAI,EAAE,KAAK,SAAS,oBAAoB;AACtF,cAAM,uBAAuB,QAAQ,gBAAgB,oBAAoB,QAAQ,SAAO,MAAM,oBAAoB,MAAM;AACxH,cAAM,kBAAkB,OAAO,sBAAsB,SAAO,OAAO,eAAe,CAAAG,SAAOA,SAAQ,GAAG,CAAC,EAAE,MAAM,gBAAgB,CAAC;AAC9H,eAAO;AAAA,UACL,KAAK;AAAA,UACL,QAAQ;AAAA,QACV;AAAA,MACF;AACA,YAAM,+BAA+B,CAAC,cAAc3B,OAAM,kBAAkB,SAAS,eAAe,YAAU,UAAU,aAAa,UAAU,UAAU,WAAWA,MAAK,EAAE,IAAI,aAAa,MAAM;AAClM,YAAM,UAAU,CAAC,cAAc,OAAO,OAAO,WAAW,eAAe;AACrE,cAAM,gBAAgB,yBAAyB,KAAK;AACpD,cAAM,oBAAoB,qBAAqB,cAAc,OAAO,aAAa;AACjF,cAAM,YAAY,mBAAmB,KAAK,EAAE;AAC5C,cAAM,4BAA4B,6BAA6B,mBAAmB,WAAW,aAAa;AAC1G,cAAM,SAAS,QAAQ,mBAAmB,OAAO,SAAS;AAC1D,eAAO,OAAO,IAAI,UAAQ;AACxB,gBAAM,QAAQ;AAAA,YACZ,GAAG;AAAA,YACH,UAAU,KAAK,WAAW,0BAA0B;AAAA,UACtD;AACA,gBAAM,aAAa,OAAO,OAAO,OAAO,SAAS;AACjD,gBAAM,mBAAmB,yBAAyB,UAAU;AAC5D,gBAAM,+BAA+B,6BAA6B,mBAAmB,WAAW,gBAAgB;AAChH,iBAAO,YAAY,mBAAmB,YAAY,WAAW,WAAW,YAAY,4BAA4B;AAAA,QAClH,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,OAAO,OAAO,OAAO,WAAW,eAAe;AACjE,kBAAU,OAAO,OAAO,YAAY,UAAU,IAAI;AAClD,cAAM,QAAQ,cAAc,OAAO,KAAK;AACxC,cAAM,gBAAgB,OAAO,OAAO,OAAO,SAAS;AACpD,cAAM,cAAc,cAAc,OAAO,aAAa;AACtD,cAAM,gBAAgB,OAAO,OAAO,aAAa,SAAS;AAC1D,eAAO,MAAM,eAAe,CAAC,SAAS,MAAM;AAC1C,iBAAO,SAAS,SAAS,OAAO,cAAc,GAAG,KAAK;AAAA,QACxD,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,OAAO,OAAO,OAAO,WAAW,eAAe;AACjE,kBAAU,OAAO,OAAO,YAAY,UAAU,IAAI;AAClD,cAAM,SAAS,yBAAyB,KAAK;AAC7C,cAAM,OAAO,aAAa,OAAO,KAAK;AACtC,cAAM,QAAQ;AAAA,UACZ,GAAG;AAAA,UACH,UAAU,KAAK,WAAW,OAAO;AAAA,QACnC;AACA,cAAM,gBAAgB,OAAO,OAAO,OAAO,SAAS;AACpD,cAAM;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,QACR,IAAI,mBAAmB,aAAa;AACpC,cAAM,YAAY,yBAAyB,aAAa;AACxD,cAAM,aAAa,aAAa,OAAO,KAAK;AAC5C,cAAM,cAAc;AAAA,UAClB,GAAG;AAAA,UACH,UAAU,WAAW,WAAW,UAAU;AAAA,QAC5C;AACA,cAAM,cAAc,cAAc,OAAO,WAAW,SAAS;AAC7D,cAAM,gBAAgB,OAAO,aAAa,aAAa,SAAS;AAChE,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG,QAAQ,MAAM,GAAG,KAAK;AAAA,UACzB,GAAG;AAAA,UACH,GAAG,QAAQ,MAAM,OAAO,QAAQ,MAAM;AAAA,QACxC;AAAA,MACF;AAEA,YAAM,WAAW,CAACE,MAAK,WAAW,YAAY,iBAAiB,MAAMA,MAAK,UAAQ,aAAa,MAAM,UAAU,GAAG,SAAS;AAC3H,YAAM,cAAc,CAACsB,OAAM,OAAO,SAAS,YAAY,iBAAiB;AACtE,cAAM,EAAC,MAAAxB,OAAM,KAAI,IAAI,mBAAmBwB,KAAI;AAC5C,cAAMF,UAAStB,MAAK,MAAM,GAAG,KAAK;AAClC,cAAMuB,SAAQvB,MAAK,MAAM,KAAK;AAC9B,cAAM,SAAS,SAASA,MAAK,UAAU,CAAC,IAAI,MAAM;AAChD,gBAAM,aAAa,QAAQ,KAAK,QAAQA,MAAK,UAAU,WAAW,eAAeA,MAAK,QAAQ,IAAI,CAAC,GAAG,eAAeA,MAAK,QAAQ,CAAC,CAAC;AACpI,gBAAM,MAAM,aAAa,QAAQA,MAAK,QAAQ,CAAC,IAAI,WAAW,aAAa,GAAG,SAAS,UAAU,GAAG,MAAM,GAAG,QAAQ;AACrH,iBAAO;AAAA,QACT,GAAG,YAAY,YAAY;AAC3B,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAGsB;AAAA,UACH;AAAA,UACA,GAAGC;AAAA,QACL;AAAA,MACF;AACA,YAAM,gBAAgB,CAACrB,MAAK,QAAQE,UAAS,YAAY,SAAS,YAAY,iBAAiB;AAC7F,YAAIA,aAAY,cAAc,CAAC,YAAY;AACzC,gBAAMQ,QAAO,QAAQV,MAAK,OAAO;AACjC,iBAAO,WAAW,aAAaU,MAAK,SAAS,UAAU,GAAG,MAAM,KAAK;AAAA,QACvE,OAAO;AACL,iBAAO,QAAQV,MAAK,MAAM;AAAA,QAC5B;AAAA,MACF;AACA,YAAM,iBAAiB,CAACsB,OAAM,OAAO,SAAS,YAAY,iBAAiB,MAAMA,OAAM,CAAAtB,SAAO;AAC5F,cAAM,aAAa,QAAQ,KAAK,QAAQ,WAAWA,IAAG,KAAK,WAAW,eAAeA,MAAK,QAAQ,CAAC,GAAG,eAAeA,MAAK,KAAK,CAAC;AAChI,cAAM,MAAM,cAAcA,MAAK,OAAOA,KAAI,SAAS,YAAY,SAAS,YAAY,YAAY;AAChG,eAAO,QAAQA,MAAK,OAAO,GAAG;AAAA,MAChC,CAAC;AACD,YAAM,kBAAkB,CAACsB,OAAMvB,aAAY,OAAOuB,OAAM,CAAAtB,SAAO;AAC7D,cAAM,gBAAgBA,KAAI;AAC1B,cAAMC,SAAQ,MAAMF,UAAS,CAAC,KAAK,WAAW,UAAU,KAAK,SAAS,IAAI,SAAS,IAAI,MAAM,GAAG,MAAM,EAAE,OAAO,IAAI,MAAM,SAAS,CAAC,CAAC,IAAI,KAAK,aAAa;AAC1J,eAAOE,OAAM,SAAS,IAAI,CAAC,SAASD,KAAI,SAASC,QAAOD,KAAI,SAASA,KAAI,KAAK,CAAC,IAAI,CAAC;AAAA,MACtF,CAAC;AACD,YAAM,eAAe,CAACsB,OAAM,OAAO,WAAW;AAC5C,cAAM,EAAC,MAAAxB,OAAM,KAAI,IAAI,mBAAmBwB,KAAI;AAC5C,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAGxB,MAAK,MAAM,GAAG,KAAK;AAAA,UACtB,GAAGA,MAAK,MAAM,SAAS,CAAC;AAAA,QAC1B;AAAA,MACF;AAEA,YAAM,gBAAgB,CAACwB,OAAM,UAAU,UAAU,eAAe,eAAeA,MAAK,WAAW,QAAQ,MAAM,WAAc,WAAW,KAAK,WAAW,eAAeA,MAAK,WAAW,IAAI,QAAQ,GAAG,eAAeA,MAAK,WAAW,QAAQ,CAAC;AAC5O,YAAM,mBAAmB,CAACtB,MAAK,OAAO,eAAe,QAAQ,KAAK,WAAW,eAAeA,MAAK,QAAQ,CAAC,GAAG,eAAeA,MAAK,KAAK,CAAC;AACvI,YAAM,mBAAmB,CAACsB,OAAM,UAAU,UAAU,eAAe,cAAcA,OAAM,UAAU,UAAU,UAAU,KAAK,iBAAiBA,MAAK,WAAW,UAAU,UAAU;AAC/K,YAAM,uBAAuB,CAAC,WAAW,kBAAkB;AACzD,cAAM,sBAAsB,OAAO,eAAe,QAAQ,KAAK,cAAc,UAAU,KAAK;AAC5F,eAAO,sBAAsB,SAAS,CAACZ,OAAM,WAAW,aAAa;AACnE,gBAAMxB,QAAO,KAAKwB,MAAK,OAAO;AAC9B,iBAAO,EAAExB,UAAS,QAAQ,cAAc;AAAA,QAC1C;AAAA,MACF;AACA,YAAM,yBAAyB,CAAC,cAAc,eAAe;AAC3D,cAAM,sBAAsB,OAAO,YAAY,QAAQ,KAAK,cAAc,YAAY;AACtF,eAAO,sBAAsB,SAAS,CAACwB,OAAM,UAAU,cAAc;AACnE,gBAAMxB,QAAO,KAAKwB,MAAK,OAAO;AAC9B,iBAAO,EAAExB,UAAS,QAAQ,WAAW;AAAA,QACvC;AAAA,MACF;AACA,YAAM,iBAAiB,CAAC,YAAYwB,OAAM,UAAU,eAAe;AACjE,cAAM,UAAU,WAAS,UAAU,QAAQ,WAAWA,KAAI,IAAI,WAAWA,KAAI;AAC7E,cAAM,WAAW,WAAS,QAAQ,KAAK,IAAI,GAAI,eAAgB;AAC/D,YAAI,YAAY;AACd,iBAAO,aAAaA,KAAI,IAAI,SAAS,QAAQ,IAAI;AAAA,QACnD,WAAW,cAAc,aAAaA,KAAI,GAAG;AAC3C,gBAAM,gBAAgB,aAAa,QAAQ,QAAQ;AACnD,iBAAO,SAAS,aAAa;AAAA,QAC/B,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,oBAAoB,CAAC,YAAY,kBAAkB,CAACA,OAAM,UAAU,gBAAgB,SAAS,KAAK,eAAe,YAAYA,MAAK,SAAS,OAAO,cAAc,YAAY,CAAC;AACnL,YAAM,uBAAuB,CAAC,YAAY,eAAe,CAACA,OAAM,aAAa,SAAS,KAAK,eAAe,YAAYA,MAAK,SAAS,OAAO,WAAW,SAAS,CAAC;AAChK,YAAM,UAAU,CAACA,OAAM,YAAY,eAAe,WAAW,WAAWA,MAAK,SAAS,UAAU,GAAG,MAAMA,MAAK,QAAQ;AACtH,YAAM,YAAY,CAACY,OAAM,SAAS,YAAY,YAAY,UAAU,UAAU,kBAAkB;AAC9F,cAAM,WAAW,CAAAZ,UAAQ;AACvB,iBAAO,OAAO,SAAS,YAAU;AAC/B,mBAAO,WAAWA,MAAK,SAAS,OAAO,OAAO;AAAA,UAChD,CAAC;AAAA,QACH;AACA,eAAO,MAAMY,OAAM,CAACtB,MAAK,aAAa;AACpC,iBAAO,SAASA,MAAK,CAACU,OAAM,aAAa;AACvC,gBAAI,SAASA,KAAI,GAAG;AAClB,oBAAM,UAAU,cAAcA,OAAM,UAAU,QAAQ,IAAI,SAASA,OAAM,YAAY,UAAU,IAAIA;AACnG,uBAAS,SAAS,UAAU,QAAQ,EAAE,KAAK,WAAS;AAClD,2BAAW,QAAQ,SAAS,EAAE,OAAO,SAAS,KAAK,KAAK,EAAE,CAAC;AAAA,cAC7D,CAAC;AACD,qBAAO;AAAA,YACT,OAAO;AACL,qBAAOA;AAAA,YACT;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,iBAAiB,CAACZ,OAAM,aAAa,eAAe,OAAOA,OAAM,CAACE,MAAK,MAAM;AACjF,eAAO,iBAAiBF,OAAM,GAAG,aAAa,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQE,MAAK,WAAW,CAAC;AAAA,MAC7F,CAAC;AACD,YAAM,cAAc,CAACF,OAAM,UAAU,eAAe;AAClD,cAAM,YAAYA,MAAK;AACvB,eAAO,OAAO,UAAU,OAAO,CAAC,MAAM,MAAM;AAC1C,iBAAO,iBAAiBA,OAAM,UAAU,GAAG,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI;AAAA,QACrE,CAAC;AAAA,MACH;AACA,YAAM,iBAAiB,CAACwB,OAAM,SAAS,YAAY,YAAY,iBAAiB;AAC9E,cAAMxB,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,cAAM,UAAU,OAAO,SAAS,WAAS,eAAexB,OAAM,OAAO,UAAU,CAAC;AAChF,cAAM,aAAa,MAAMA,OAAM,CAAAE,SAAO,cAAcA,KAAI,KAAK,CAAC;AAC9D,cAAM,oBAAoB,uBAAuB,SAAS,UAAU;AACpE,cAAM,iBAAiB,qBAAqB,YAAY,UAAU;AAClE,eAAO,UAAUsB,OAAM,SAAS,YAAY,cAAc,SAAS,gBAAgB,iBAAiB;AAAA,MACtG;AACA,YAAM,cAAc,CAACA,OAAM,SAASpB,UAAS,YAAY,YAAY,cAAc,iBAAiB;AAClG,cAAM,EAAC,MAAM,MAAAJ,MAAI,IAAI,mBAAmBwB,KAAI;AAC5C,cAAM,YAAYxB,MAAK,QAAQ;AAC/B,cAAM,UAAU,OAAO,SAAS,WAAS,YAAYA,OAAM,OAAO,UAAU,CAAC;AAC7E,cAAM,gBAAgB,MAAM,UAAU,OAAO,CAAC,OAAO,UAAU,cAAc,eAAeA,OAAM,OAAO,UAAU,CAAC,CAAC;AACrH,cAAM,UAAU,CAAC,GAAGA,KAAI;AACxB,eAAO,SAAS,WAAS;AACvB,kBAAQ,SAAS,aAAa,aAAaA,MAAK,QAAQI,QAAO;AAAA,QACjE,CAAC;AACD,cAAM,UAAU;AAAA,UACd,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AACA,cAAM,oBAAoB,qBAAqB,WAAW,aAAa;AACvE,cAAM,iBAAiB,kBAAkB,YAAY,aAAa;AAClE,eAAO,UAAU,SAAS,SAAS,YAAY,cAAc,aAAa,eAAe,gBAAgB,iBAAiB;AAAA,MAC5H;AACA,YAAM,eAAe,CAACoB,OAAM,SAAS,YAAY,iBAAiB;AAChE,cAAMxB,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,cAAM,cAAc,MAAM,SAAS,CAAAE,YAAU,QAAQ1B,MAAK0B,QAAO,MAAMA,QAAO,MAAM,CAAC;AACrF,eAAO,UAAUF,OAAM,aAAa,YAAY,cAAc,SAAS,SAAS,MAAM,MAAM;AAAA,MAC9F;AAEA,YAAM,WAAW,WAAS;AACxB,YAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,gBAAM,IAAI,MAAM,wBAAwB;AAAA,QAC1C;AACA,YAAI,MAAM,WAAW,GAAG;AACtB,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QACnD;AACA,cAAM,eAAe,CAAC;AACtB,cAAMmC,OAAM,CAAC;AACb,eAAO,OAAO,CAAC,OAAO,UAAU;AAC9B,gBAAM,SAAS,KAAK,KAAK;AACzB,cAAI,OAAO,WAAW,GAAG;AACvB,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AACA,gBAAMhE,OAAM,OAAO;AACnB,gBAAMN,SAAQ,MAAMM;AACpB,cAAIgE,KAAIhE,UAAS,QAAW;AAC1B,kBAAM,IAAI,MAAM,4BAA4BA,IAAG;AAAA,UACjD,WAAWA,SAAQ,QAAQ;AACzB,kBAAM,IAAI,MAAM,uCAAuC;AAAA,UACzD,WAAW,CAAC,QAAQN,MAAK,GAAG;AAC1B,kBAAM,IAAI,MAAM,iCAAiC;AAAA,UACnD;AACA,uBAAa,KAAKM,IAAG;AACrB,UAAAgE,KAAIhE,QAAO,IAAI,SAAS;AACtB,kBAAM,YAAY,KAAK;AACvB,gBAAI,cAAcN,OAAM,QAAQ;AAC9B,oBAAM,IAAI,MAAM,uCAAuCM,OAAM,gBAAgBN,OAAM,SAAS,OAAOA,SAAQ,YAAY,SAAS;AAAA,YAClI;AACA,kBAAM,QAAQ,cAAY;AACxB,oBAAM,aAAa,KAAK,QAAQ;AAChC,kBAAI,aAAa,WAAW,WAAW,QAAQ;AAC7C,sBAAM,IAAI,MAAM,mDAAmD,aAAa,KAAK,GAAG,IAAI,eAAe,WAAW,KAAK,GAAG,CAAC;AAAA,cACjI;AACA,oBAAM,UAAU,OAAO,cAAc,YAAU;AAC7C,uBAAO,WAAW,YAAY,MAAM;AAAA,cACtC,CAAC;AACD,kBAAI,CAAC,SAAS;AACZ,sBAAM,IAAI,MAAM,kEAAkE,WAAW,KAAK,IAAI,IAAI,iBAAiB,aAAa,KAAK,IAAI,CAAC;AAAA,cACpJ;AACA,qBAAO,SAASM,MAAK,MAAM,MAAM,IAAI;AAAA,YACvC;AACA,mBAAO;AAAA,cACL,MAAM,IAAI,aAAa;AACrB,oBAAI,SAAS,WAAW,MAAM,QAAQ;AACpC,wBAAM,IAAI,MAAM,iDAAiD,MAAM,SAAS,WAAW,SAAS,MAAM;AAAA,gBAC5G;AACA,sBAAM,SAAS,SAAS;AACxB,uBAAO,OAAO,MAAM,MAAM,IAAI;AAAA,cAChC;AAAA,cACA;AAAA,cACA,KAAK,WAAS;AACZ,wBAAQ,IAAI,OAAO;AAAA,kBACjB;AAAA,kBACA,aAAaA;AAAA,kBACb,QAAQ;AAAA,gBACV,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAOgE;AAAA,MACT;AACA,YAAM,MAAM,EAAE,SAAS;AAEvB,YAAM,QAAQ,IAAI,SAAS;AAAA,QACzB,EAAE,MAAM,CAAC,EAAE;AAAA,QACX,EAAE,MAAM,CAAC,OAAO,EAAE;AAAA,QAClB;AAAA,UACE,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,gBAAgB,EAAE,GAAG,MAAM;AAEjC,YAAM,aAAa,CAAC,OAAO,UAAU;AACnC,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO,cAAc,KAAK;AAAA,QAC5B;AACA,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO,cAAc,KAAK,CAAC;AAAA,QAC7B;AACA,YAAI,UAAU,GAAG;AACf,iBAAO,cAAc,KAAK,GAAG,CAAC;AAAA,QAChC;AACA,YAAI,UAAU,MAAM,SAAS,GAAG;AAC9B,iBAAO,cAAc,MAAM,QAAQ,GAAG,KAAK;AAAA,QAC7C;AACA,YAAI,QAAQ,KAAK,QAAQ,MAAM,SAAS,GAAG;AACzC,iBAAO,cAAc,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC;AAAA,QACzD;AACA,eAAO,cAAc,KAAK;AAAA,MAC5B;AACA,YAAM,YAAY,CAAC,OAAO,QAAQ,MAAM,WAAWC,YAAW;AAC5D,cAAM,SAAS,MAAM,MAAM,CAAC;AAC5B,cAAM,UAAU,WAAW,OAAO,MAAM;AACxC,cAAM,SAAS,SAAS,MAAM,QAAQ,SAAS,CAAC,CAAC,CAAC;AAClD,cAAM,SAAS,WAAS,UAAU,kBAAkB,OAAO,QAAQ,IAAI;AACvE,cAAM,SAAS,CAAC,OAAO,SAASA,QAAO,mBAAmB,QAAQ,OAAO,MAAM,MAAM,UAAU,aAAa,GAAG,UAAU,UAAU;AACnI,cAAM,WAAW,CAAC,MAAM,OAAO,SAASA,QAAO,iBAAiB,QAAQ,MAAM,OAAO,MAAM,MAAM,UAAU,aAAa,GAAG,UAAU,UAAU;AAC/I,cAAM,UAAU,CAAC,MAAM,UAAUA,QAAO,oBAAoB,QAAQ,MAAM,OAAO,MAAM,UAAU,aAAa,GAAG,UAAU,UAAU;AACrI,eAAO,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,UAAU,OAAO;AAAA,MAC/D;AAEA,YAAM,QAAQ,CAAC,OAAO,KAAK,aAAa;AACtC,YAAIpE,KAAI;AACR,iBAAS,IAAI,OAAO,IAAI,KAAK,KAAK;AAChC,UAAAA,MAAK,SAAS,OAAO,SAAY,SAAS,KAAK;AAAA,QACjD;AACA,eAAOA;AAAA,MACT;AACA,YAAM,2BAA2B,CAAC,WAAW,WAAW;AACtD,cAAMF,OAAM,UAAU,UAAU,SAAS;AACzC,eAAO,MAAMA,MAAK,CAAAsB,UAAQ;AACxB,gBAAMS,SAAQ,MAAMT,MAAK,QAAQA,MAAK,SAASA,MAAK,SAAS,MAAM;AACnE,iBAAO;AAAA,YACL,SAASA,MAAK;AAAA,YACd,OAAAS;AAAA,YACA,SAAST,MAAK;AAAA,UAChB;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,6BAA6B,CAAC,WAAW,WAAW;AACxD,cAAM,SAAS,UAAU,YAAY,SAAS;AAC9C,eAAO,MAAM,QAAQ,CAAC,QAAQ,WAAW;AAAA,UACvC,SAAS,OAAO;AAAA,UAChB,OAAO,OAAO;AAAA,UACd,SAAS,OAAO;AAAA,QAClB,EAAE;AAAA,MACJ;AACA,YAAM,4BAA4B,CAAC,WAAW,YAAY;AACxD,cAAMtB,OAAM,UAAU,UAAU,SAAS;AACzC,eAAO,MAAMA,MAAK,CAAAsB,UAAQ;AACxB,gBAAMiD,UAAS,MAAMjD,MAAK,KAAKA,MAAK,MAAMA,MAAK,SAAS,OAAO;AAC/D,iBAAO;AAAA,YACL,SAASA,MAAK;AAAA,YACd,QAAAiD;AAAA,YACA,SAASjD,MAAK;AAAA,UAChB;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,iBAAiB,CAAC,WAAW,YAAY;AAC7C,eAAO,MAAM,UAAU,KAAK,CAACV,MAAK,MAAM;AACtC,iBAAO;AAAA,YACL,SAASA,KAAI;AAAA,YACb,QAAQ,QAAQ;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,QAAQ,aAAW,MAAM,SAAS,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC;AAC1D,YAAM,cAAc,CAAC,WAAW,WAAW;AACzC,YAAI,UAAU,WAAW,SAAS,GAAG;AACnC,iBAAO,2BAA2B,WAAW,MAAM;AAAA,QACrD,OAAO;AACL,iBAAO,yBAAyB,WAAW,MAAM;AAAA,QACnD;AAAA,MACF;AACA,YAAM,sBAAsB,CAAC,WAAW,QAAQ,cAAc;AAC5D,cAAM,WAAW,YAAY,WAAW,MAAM;AAC9C,eAAO,UAAU,CAAAU,UAAQ;AACvB,oBAAU,gBAAgBA,MAAK,SAASA,MAAK,KAAK;AAAA,QACpD,CAAC;AAAA,MACH;AACA,YAAM,cAAc,CAACE,QAAO,OAAO,OAAO,UAAU,cAAc;AAChE,cAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,cAAM,OAAO,UAAU,aAAa,KAAK;AACzC,cAAM,SAAS,UAAU,UAAU,WAAW,SAAS;AACvD,cAAM,eAAe,UAAU,UAAU,KAAK,UAAU;AACxD,cAAM,cAAc,SAAS,gBAAgB,QAAQ,OAAO,MAAM,UAAU,aAAa,GAAG,YAAY;AACxG,cAAM,SAAS,UAAU,QAAQ,OAAO,aAAa,WAAW,QAAQ;AACxE,cAAM,YAAY,MAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,OAAO,EAAE;AACzD,4BAAoB,WAAW,WAAW,SAAS;AACnD,iBAAS,YAAY,UAAU,kBAAkB,aAAa,YAAY;AAAA,MAC5E;AACA,YAAM,eAAe,CAACA,QAAO,OAAO,OAAO,cAAc;AACvD,cAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,cAAM,UAAU,gBAAgB,WAAWA,QAAO,SAAS;AAC3D,cAAM,aAAa,MAAM,SAAS,CAAC,IAAI,MAAM,UAAU,IAAI,KAAK,IAAI,QAAQ,IAAI,UAAU,CAAC,IAAI,EAAE;AACjG,cAAM,eAAe,0BAA0B,WAAW,UAAU;AACpE,cAAM,cAAc,eAAe,WAAW,UAAU;AACxD,eAAO,aAAa,CAAAZ,SAAO;AACzB,oBAAUA,KAAI,SAASA,KAAI,MAAM;AAAA,QACnC,CAAC;AACD,eAAO,cAAc,CAAAU,UAAQ;AAC3B,oBAAUA,MAAK,SAASA,MAAK,MAAM;AAAA,QACrC,CAAC;AACD,cAAMkB,SAAQ,MAAM,UAAU;AAC9B,kBAAUhB,QAAOgB,MAAK;AAAA,MACxB;AACA,YAAM,gCAAgC,CAAC,QAAQ,MAAM,SAAS,WAAW,oBAAoB;AAC3F,cAAM,YAAY,UAAU,SAAS,IAAI;AACzC,cAAM,QAAQ,UAAU,UAAU,WAAW,SAAS;AACtD,cAAM,kBAAkB,UAAU,WAAW;AAC7C,cAAM,EAAC,UAAU,MAAK,IAAI,gBAAgB,wBAAwB,OAAO,iBAAiB,QAAQ,YAAY,UAAU,UAAU;AAClI,4BAAoB,WAAW,UAAU,SAAS;AAClD,kBAAU,iBAAiB,KAAK;AAAA,MAClC;AACA,YAAM,gBAAgB,CAAC,QAAQ,MAAM,OAAO,cAAc;AACxD,cAAM,YAAY,UAAU,SAAS,IAAI;AACzC,cAAM,SAAS,UAAU,UAAU,WAAW,SAAS;AACvD,4BAAoB,WAAW,QAAQ,SAAS;AAAA,MAClD;AAEA,YAAM,gBAAgB,aAAW;AAC/B,cAAM,cAAc,CAAC,MAAMJ,YAAW;AACpC,gBAAM,eAAe,OAAO,MAAM,mBAAiB,cAAc,WAAWA,QAAO,MAAM;AACzF,iBAAO,eAAe,OAAO,KAAK,OAAO,CAACA,OAAM,CAAC;AAAA,QACnD;AACA,eAAO,MAAM,SAAS,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,YAAY,QAAQ,SAAS,QAAQ,MAAM;AAAA,MACnG;AAEA,YAAM,QAAQ,MAAM,KAAK;AACzB,YAAM,aAAa,MAAM,UAAU;AACnC,YAAM,UAAU,aAAW,KAAK,OAAO,MAAM,QAAQ,WAAW,OAAO;AACvE,YAAM,gBAAgB,aAAW;AAC/B,cAAM,UAAU,aAAa,SAAS,WAAW,CAAC;AAClD,cAAM,UAAU,aAAa,SAAS,WAAW,CAAC;AAClD,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAe,CAAC,YAAY,SAAS,kBAAkB;AAC3D,cAAM,SAAS,UAAQ,MAAM,KAAK,OAAO,IAAI,WAAW,IAAI,IAAI,IAAI,WAAW,KAAK,IAAI;AACxF,cAAM,QAAQ,UAAQ,WAAW,KAAK,OAAO,IAAI,WAAW,SAAS,IAAI,IAAI,WAAW,IAAI,IAAI;AAChG,cAAMoC,OAAM,aAAW;AACrB,cAAI,QAAQ,OAAO,GAAG;AACpB,mBAAO,MAAM,EAAE,QAAQ,CAAC;AAAA,UAC1B,OAAO;AACL,kBAAMlD,QAAO;AACb,kBAAM,cAAc,OAAO,OAAOA,KAAI,CAAC;AACvC,qBAAS,SAAS,KAAK;AAAA,cACrB,MAAMA;AAAA,cACN;AAAA,YACF,CAAC;AACD,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,SAAS,SAAS,KAAK;AAC3B,cAAM,YAAY,CAAC,SAAS,eAAe;AACzC,iBAAO,OAAO,KAAK,MAAM;AACvB,mBAAOkD,KAAI,OAAO;AAAA,UACpB,GAAG,OAAK;AACN,mBAAO,WAAW,SAAS,EAAE,IAAI,IAAI,EAAE,cAAcA,KAAI,OAAO;AAAA,UAClE,CAAC;AAAA,QACH;AACA,eAAO,EAAE,UAAU;AAAA,MACrB;AACA,YAAM,cAAc,SAAO;AACzB,eAAO,gBAAc;AACnB,gBAAM,OAAO,CAAC;AACd,gBAAMC,QAAO,CAAC,SAAS,eAAe;AACpC,mBAAO,OAAO,MAAM,OAAK;AACvB,qBAAO,WAAW,EAAE,MAAM,OAAO;AAAA,YACnC,CAAC;AAAA,UACH;AACA,gBAAM,UAAU,aAAW;AACzB,kBAAM,QAAQ,QAAQ,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;AAChD,kBAAMnD,QAAO,WAAW,QAAQ,SAAS,KAAK,KAAK;AACnD,iBAAK,KAAK;AAAA,cACR,MAAM;AAAA,cACN,KAAKA;AAAA,YACP,CAAC;AACD,mBAAOA;AAAA,UACT;AACA,gBAAM,gBAAgB,CAAC,SAAS,eAAe;AAC7C,gBAAI,QAAQ,OAAO,KAAK,MAAM,OAAO,GAAG;AACtC,qBAAO;AAAA,YACT,OAAO;AACL,oBAAMA,QAAO;AACb,qBAAOmD,MAAKnD,OAAM,UAAU,EAAE,KAAK,MAAM;AACvC,uBAAO,QAAQA,KAAI;AAAA,cACrB,GAAG,OAAK;AACN,uBAAO,WAAW,SAAS,EAAE,IAAI,IAAI,EAAE,MAAM,QAAQA,KAAI;AAAA,cAC3D,CAAC;AAAA,YACH;AAAA,UACF;AACA,iBAAO,EAAE,cAAc;AAAA,QACzB;AAAA,MACF;AACA,YAAM,oBAAoB,CAAAA,UAAQ,OAAOA,OAAM,OAAO,EAAE,IAAI,eAAa,UAAU,OAAO,GAAG,CAAC,CAAC;AAC/F,YAAM,UAAU,gBAAc;AAC5B,cAAMoD,WAAU,CAAApD,UAAQ;AACtB,gBAAM,QAAQ,kBAAkBA,KAAI;AACpC,gBAAM,KAAK,eAAa,MAAMA,OAAM,SAAS,SAAS,CAAC;AACvD,iBAAO,MAAM;AACX,kBAAM,MAAM,WAAW,KAAK;AAAA,cAC1B,SAASA;AAAA,cACT,SAAS;AAAA,cACT,SAAS;AAAA,YACX,CAAC;AACD,qBAAS,KAAK,OAAO;AACrB,qBAASA,OAAM,OAAO;AACtB,kBAAM,KAAK,eAAa,MAAM,KAAK,SAAS,SAAS,CAAC;AACtD,mBAAO;AAAA,UACT;AAAA,QACF;AACA,cAAMqD,SAAQ,CAAA9D,WAAS;AACrB,gBAAM,mBAAmB,MAAM;AAC7B,kBAAM,mBAAmB,IAAI,MAAMA,QAAO,iBAAiB,CAAC;AAC5D,gBAAI,iBAAiB,WAAW,GAAG;AACjC,qBAAO,SAAS,KAAK;AAAA,YACvB,OAAO;AACL,oBAAM,YAAY,iBAAiB;AACnC,oBAAM,SAAS;AAAA,gBACb;AAAA,gBACA;AAAA,cACF;AACA,oBAAM,UAAU,OAAO,kBAAkB,eAAa;AACpD,uBAAO,cAAc,aAAa,WAAW,QAAQ,SAAS;AAAA,cAChE,CAAC;AACD,qBAAO,UAAU,SAAS,KAAK,IAAI,SAAS,KAAK,SAAS;AAAA,YAC5D;AAAA,UACF;AACA,mBAASA,OAAM,IAAI,OAAO;AAC1B,2BAAiB,EAAE,KAAK,MAAM,SAASA,OAAM,IAAI,OAAO,GAAG,eAAa,MAAMA,OAAM,IAAI,SAAS,YAAY,OAAO,CAAC;AACrH,iBAAO,SAASA,OAAM,EAAE;AAAA,QAC1B;AACA,eAAO;AAAA,UACL,SAAA6D;AAAA,UACA,OAAAC;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA,WAAW;AAAA,QACX;AAAA,MACF;AAEA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,CAACnB,WAAU,SAAS;AACnC,cAAM,UAAUA,UAAS,SAAS,EAAE,KAAK,IAAI;AAC7C,eAAO,WAAW;AAAA,UAChB;AAAA,UACA;AAAA,QACF,GAAG,OAAO;AAAA,MACZ;AACA,YAAM,YAAY,CAACA,WAAU,SAAS;AACpC,cAAM,UAAUA,UAAS,SAAS,EAAE,KAAK,IAAI;AAC7C,eAAO,WAAW,WAAW,OAAO;AAAA,MACtC;AACA,YAAM,eAAe,CAACA,WAAU,SAAS;AACvC,eAAO,WAAW;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,GAAGA,UAAS,SAAS,EAAE,KAAK,IAAI,CAAC;AAAA,MACnC;AAEA,YAAM,aAAa,YAAY;AAC/B,YAAM,UAAU,aAAW;AACzB,eAAO,UAAU,YAAY,OAAO;AAAA,MACtC;AACA,YAAM,SAAS,aAAW;AACxB,eAAO,SAAS,YAAY,OAAO;AAAA,MACrC;AACA,YAAM,aAAa,aAAW;AAC5B,eAAO,aAAa,YAAY,OAAO;AAAA,MACzC;AAEA,YAAM,QAAQ,CAAA3C,WAAS;AACrB,cAAM+D,QAAO,MAAM,IAAI;AACvB,cAAM,aAAa,CAAApE,cAAY;AAC7B,iBAAO,OAAOA,WAAU,OAAK;AAC3B,mBAAOoE,MAAK,CAAC,KAAK,OAAO,CAAC,KAAK,MAAM,CAAC,EAAE,KAAK,EAAE,WAAW;AAAA,UAC5D,CAAC;AAAA,QACH;AACA,cAAM,aAAa,QAAM;AACvB,iBAAO,KAAK,EAAE,MAAM,QAAQ,WAAW,IAAI,MAAM,EAAE,OAAO;AAAA,QAC5D;AACA,cAAM,iBAAiB,QAAM;AAC3B,iBAAO,YAAY,EAAE,EAAE,IAAI,kBAAgB;AACzC,gBAAI,QAAQ,YAAY,GAAG;AACzB,qBAAO;AAAA,YACT;AACA,gBAAI,WAAW,YAAY,GAAG;AAC5B,qBAAO,KAAK,YAAY,MAAM,QAAQ,QAAQ;AAAA,YAChD;AACA,mBAAO;AAAA,UACT,CAAC,EAAE,MAAM,KAAK;AAAA,QAChB;AACA,cAAM,WAAW,CAAAtD,UAAQ;AACvB,iBAAO,OAAOA,KAAI,EAAE,KAAK,eAAa;AACpC,kBAAM,sBAAsB,eAAe,SAAS;AACpD,mBAAO,OAAO,SAAS,EAAE,IAAI,CAAAf,YAAU;AACrC,qBAAO,wBAAwB,QAAQ,WAAWA,OAAM,KAAKqE,MAAK,SAAS,KAAK,QAAQrE,OAAM,KAAK,CAAC,KAAKe,OAAMf,OAAM,IAAI,CAAC,IAAI,CAAC,aAAa,QAAQ,IAAI,CAAC;AAAA,YAC3J,CAAC;AAAA,UACH,CAAC,EAAE,MAAM,CAAC,CAAC;AAAA,QACb;AACA,cAAM,cAAc,MAAM;AACxB,gBAAM,UAAU,OAAOM,QAAO,CAAAS,UAAQ;AACpC,kBAAMd,YAAW,WAAWc,KAAI;AAChC,mBAAO,WAAWd,SAAQ,IAAI,CAAC,IAAIA,UAAS,OAAO,SAASc,KAAI,CAAC;AAAA,UACnE,CAAC;AACD,iBAAO,QAAQ,WAAW,IAAI,CAAC,aAAa,QAAQ,IAAI,CAAC,IAAI;AAAA,QAC/D;AACA,cAAM,WAAW,YAAY;AAC7B,cAAMT,OAAM,EAAE;AACd,eAAOA,OAAM,IAAI,QAAQ;AAAA,MAC3B;AAEA,YAAM,aAAa,UAAQ,aAAa,MAAM,IAAI;AAClD,YAAM,QAAQ,CAAAW,WAAS;AACrB,cAAMX,SAAQ,QAAQW,MAAK;AAC3B,YAAIX,OAAM,WAAW,GAAG;AACtB,mBAASW,MAAK;AAAA,QAChB;AAAA,MACF;AACA,YAAM,UAAU,CAACU,OAAM,YAAY;AAAA,QACjC,MAAAA;AAAA,QACA;AAAA,MACF;AACA,YAAM,6BAA6B,CAAAxB,UAAQ,QAAQA,OAAM,CAAAE,SAAO,QAAQA,KAAI,OAAO,CAAAU,UAAQ;AACzF,cAAM,OAAOA,MAAK;AAClB,eAAO,OAAO,WAAW,IAAI,GAAG,IAAI;AAAA,MACtC,CAAC,CAAC;AACF,YAAM,kBAAkB,CAACY,OAAMtB,MAAK,WAAW;AAC7C,YAAI,IAAI;AACR,cAAMF,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,eAAO,SAAS,MAAM,MAAM,KAAKxB,MAAKE,WAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,EAAE,OAAO,UAAU,EAAE,QAAQ,MAAM,2BAA2BF,KAAI,CAAC;AAAA,MACvN;AACA,YAAM,SAAS,CAACwB,OAAMtB,MAAK,WAAW;AACpC,cAAM,gBAAgB,gBAAgBsB,OAAMtB,MAAK,MAAM;AACvD,eAAO,QAAQsB,OAAM,aAAa;AAAA,MACpC;AACA,YAAM,aAAa,aAAW;AAC5B,cAAM,iBAAiB,CAAC,MAAME,YAAW;AACvC,gBAAM,YAAY,OAAO,MAAM,mBAAiB,cAAc,QAAQA,QAAO,GAAG;AAChF,iBAAO,YAAY,OAAO,KAAK,OAAO,CAACA,OAAM,CAAC;AAAA,QAChD;AACA,eAAO,MAAM,SAAS,gBAAgB,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,YAAY,QAAQ,MAAM,QAAQ,GAAG;AAAA,MAChG;AACA,YAAM,qBAAqB,CAACF,OAAM,SAAS,YAAY,gBAAgB;AACrE,cAAM,cAAc,QAAQ,GAAG;AAC/B,cAAMxB,QAAO,WAAW,OAAO;AAC/B,cAAM,UAAU,MAAMA,OAAM,CAAC,KAAKE,SAAQ;AACxC,gBAAM,OAAO,YAAY,IAAI,MAAM,aAAaA,KAAI,MAAM,IAAI,OAAO,YAAY,YAAY,SAAS;AACtG,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO,IAAI,QAAQ;AAAA,UACrB;AAAA,QACF,GAAG;AAAA,UACD,MAAAsB;AAAA,UACA,OAAO;AAAA,QACT,CAAC,EAAE;AACH,eAAO,OAAO,SAAS,aAAa,QAAQ,GAAG,MAAM;AAAA,MACvD;AACA,YAAM,oBAAoB,CAACA,OAAM,SAAS,YAAY,gBAAgB;AACpE,cAAMxB,QAAO,WAAW,OAAO;AAC/B,cAAM,SAASA,MAAKA,MAAK,SAAS;AAClC,cAAM,cAAc,OAAO,MAAM,OAAO;AACxC,cAAM,UAAU,MAAMA,OAAM,CAAC,MAAME,SAAQ;AACzC,iBAAO,YAAY,MAAM,aAAaA,KAAI,KAAK,YAAY,YAAY,SAAS;AAAA,QAClF,GAAGsB,KAAI;AACP,eAAO,OAAO,SAAS,aAAa,QAAQ,GAAG,MAAM;AAAA,MACvD;AACA,YAAM,wBAAwB,CAACA,OAAM,eAAe,YAAY,gBAAgB;AAC9E,cAAM,UAAU,cAAc;AAC9B,cAAMvB,WAAU,cAAc,OAAO;AACrC,cAAM,cAAcA,SAAQ,GAAG;AAC/B,cAAM,UAAU,MAAMA,UAAS,CAAC,KAAK0B,SAAQ;AAC3C,gBAAM,OAAO,eAAe,IAAI,MAAM,aAAaA,KAAI,SAAS,IAAI,OAAO,YAAY,YAAY,SAAS;AAC5G,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO,IAAI,QAAQ;AAAA,UACrB;AAAA,QACF,GAAG;AAAA,UACD,MAAAH;AAAA,UACA,OAAO;AAAA,QACT,CAAC,EAAE;AACH,eAAO,OAAO,SAAS,QAAQ,GAAG,KAAK,WAAW;AAAA,MACpD;AACA,YAAM,uBAAuB,CAACA,OAAM,eAAe,YAAY,gBAAgB;AAC7E,cAAM,UAAU,cAAc;AAC9B,cAAM,SAAS,QAAQ,QAAQ,SAAS;AACxC,cAAM,cAAc,OAAO,SAAS,OAAO;AAC3C,cAAMvB,WAAU,cAAc,OAAO;AACrC,cAAM,UAAU,MAAMA,UAAS,CAAC,MAAM0B,SAAQ;AAC5C,iBAAO,eAAe,MAAM,aAAaA,KAAI,QAAQ,YAAY,YAAY,SAAS;AAAA,QACxF,GAAGH,KAAI;AACP,eAAO,OAAO,SAAS,QAAQ,GAAG,KAAK,WAAW;AAAA,MACpD;AACA,YAAM,sBAAsB,CAAC,aAAa,SAAS,YAAY,gBAAgB;AAC7E,cAAMvB,WAAU,cAAc,OAAO;AACrC,cAAM,gBAAgB,MAAMA,UAAS,CAAAyB,YAAUA,QAAO,MAAM;AAC5D,cAAM,UAAU,eAAe,aAAa,eAAe,MAAM,YAAY,YAAY,aAAa;AACtG,eAAO,OAAO,SAAS,QAAQ,GAAG,KAAK,QAAQ,GAAG,MAAM;AAAA,MAC1D;AACA,YAAM,oBAAoB,CAAC,aAAa,SAAS,YAAY,gBAAgB;AAC3E,cAAM,UAAU,aAAa,aAAa,SAAS,YAAY,YAAY,aAAa;AACxF,eAAO,OAAO,SAAS,QAAQ,GAAG,KAAK,QAAQ,GAAG,MAAM;AAAA,MAC1D;AACA,YAAM,wBAAwB,CAAC,aAAa,SAAS,YAAY,gBAAgB;AAC/E,cAAMzB,WAAU,cAAc,OAAO;AACrC,cAAM,gBAAgB,MAAMA,UAAS,CAAAyB,YAAUA,QAAO,MAAM;AAC5D,cAAM,UAAU,eAAe,aAAa,eAAe,OAAO,YAAY,YAAY,aAAa;AACvG,eAAO,OAAO,SAAS,QAAQ,GAAG,KAAK,QAAQ,GAAG,MAAM;AAAA,MAC1D;AACA,YAAM,sBAAsB,CAAC,aAAa,SAAS,YAAY,gBAAgB;AAC7E,cAAM,UAAU,aAAa,aAAa,SAAS,YAAY,YAAY,aAAa;AACxF,eAAO,OAAO,SAAS,QAAQ,GAAG,KAAK,QAAQ,GAAG,MAAM;AAAA,MAC1D;AACA,YAAM,kBAAkB,CAACtB,UAAS,eAAe,CAAC,aAAa,SAAS,YAAY,aAAa,iBAAiB;AAChH,cAAMJ,QAAO,WAAW,OAAO;AAC/B,cAAM,aAAa,MAAMA,OAAM,CAAA0B,YAAUA,QAAO,GAAG;AACnD,cAAM,UAAU,YAAY,aAAa,YAAYtB,UAAS,YAAY,YAAY,YAAY,eAAe,YAAY;AAC7H,eAAO,OAAO,SAAS,QAAQ,GAAG,KAAK,QAAQ,GAAG,MAAM;AAAA,MAC1D;AACA,YAAM,mBAAmB,gBAAgB,SAAS,IAAI;AACtD,YAAM,iBAAiB,gBAAgB,SAAS,KAAK;AACrD,YAAM,mBAAmB,gBAAgB,SAAS,KAAK;AACvD,YAAM,iBAAiB,CAACoB,OAAM,eAAe,aAAa,iBAAiB;AACzE,cAAMvB,WAAU,cAAc,cAAc,OAAO;AACnD,cAAM,UAAU,gBAAgBuB,OAAM,MAAMvB,UAAS,YAAU,OAAO,MAAM,CAAC;AAC7E,cAAM,cAAc,QAAQ,SAAS,IAAI,QAAQ,GAAG,MAAM,SAAS,IAAI;AACvE,eAAO,OAAO,SAASA,SAAQ,GAAG,KAAK,KAAK,IAAIA,SAAQ,GAAG,QAAQ,WAAW,CAAC;AAAA,MACjF;AACA,YAAM,cAAc,CAACuB,OAAM,SAAS,aAAa,iBAAiB;AAChE,cAAMxB,QAAO,WAAW,OAAO;AAC/B,cAAM,UAAU,aAAawB,OAAMxB,MAAK,GAAG,KAAKA,MAAKA,MAAK,SAAS,GAAG,GAAG;AACzE,cAAM,cAAc,QAAQ,SAAS,IAAI,QAAQ,SAAS,IAAI;AAC9D,eAAO,OAAO,SAAS,KAAK,IAAI,QAAQ,GAAG,KAAK,WAAW,GAAG,QAAQ,GAAG,MAAM;AAAA,MACjF;AACA,YAAM,eAAe,CAACwB,OAAM2C,WAAU,YAAY,gBAAgB;AAChE,cAAMhE,SAAQgE,UAAS;AACvB,cAAMhE,MAAK;AACX,cAAM,UAAU,QAAQqB,OAAM2C,UAAS,QAAQ,YAAY,YAAY,MAAMhE,MAAK,CAAC;AACnF,eAAO,QAAQ,SAAS,SAAS,KAAKA,OAAM,EAAE,CAAC;AAAA,MACjD;AACA,YAAM,iBAAiB,CAACqB,OAAM4C,aAAY,YAAY,gBAAgB;AACpE,cAAM,YAAY,CAAC,GAAGxD,UAAS,QAAQ,GAAGA,OAAM,YAAY,YAAY,QAAQA,KAAI,CAAC;AACrF,cAAM,UAAU,MAAMwD,aAAY,WAAW5C,KAAI;AACjD,eAAO,QAAQ,SAAS,SAAS,KAAK4C,YAAW,EAAE,CAAC;AAAA,MACtD;AACA,YAAM,eAAe,CAAC5C,OAAM,cAAc,YAAY,iBAAiB;AACrE,cAAM,UAAU,CAACV,QAAO,eAAe;AACrC,gBAAM,KAAK,UAAU,UAAUA,MAAK;AACpC,iBAAO,OAAO,IAAI,YAAY,IAAI;AAAA,QACpC;AACA,cAAM,QAAQ,QAAQ,aAAa,WAAW,aAAa,UAAU;AACrE,cAAM,eAAe,QAAQ,aAAa,KAAK,aAAa,MAAM;AAClE,cAAM,aAAa,QAAQ,cAAcU,OAAM,OAAO,aAAa,YAAY,UAAU;AACzF,eAAO,WAAW,KAAK,MAAM,QAAQA,OAAM,SAAS,KAAK,aAAa,OAAO,CAAC,GAAG,aAAW;AAC1F,iBAAO,OAAO,SAAS,aAAa,KAAK,aAAa,MAAM;AAAA,QAC9D,CAAC;AAAA,MACH;AACA,YAAM,cAAc,CAACxB,OAAM,YAAY,YAAY;AACjD,cAAM,eAAe,eAAeA,OAAM,QAAQ,OAAO;AACzD,cAAM,KAAK,UAAU,SAAS,YAAY;AAC1C,eAAO,OAAO,IAAI,YAAY,IAAI;AAAA,MACpC;AACA,YAAM,oBAAoB,CAACwB,OAAM,cAAc,YAAY,iBAAiB;AAC1E,cAAMxB,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,cAAM,QAAQ,aAAa,MAAM,GAAG;AACpC,cAAM,UAAUxB,MAAK,aAAa,MAAM,GAAG;AAC3C,cAAM,QAAQ,YAAY,aAAa,WAAW,aAAa,YAAY,OAAO;AAClF,cAAM,aAAa,WAAW,OAAOwB,OAAM,OAAO,aAAa,YAAY,UAAU;AACrF,eAAO,OAAO,YAAY,aAAa,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,MAAM;AAAA,MACnF;AACA,YAAM,mBAAmB,CAACA,OAAM,cAAc,YAAY,iBAAiB;AACzE,cAAMxB,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,cAAM,QAAQ,aAAa,MAAM,aAAa,MAAM,SAAS,GAAG,SAAS,aAAa,MAAM,aAAa,MAAM,SAAS,GAAG;AAC3H,cAAM,UAAUxB,MAAK,aAAa,MAAM,GAAG;AAC3C,cAAM,QAAQ,YAAY,aAAa,WAAW,aAAa,YAAY,OAAO;AAClF,cAAM,aAAa,WAAW,OAAOwB,OAAM,OAAO,aAAa,YAAY,UAAU;AACrF,eAAO,OAAO,YAAY,aAAa,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,MAAM;AAAA,MACnF;AACA,YAAM,oBAAoB,CAACA,OAAM,cAAc,YAAY,iBAAiB;AAC1E,cAAMxB,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,cAAM,QAAQ,aAAa,MAAM,GAAG;AACpC,cAAM,UAAUxB,MAAK;AACrB,cAAM,QAAQ,YAAY,aAAa,WAAW,aAAa,YAAY,OAAO;AAClF,cAAM,aAAa,WAAW,OAAOwB,OAAM,OAAO,aAAa,YAAY,UAAU;AACrF,eAAO,OAAO,YAAY,aAAa,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,MAAM;AAAA,MACnF;AACA,YAAM,mBAAmB,CAACA,OAAM,cAAc,YAAY,iBAAiB;AACzE,cAAMxB,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,cAAM,QAAQ,aAAa,MAAM,aAAa,MAAM,SAAS,GAAG,MAAM,aAAa,MAAM,aAAa,MAAM,SAAS,GAAG;AACxH,cAAM,UAAUxB,MAAK,aAAa,MAAM,GAAG;AAC3C,cAAM,QAAQ,YAAY,aAAa,WAAW,aAAa,YAAY,OAAO;AAClF,cAAM,aAAa,WAAW,OAAOwB,OAAM,OAAO,aAAa,YAAY,UAAU;AACrF,eAAO,OAAO,YAAY,aAAa,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,MAAM;AAAA,MACnF;AACA,YAAM,mBAAmB,CAACV,QAAO,WAAW;AAC1C,cAAM,QAAQ,UAAU,UAAUA,MAAK;AACvC,cAAM,UAAU,QAAQ,OAAO,MAAM;AACrC,eAAO,QAAQ,KAAK,mBAAiB;AACnC,gBAAM,mBAAmB,cAAc,cAAc,SAAS;AAC9D,gBAAM,cAAc,cAAc,GAAG;AACrC,gBAAM,cAAc,iBAAiB,SAAS,iBAAiB;AAC/D,gBAAM,sBAAsB,QAAQ,MAAM,MAAM,KAAK,CAAAZ,SAAO,SAASA,KAAI,OAAO,CAAAU,UAAQA,MAAK,UAAU,eAAeA,MAAK,SAAS,WAAW,CAAC,CAAC;AACjJ,iBAAO,mBAAmB,mBAAmB;AAAA,QAC/C,CAAC,EAAE,MAAM,EAAE;AAAA,MACb;AACA,YAAM,iBAAiB,CAACE,QAAO,WAAW;AACxC,cAAM,QAAQ,UAAU,UAAUA,MAAK;AACvC,cAAM,UAAU,QAAQ,OAAO,MAAM;AACrC,eAAO,QAAQ,KAAK,kBAAkB,EAAE,MAAM,EAAE;AAAA,MAClD;AACA,YAAM,gBAAgB,CAACA,QAAO,WAAW;AACvC,cAAM,QAAQ,UAAU,UAAUA,MAAK;AACvC,cAAM,UAAU,QAAQ,OAAO,MAAM;AACrC,eAAO,QAAQ,KAAK,mBAAiB;AACnC,gBAAM,mBAAmB,cAAc,cAAc,SAAS;AAC9D,gBAAM,cAAc,cAAc,GAAG;AACrC,gBAAM,cAAc,iBAAiB,MAAM,iBAAiB;AAC5D,gBAAM,eAAe,MAAM,IAAI,MAAM,aAAa,WAAW;AAC7D,iBAAO,kBAAkB,YAAY;AAAA,QACvC,CAAC,EAAE,MAAM,EAAE;AAAA,MACb;AACA,YAAM,SAAS,CAACA,QAAO,MAAM,SAAS,eAAe,cAAcA,QAAO,MAAM,SAAS,WAAW,MAAM;AAC1G,YAAM,8BAA8B,CAACA,QAAO,MAAM,SAAS,eAAe,8BAA8BA,QAAO,MAAM,SAAS,WAAW,QAAQ,WAAW,MAAM;AAClK,YAAM,sBAAsB,CAAC,YAAY,YAAY,OAAO,SAAS,CAAAY,YAAUA,QAAO,WAAW,KAAKA,QAAO,QAAQ;AACrH,YAAM,qBAAqB,CAAC,WAAW,YAAY,OAAO,SAAS,CAAAA,YAAUA,QAAO,SAASA,QAAO,WAAW,UAAU,KAAK,WAAWA,QAAO,QAAQ;AACxJ,YAAM,kBAAkB,CAAC,WAAW,YAAY;AAC9C,cAAMO,aAAY,QAAQ,SAAS;AACnC,cAAM,aAAa,cAAc,OAAO;AACxC,eAAO,MAAM,YAAY,CAAC,KAAKP,YAAW;AACxC,gBAAM,SAASO,WAAUP,QAAO;AAChC,gBAAM,WAAW,OAAO,IAAI,UAAU,EAAE,MAAM,CAAC;AAC/C,iBAAO,MAAM;AAAA,QACf,GAAG,CAAC;AAAA,MACN;AACA,YAAM,yBAAyB,CAAAJ,YAAU,CAAC,WAAW,WAAW,QAAQ,WAAW,MAAM,EAAE,OAAO,aAAW;AAC3G,cAAM,cAAcA,UAAS,sBAAsB;AACnD,eAAO,CAAC,YAAY,WAAW,OAAO;AAAA,MACxC,CAAC,EAAE,IAAI,cAAY;AAAA,QACjB;AAAA,QACA,YAAY,gBAAgB,WAAW,OAAO;AAAA,MAChD,EAAE;AACF,YAAM,wBAAwB,CAAC,WAAW,WAAW,gBAAgB,WAAW,MAAM,EAAE,IAAI,cAAY;AAAA,QACtG;AAAA,QACA,YAAY,CAAC,gBAAgB,WAAW,OAAO;AAAA,MACjD,EAAE;AACF,YAAM,wBAAwB,CAAAA,YAAU,CAAC,WAAW,WAAW,gBAAgB,WAAW,MAAM,EAAE,OAAO,aAAW;AAClH,cAAM,cAAcA,UAAS,sBAAsB;AACnD,eAAO,CAAC,YAAY,WAAW,QAAQ,KAAK;AAAA,MAC9C,CAAC;AACD,YAAM,sBAAsB,WAAW,UAAU,IAAI;AACrD,YAAM,oBAAoB,WAAW,UAAU,IAAI;AACnD,YAAM,mBAAmB,IAAI,oBAAoB,SAAS,MAAM,MAAM,WAAW,YAAY;AAC7F,YAAM,kBAAkB,IAAI,mBAAmB,SAAS,MAAM,MAAM,WAAW,YAAY;AAC3F,YAAM,sBAAsB,IAAI,uBAAuB,uBAAuB,IAAI,GAAG,6BAA6B,MAAM,WAAW,YAAY;AAC/I,YAAM,qBAAqB,IAAI,sBAAsB,uBAAuB,KAAK,GAAG,6BAA6B,MAAM,WAAW,YAAY;AAC9I,YAAM,eAAe,IAAI,gBAAgB,uBAAuB,6BAA6B,OAAO,WAAW,YAAY;AAC3H,YAAM,YAAY,IAAI,aAAa,SAAS,MAAM,OAAO,WAAW,YAAY;AAChF,YAAM,oBAAoB,IAAI,qBAAqB,iBAAiB,MAAM,MAAM,mBAAmB;AACnG,YAAM,sBAAsB,IAAI,uBAAuB,iBAAiB,MAAM,MAAM,iBAAiB;AACrG,YAAM,iBAAiB,IAAI,kBAAkB,iBAAiB,MAAM,MAAM,mBAAmB;AAC7F,YAAM,eAAe,IAAI,gBAAgB,iBAAiB,MAAM,MAAM,iBAAiB;AACvF,YAAM,iBAAiB,IAAI,kBAAkB,iBAAiB,MAAM,MAAM,iBAAiB;AAC3F,YAAM,kBAAkB,IAAI,mBAAmB,iBAAiB,MAAM,MAAM,mBAAmB;AAC/F,YAAM,oBAAoB,IAAI,qBAAqB,iBAAiB,MAAM,MAAM,iBAAiB;AACjG,YAAM,aAAa,IAAI,cAAc,oBAAoB,QAAQ,MAAM,WAAW,OAAO;AACzF,YAAM,eAAe,IAAI,gBAAgB,sBAAsB,QAAQ,MAAM,WAAW,OAAO;AAC/F,YAAM,aAAa,IAAI,cAAc,SAAS,QAAQ,MAAM,WAAW,YAAY;AACnF,YAAM,kBAAkB,IAAI,mBAAmB,sBAAsB,IAAI,GAAG,MAAM,MAAM,WAAW,YAAY;AAC/G,YAAM,iBAAiB,IAAI,kBAAkB,sBAAsB,KAAK,GAAG,MAAM,MAAM,WAAW,YAAY;AAC9G,YAAM,kBAAkB,IAAI,mBAAmB,iBAAiB,MAAM,MAAM,WAAW,YAAY;AACnG,YAAM,iBAAiB,IAAI,kBAAkB,iBAAiB,MAAM,MAAM,WAAW,YAAY;AACjG,YAAM,iBAAiB;AACvB,YAAM,eAAe;AACrB,YAAM,cAAc;AAEpB,YAAM,aAAa,CAAC,QAAQpB,SAAQ,OAAO,SAAS,UAAU,EAAE,MAAMA,KAAI,CAAC;AAC3E,YAAM,cAAc,CAAC,QAAQU,UAAS,OAAO,SAAS,WAAW,EAAE,MAAMA,MAAK,CAAC;AAC/E,YAAM,oBAAoB,CAAC,QAAQE,QAAO,SAAS;AACjD,eAAO,SAAS,iBAAiB;AAAA,UAC/B,GAAG;AAAA,UACH,OAAAA;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,2BAA2B,CAAC,QAAQX,QAAO,OAAO,QAAQ,eAAe;AAC7E,eAAO,SAAS,wBAAwB;AAAA,UACtC,OAAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,0BAA0B,YAAU;AACxC,eAAO,SAAS,qBAAqB;AAAA,MACvC;AACA,YAAM,wBAAwB,CAAC,QAAQ,QAAQkB,QAAOwC,SAAQ,WAAW;AACvE,eAAO,SAAS,qBAAqB;AAAA,UACnC;AAAA,UACA,OAAAxC;AAAA,UACA,QAAAwC;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,oBAAoB,CAAC,QAAQ,QAAQxC,QAAOwC,SAAQ,WAAW;AACnE,eAAO,SAAS,iBAAiB;AAAA,UAC/B;AAAA,UACA,OAAAxC;AAAA,UACA,QAAAwC;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB;AAAA,QACpB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,oBAAoB;AAAA,QACxB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,4BAA4B;AAAA,QAChC,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,QAAQ,CAAC,QAAQ/C,WAAU;AAC/B,YAAI,yBAAyB,MAAM,GAAG;AACpC,iBAAO,UAAU,eAAeA,MAAK;AAAA,QACvC,WAAW,oBAAoB,MAAM,GAAG;AACtC,iBAAO,UAAU,UAAUA,MAAK;AAAA,QAClC,OAAO;AACL,iBAAO,UAAU,aAAaA,MAAK;AAAA,QACrC;AAAA,MACF;AAEA,YAAM,eAAe,CAAC,QAAQ,eAAe,yBAAyB;AACpE,cAAM,cAAc,CAAAuD,YAAU,KAAK,QAAQA,OAAM,CAAC,MAAM;AACxD,cAAM,eAAe,CAAAvD,WAAS,CAAC,YAAY,MAAM,KAAK,YAAYA,MAAK,EAAE,OAAO;AAChF,cAAM,kBAAkB,CAAAA,WAAS,CAAC,YAAY,MAAM,KAAK,YAAYA,MAAK,EAAE,UAAU;AACtF,cAAMwD,gBAAe,sBAAsB,MAAM;AACjD,cAAM,gBAAgB,4BAA4B,MAAM,IAAI,OAAO;AACnE,cAAMC,uBAAsB,CAAAzD,WAAS;AACnC,kBAAQ,mBAAmB,MAAM,GAAG;AAAA,YACpC,KAAK;AACH,qBAAO,aAAa,QAAQ;AAAA,YAC9B,KAAK;AACH,qBAAO,aAAa,aAAa;AAAA,YACnC,KAAK;AACH,qBAAO,aAAa,MAAM;AAAA,YAC5B;AACE,qBAAO,aAAa,oBAAoBA,QAAO,SAAS;AAAA,UAC1D;AAAA,QACF;AACA,cAAM,yBAAyB,CAACA,QAAO,WAAW,OAAO,OAAO,KAAK,MAAM;AACzE,gBAAMX,SAAQ,QAAQW,MAAK;AAC3B,iBAAO,KAAKX,MAAK,EAAE,OAAO,MAAM,EAAE,IAAI,eAAa;AACjD,iCAAqB,mBAAmBW,OAAM,GAAG;AACjD,kBAAM,MAAM,OAAO,IAAI,UAAU;AACjC,gBAAI,WAAW,UAAU,GAAG;AAC5B,mBAAO,UAAU,OAAO,GAAG;AAC3B,kBAAM,WAAW,qBAAqB,GAAG;AACzC,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,GAAG,CAAAF,UAAQ;AACT,gBAAM,MAAM,YAAYA,KAAI;AAC5B,gBAAM,MAAM,OAAO,IAAI,UAAU;AACjC,cAAI,SAAS,IAAI,QAAQ,KAAK,IAAI,MAAM;AACxC,cAAI,OAAO,IAAI,QAAQ,KAAK,IAAI,MAAM;AACtC,iBAAO,UAAU,OAAO,GAAG;AAC3B,+BAAqB,mBAAmBE,OAAM,GAAG;AACjD,iBAAO,SAAS,KAAK,GAAG;AAAA,QAC1B,CAAC;AACD,cAAM,UAAU,CAAC,WAAW,OAAO2B,SAAQ,WAAW,CAAC3B,QAAO,QAAQ,WAAW,UAAU;AACzF,0BAAgBA,MAAK;AACrB,gBAAM,MAAM,aAAa,QAAQ,OAAO,OAAO,CAAC;AAChD,gBAAM,aAAa,eAAe2B,SAAQ,KAAK6B,aAAY;AAC3D,gBAAM,aAAa;AAAA,YACjB,QAAQ,MAAM,QAAQxD,MAAK;AAAA,YAC3B,QAAQ,4BAA4B,MAAM,IAAI,YAAY,IAAI,cAAc;AAAA,YAC5E,SAASyD,qBAAoBzD,MAAK;AAAA,UACpC;AACA,iBAAO,MAAMA,MAAK,IAAI,UAAUA,QAAO,QAAQ,YAAY,UAAU,EAAE,KAAK,YAAU;AACpF,0BAAc,QAAQA,OAAM,GAAG;AAC/B,mBAAO,OAAO,SAAS,CAAAZ,SAAO;AAC5B,yBAAW,QAAQA,KAAI,GAAG;AAAA,YAC5B,CAAC;AACD,mBAAO,OAAO,UAAU,CAAAU,UAAQ;AAC9B,0BAAY,QAAQA,MAAK,GAAG;AAAA,YAC9B,CAAC;AACD,kBAAM4D,SAAQ,uBAAuB1D,QAAO,MAAM;AAClD,gBAAI,OAAOA,MAAK,GAAG;AACjB,8BAAgBA,MAAK;AACrB,kBAAI,CAAC,UAAU;AACb,kCAAkB,QAAQA,OAAM,KAAK,MAAM;AAAA,cAC7C;AAAA,YACF;AACA,mBAAO0D,OAAM,IAAI,UAAQ;AAAA,cACvB;AAAA,cACA;AAAA,YACF,EAAE;AAAA,UACJ,CAAC,IAAI,SAAS,KAAK;AAAA,QACrB;AACA,cAAM,YAAY,QAAQ,WAAW,cAAc,MAAM,iBAAiB;AAC1E,cAAM,eAAe,QAAQ,cAAc,iBAAiB,MAAM,iBAAiB;AACnF,cAAM,qBAAqB,QAAQ,kBAAkB,QAAQ,MAAM,iBAAiB;AACpF,cAAM,oBAAoB,QAAQ,iBAAiB,QAAQ,MAAM,iBAAiB;AAClF,cAAM,wBAAwB,QAAQ,qBAAqB,QAAQ,eAAe,iBAAiB;AACnG,cAAM,uBAAuB,QAAQ,oBAAoB,QAAQ,eAAe,iBAAiB;AACjG,cAAM,eAAe,QAAQ,YAAY,QAAQ,MAAM,iBAAiB;AACxE,cAAM,iBAAiB,QAAQ,cAAc,QAAQ,MAAM,iBAAiB;AAC5E,cAAM,oBAAoB,QAAQ,iBAAiB,QAAQ,MAAM,iBAAiB;AAClF,cAAM,mBAAmB,QAAQ,gBAAgB,QAAQ,MAAM,iBAAiB;AAChF,cAAM,oBAAoB,QAAQ,iBAAiB,QAAQ,MAAM,iBAAiB;AAClF,cAAM,mBAAmB,QAAQ,gBAAgB,QAAQ,MAAM,iBAAiB;AAChF,cAAM,eAAe,QAAQ,YAAY,QAAQ,MAAM,yBAAyB;AAChF,cAAM,oBAAoB,QAAQ,iBAAiB,QAAQ,MAAM,iBAAiB;AAClF,cAAM,sBAAsB,QAAQ,mBAAmB,QAAQ,MAAM,iBAAiB;AACtF,cAAM,sBAAsB,QAAQ,mBAAmB,QAAQ,MAAM,iBAAiB;AACtF,cAAM,wBAAwB,QAAQ,qBAAqB,QAAQ,MAAM,iBAAiB;AAC1F,cAAM,mBAAmB,QAAQ,gBAAgB,QAAQ,MAAM,iBAAiB;AAChF,cAAM,iBAAiB,QAAQ,cAAc,QAAQ,MAAM,iBAAiB;AAC5E,cAAM,mBAAmB,QAAQ,gBAAgB,QAAQ,MAAM,iBAAiB;AAChF,cAAM,mBAAmB;AACzB,cAAM,kBAAkB;AACxB,cAAM,kBAAkB;AACxB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,kBAAkB;AAAA,UAClB,iBAAiB;AAAA,UACjB,qBAAqB;AAAA,UACrB,oBAAoB;AAAA,UACpB,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,iBAAiB;AAAA,UACjB,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,UACjB,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,mBAAmB;AAAA,UACnB,mBAAmB;AAAA,UACnB,qBAAqB;AAAA,UACrB,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,gBAAgB,CAAC,SAAS,UAAUnF,WAAU;AAClD,cAAM,iBAAiB,aAAa,SAAS,UAAU,CAAC;AACxD,YAAIA,WAAU,KAAK,kBAAkB,GAAG;AACtC,mBAAS,SAAS,QAAQ;AAAA,QAC5B,OAAO;AACL,gBAAM,SAAS,UAAU,KAAK,IAAIA,QAAO,cAAc,CAAC;AAAA,QAC1D;AAAA,MACF;AACA,YAAM,eAAe,CAAC,aAAa,gBAAgB,CAAAuB,UAAQ;AACzD,cAAM,SAASA,MAAK,SAASA,MAAK,UAAU;AAC5C,cAAM,WAAWA,MAAK;AACtB,eAAO,UAAU,eAAe,WAAW;AAAA,MAC7C;AACA,YAAM,mBAAmB,CAAC,OAAO,aAAa,gBAAgB;AAC5D,YAAI,UAAU,WAAW,KAAK,GAAG;AAC/B,gBAAM,aAAa,SAAS,UAAU,YAAY,KAAK,GAAG,aAAa,aAAa,WAAW,CAAC;AAChG,gBAAM,aAAa,MAAM,YAAY,OAAK;AACxC,kBAAM,YAAY,KAAK,EAAE,OAAO;AAChC,0BAAc,WAAW,QAAQ,cAAc,WAAW;AAC1D,mBAAO;AAAA,UACT,CAAC;AACD,gBAAM,eAAe,aAAa,QAAQ,UAAU;AACpD,iBAAO,cAAc,UAAU;AAC/B,iBAAO,CAAC,YAAY;AAAA,QACtB,OAAO;AACL,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AACA,YAAM,eAAe,CAAC,OAAO,aAAa,gBAAgB,MAAM,MAAM,KAAK,CAAAV,SAAO;AAChF,cAAM,cAAc,SAASA,KAAI,OAAO,aAAa,aAAa,WAAW,CAAC;AAC9E,cAAM,cAAc,MAAM,aAAa,CAAAU,UAAQ;AAC7C,gBAAM,aAAa,KAAKA,MAAK,OAAO;AACpC,wBAAc,YAAY,WAAW,cAAc,WAAW;AAC9D,iBAAO;AAAA,QACT,CAAC;AACD,cAAM,SAAS,aAAa,QAAQ,IAAI;AACxC,eAAO,QAAQ,WAAW;AAC1B,eAAO;AAAA,MACT,CAAC;AACD,YAAM,WAAW,CAACE,QAAO,WAAW;AAClC,cAAM,QAAQ,UAAU,UAAUA,MAAK;AACvC,cAAM,UAAU,gBAAgB,OAAO,MAAM;AAC7C,eAAO,QAAQ,IAAI,mBAAiB;AAClC,gBAAM,mBAAmB,cAAc,cAAc,SAAS;AAC9D,gBAAM,cAAc,cAAc,GAAG;AACrC,gBAAM,cAAc,iBAAiB,SAAS,iBAAiB;AAC/D,gBAAM,gBAAgB,iBAAiB,OAAO,aAAa,WAAW;AACtE,gBAAM,WAAW,aAAa,OAAO,aAAa,WAAW;AAC7D,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAACA,QAAO,QAAQ,eAAe;AAC9C,cAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,cAAM,UAAU,QAAQ,WAAW,MAAM;AACzC,eAAO,QAAQ,KAAK,mBAAiB;AACnC,gBAAMU,QAAO,OAAO,WAAW,YAAY,KAAK;AAChD,gBAAMxB,QAAO,mBAAmBwB,KAAI,EAAE;AACtC,gBAAM,aAAaxB,MAAK,MAAM,cAAc,GAAG,KAAK,cAAc,cAAc,SAAS,GAAG,MAAM,cAAc,cAAc,SAAS,GAAG,OAAO;AACjJ,gBAAM,eAAe,OAAO,YAAY,CAAAE,SAAO;AAC7C,kBAAM,WAAW,SAASA,KAAI,OAAO,CAAAU,UAAQ,CAACA,MAAK,QAAQ;AAC3D,mBAAO,SAAS,SAAS,IAAI,CAAC;AAAA,cAC1B,GAAGV;AAAA,cACH,OAAO;AAAA,YACT,CAAC,IAAI,CAAC;AAAA,UACV,CAAC;AACD,gBAAM,gBAAgB,aAAa,YAAY;AAC/C,iBAAO,OAAO,cAAc,SAAS,GAAG,aAAa;AAAA,QACvD,CAAC,EAAE,IAAI,mBAAiB,KAAK,aAAa,CAAC;AAAA,MAC7C;AAEA,YAAM,QAAQ,IAAI,SAAS;AAAA,QACzB,EAAE,SAAS,CAAC,KAAK,EAAE;AAAA,QACnB,EAAE,QAAQ,CAAC,OAAO,EAAE;AAAA,QACpB,EAAE,SAAS,CAAC,OAAO,EAAE;AAAA,MACvB,CAAC;AACD,YAAM,cAAc,CAAC,QAAQd,OAAMC,WAAU;AAC3C,cAAM,YAAYA,OAAM,UAAU,GAAGA,OAAM,SAAS,OAAO,MAAM;AACjE,cAAM,SAAS,WAAW,SAAS;AACnC,eAAO,cAAc,OAAO,SAAS,IAAID,MAAK,MAAM,IAAI,MAAM,QAAQC,MAAK;AAAA,MAC7E;AACA,YAAM,OAAO,CAAAA,WAAS;AACpB,YAAI,SAASA,QAAO,GAAG,GAAG;AACxB,iBAAO,YAAY,KAAK,MAAM,SAASA,MAAK;AAAA,QAC9C;AACA,YAAI,SAASA,QAAO,IAAI,GAAG;AACzB,iBAAO,YAAY,MAAM,MAAM,QAAQA,MAAK;AAAA,QAC9C;AACA,eAAO,MAAM,QAAQA,MAAK;AAAA,MAC5B;AACA,YAAM,OAAO;AAAA,QACX,GAAG;AAAA,QACH;AAAA,MACF;AAEA,YAAM,wBAAwB,CAAC,QAAQ,eAAe;AACpD,eAAO,MAAM,QAAQ,OAAK;AACxB,gBAAM,UAAU,KAAK,KAAK,CAAC;AAC3B,iBAAO,QAAQ,KAAK,MAAM;AACxB,mBAAO;AAAA,UACT,GAAG,QAAM;AACP,kBAAM,QAAQ,KAAK,aAAa;AAChC,mBAAO,QAAQ;AAAA,UACjB,GAAG,QAAM;AACP,mBAAO,KAAK;AAAA,UACd,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,mBAAmB,CAAC,QAAQ,YAAY,kBAAkB;AAC9D,cAAM,QAAQ,gBAAgB;AAC9B,eAAO,MAAM,QAAQ,OAAK;AACxB,gBAAM,UAAU,KAAK,KAAK,CAAC;AAC3B,iBAAO,QAAQ,KAAK,MAAM;AACxB,mBAAO;AAAA,UACT,GAAG,QAAM;AACP,mBAAO,KAAK,QAAQ;AAAA,UACtB,GAAG,QAAM;AACP,mBAAO,KAAK,MAAM,gBAAgB;AAAA,UACpC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,oBAAoB,CAAC,cAAcY,aAAY;AACnD,cAAM,IAAI,aAAa,KAAK,MAAM,SAAS,EAAE,GAAG,YAAU;AACxD,gBAAM,MAAM,SAASA;AACrB,iBAAO,SAAS,MAAM,IAAI;AAAA,QAC5B,GAAG,MAAM;AACP,gBAAM,MAAM,MAAMA;AAClB,iBAAO,SAAS,MAAM,GAAG;AAAA,QAC3B,CAAC;AACD,eAAO,QAAQA,UAAS,CAAC;AAAA,MAC3B;AACA,YAAM,qBAAqB,CAAC,cAAc,QAAQ,eAAe;AAC/D,eAAO,aAAa,KAAK,MAAM;AAC7B,iBAAO;AAAA,QACT,GAAG,QAAM;AACP,iBAAO,iBAAiB,QAAQ,YAAY,EAAE;AAAA,QAChD,GAAG,SAAO;AACR,iBAAO,sBAAsB,QAAQ,UAAU;AAAA,QACjD,CAAC;AAAA,MACH;AACA,YAAM,iBAAiB,CAAC,QAAQ,YAAY,aAAa;AACvD,cAAM,UAAU,KAAK,KAAK,QAAQ;AAClC,cAAM,SAAS,OAAO,QAAQ,OAAK;AACjC,iBAAO,MAAM;AAAA,QACf,CAAC,IAAI,kBAAkB,SAAS,OAAO,MAAM,IAAI,mBAAmB,SAAS,QAAQ,UAAU;AAC/F,eAAO,UAAU,MAAM;AAAA,MACzB;AACA,YAAM,MAAM,CAACwE,SAAQ5D,cAAa;AAChC,YAAI4D,QAAO,WAAW,GAAG;AACvB,iBAAO5D;AAAA,QACT;AACA,eAAO,MAAM4D,SAAQ,CAAC,MAAM,MAAM;AAChC,iBAAO,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC,GAAG,UAAU,QAAQ,IAAI;AAAA,QAC9D,GAAG,CAAC;AAAA,MACN;AACA,YAAM,YAAY,CAAC,KAAK,SAAS;AAC/B,cAAM,UAAU,KAAK,MAAM,GAAG;AAC9B,eAAO;AAAA,UACL,OAAO,UAAU;AAAA,UACjB,WAAW,MAAM;AAAA,QACnB;AAAA,MACF;AACA,YAAM,QAAQ,CAACpF,QAAO,WAAW;AAC/B,eAAO,KAAK,KAAKA,MAAK,EAAE,KAAK,SAASA,MAAK,GAAG,QAAM;AAClD,iBAAO,KAAK,SAAS;AAAA,QACvB,GAAG,QAAM;AACP,iBAAO,KAAK,SAAS;AAAA,QACvB,CAAC;AAAA,MACH;AACA,YAAM,YAAY,CAAAoF,YAAU;AAC1B,YAAIA,QAAO,WAAW,GAAG;AACvB,iBAAOA;AAAA,QACT;AACA,cAAMC,QAAO,MAAMD,SAAQ,CAAC,MAAMpF,WAAU;AAC1C,gBAAM,OAAO,KAAK,KAAKA,MAAK,EAAE,KAAK,OAAO;AAAA,YACxC,OAAAA;AAAA,YACA,WAAW;AAAA,UACb,IAAI,SAAO,UAAU,KAAK,IAAI,GAAG,UAAQ;AAAA,YACvC,OAAO,MAAM;AAAA,YACb,WAAW;AAAA,UACb,EAAE;AACF,iBAAO;AAAA,YACL,QAAQ,CAAC,KAAK,KAAK,EAAE,OAAO,KAAK,MAAM;AAAA,YACvC,WAAW,KAAK,YAAY,KAAK;AAAA,UACnC;AAAA,QACF,GAAG;AAAA,UACD,QAAQ,CAAC;AAAA,UACT,WAAW;AAAA,QACb,CAAC;AACD,cAAMG,KAAIkF,MAAK;AACf,eAAOlF,GAAE,MAAM,GAAGA,GAAE,SAAS,CAAC,EAAE,OAAO,CAAC,MAAMA,GAAEA,GAAE,SAAS,IAAI,KAAK,MAAMkF,MAAK,SAAS,CAAC,CAAC,CAAC;AAAA,MAC7F;AACA,YAAM,WAAW,KAAK;AAEtB,YAAM,kBAAkB,CAAC,WAAWvE,QAAO,SAAS;AAClD,eAAOA,QAAO,CAAAS,UAAQ;AACpB,gBAAM,SAAS,UAAU,MAAMA,MAAK,QAAQA,MAAK,UAAUA,MAAK,MAAM;AACtE,gBAAM,IAAI,IAAI,QAAQ,SAAS,CAAC;AAChC,gBAAMA,MAAK,SAAS,SAAS,IAAI,IAAI;AAAA,QACvC,CAAC;AAAA,MACH;AACA,YAAM,wBAAwB,CAAC,WAAWX,UAAS,SAAS;AAC1D,eAAOA,UAAS,CAAC,QAAQ,UAAU;AACjC,gBAAMoB,SAAQ,IAAI,CAAC,UAAU,MAAM,GAAG,SAAS,CAAC;AAChD,gBAAM,OAAO,SAAS,SAASA,SAAQ,IAAI;AAAA,QAC7C,CAAC;AAAA,MACH;AACA,YAAM,kBAAkB,CAAC,YAAYrB,OAAMG,QAAO,SAAS;AACzD,eAAOA,QAAO,CAAAS,UAAQ;AACpB,gBAAM,UAAU,WAAW,MAAMA,MAAK,KAAKA,MAAK,UAAUA,MAAK,GAAG;AAClE,gBAAM,IAAI,IAAI,SAAS,UAAU,CAAC;AAClC,gBAAMA,MAAK,SAAS,UAAU,IAAI,IAAI;AAAA,QACxC,CAAC;AACD,eAAOZ,OAAM,CAACE,MAAK,MAAM;AACvB,gBAAMA,KAAI,SAAS,UAAU,WAAW,EAAE;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,YAAM,UAAU,aAAW;AACzB,eAAO,SAAS,OAAO,EAAE,KAAK,SAAS,IAAI,GAAG,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC;AAAA,MAC7E;AACA,YAAM,eAAe,CAACY,QAAO,UAAU,cAAc;AACnD,cAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,cAAMd,QAAO,UAAU;AACvB,cAAMG,SAAQ,UAAU,UAAU,SAAS;AAC3C,cAAMF,WAAU,UAAU,YAAY,SAAS;AAC/C,iBAAS,KAAK,cAAY;AACxB,gBAAM,YAAY,QAAQ,QAAQ;AAClC,gBAAM,aAAa,MAAMa,MAAK;AAC9B,gBAAM,YAAY,aAAa,WAAWA,MAAK;AAC/C,gBAAM,WAAW,eAAe,WAAW,YAAY,QAAQ;AAC/D,cAAI,UAAU,WAAW,SAAS,GAAG;AACnC,kCAAsB,UAAUb,UAAS,SAAS;AAAA,UACpD,OAAO;AACL,4BAAgB,UAAUE,QAAO,SAAS;AAAA,UAC5C;AACA,gBAAMW,QAAO,SAAS,QAAQ;AAAA,QAChC,CAAC;AACD,kBAAU,KAAK,eAAa;AAC1B,gBAAM,QAAQ,QAAQ,SAAS;AAC/B,gBAAM,cAAc,MAAMA,MAAK;AAC/B,gBAAM,aAAa,cAAc,WAAWA,QAAO,MAAM;AACzD,gBAAM,YAAY,eAAe,YAAY,aAAa,SAAS;AACnE,0BAAgB,WAAWd,OAAMG,QAAO,KAAK;AAC7C,gBAAMW,QAAO,UAAU,SAAS;AAAA,QAClC,CAAC;AAAA,MACH;AACA,YAAM,kBAAkB;AACxB,YAAM,gBAAgB;AACtB,YAAM,eAAe;AAErB,YAAM,0BAA0B,aAAW;AACzC,iBAAS,SAAS,OAAO;AAAA,MAC3B;AACA,YAAM,uBAAuB,CAAAA,WAAS;AACpC,cAAM,WAAW,qBAAqBA,MAAK;AAC3C,qBAAaA,QAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,KAAK,CAAC;AAC5D,gCAAwBA,MAAK;AAAA,MAC/B;AACA,YAAM,qBAAqB,CAAAA,WAAS;AAClC,cAAM,WAAW,mBAAmBA,MAAK;AACzC,qBAAaA,QAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,KAAK,CAAC;AAC5D,gCAAwBA,MAAK;AAAA,MAC/B;AACA,YAAM,oBAAoB,CAAAA,WAAS;AACjC,iBAASA,QAAO,OAAO;AACvB,cAAMb,WAAU,UAAUa,MAAK;AAC/B,cAAM,cAAcb,SAAQ,SAAS,IAAIA,WAAU,QAAQa,MAAK;AAChE,eAAO,aAAa,CAAAF,UAAQ;AAC1B,mBAASA,OAAM,OAAO;AACtB,kCAAwBA,KAAI;AAAA,QAC9B,CAAC;AACD,gCAAwBE,MAAK;AAAA,MAC/B;AAEA,YAAM,uBAAuB;AAAA,QAC3B,QAAQ;AAAA,UACN,mBAAmB;AAAA,UACnB,SAAS;AAAA,QACX;AAAA,QACA,YAAY,EAAE,QAAQ,IAAI;AAAA,QAC1B,WAAW;AAAA,MACb;AACA,YAAM,kBAAkB,MAAM,aAAa,QAAQ,IAAI;AACvD,YAAM,YAAY,MAAM,aAAa,QAAQ,IAAI;AACjD,YAAM,cAAc,MAAM,aAAa,QAAQ,KAAK;AACpD,YAAM,YAAY,CAACb,UAAS,YAAY,eAAe,aAAa;AAClE,cAAM,KAAK,aAAa,QAAQ,IAAI;AACpC,iBAAS,IAAI,GAAG,IAAIA,UAAS,KAAK;AAChC,gBAAM,KAAK,WAAW,cAAc,IAAI,gBAAgB,gBAAgB,IAAI,UAAU;AACtF,cAAI,IAAI,eAAe;AACrB,kBAAM,IAAI,SAAS,KAAK;AAAA,UAC1B;AACA,cAAI,WAAW,YAAY;AACzB,kBAAM,IAAI,SAAS,KAAK;AAAA,UAC1B;AACA,mBAAS,IAAI,aAAa,QAAQ,IAAI,CAAC;AACvC,mBAAS,IAAI,EAAE;AAAA,QACjB;AACA,eAAO;AAAA,MACT;AACA,YAAM,iBAAiB,CAAAA,aAAW;AAChC,cAAM,cAAc,aAAa,QAAQ,UAAU;AACnD,gBAAQA,UAAS,MAAM,SAAS,aAAa,YAAY,CAAC,CAAC;AAC3D,eAAO;AAAA,MACT;AACA,YAAM,aAAa,CAACD,OAAMC,UAAS,YAAY,kBAAkB,QAAQD,OAAM,CAAAR,OAAK,UAAUS,UAAS,YAAY,eAAeT,EAAC,CAAC;AACpI,YAAM,SAAS,CAACQ,OAAMC,UAAS,YAAY,eAAe,YAAY,aAAa,yBAAyB;AAC1G,cAAMa,SAAQ,aAAa,QAAQ,OAAO;AAC1C,cAAM,sBAAsB,eAAe;AAC3C,eAAOA,QAAO,WAAW,MAAM;AAC/B,iBAASA,QAAO,WAAW,UAAU;AACrC,YAAI,WAAW,WAAW;AACxB,mBAASA,QAAO,eAAeb,QAAO,CAAC;AAAA,QACzC;AACA,cAAM,mBAAmB,KAAK,IAAID,OAAM,UAAU;AAClD,YAAI,uBAAuB,aAAa,GAAG;AACzC,gBAAM,QAAQ,aAAa,QAAQ,OAAO;AAC1C,mBAASc,QAAO,KAAK;AACrB,gBAAM,kBAAkB,eAAe,iBAAiB,mBAAmB;AAC3E,gBAAM,YAAY,WAAW,YAAYb,UAAS,iBAAiB,aAAa;AAChF,iBAAO,OAAO,SAAS;AAAA,QACzB;AACA,cAAM,QAAQ,aAAa,QAAQ,OAAO;AAC1C,iBAASa,QAAO,KAAK;AACrB,cAAM,UAAU,sBAAsBd,QAAO,mBAAmBA;AAChE,cAAM,gBAAgB,sBAAsB,IAAI;AAChD,cAAM,YAAY,WAAW,SAASC,UAAS,eAAe,aAAa;AAC3E,eAAO,OAAO,SAAS;AACvB,eAAOa;AAAA,MACT;AAEA,YAAM,QAAQ,aAAW,QAAQ,IAAI;AACrC,YAAM,WAAW,aAAW;AAC1B,cAAM,YAAY,aAAa,QAAQ,KAAK;AAC5C,cAAM0B,SAAQ,aAAa,QAAQ,QAAQ,IAAI,UAAU,IAAI,CAAC;AAC9D,iBAAS,WAAWA,MAAK;AACzB,eAAO,MAAM,SAAS;AAAA,MACxB;AAEA,YAAM,mBAAmB,CAAC,QAAQ5B,UAAS;AACzC,eAAO,UAAU,OAAOA,MAAK,KAAK,IAAI;AACtC,eAAO,UAAU,SAAS,IAAI;AAAA,MAChC;AACA,YAAM,yBAAyB,CAAC,QAAQ,aAAa;AACnD,mBAAW,UAAU,OAAO,EAAE,KAAK,MAAM,kBAAkB,MAAM,CAAC;AAAA,MACpE;AACA,YAAM,aAAa,CAAC,QAAQE,WAAU;AACpC,eAAO,YAAYA,QAAO,IAAI,GAAG,CAAAZ,SAAO;AACtC,qBAAW,QAAQA,KAAI,GAAG;AAC1B,iBAAO,YAAYA,MAAK,OAAO,GAAG,CAAAU,UAAQ;AACxC,wBAAY,QAAQA,MAAK,GAAG;AAAA,UAC9B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,eAAe,CAAAS,WAAS,SAASA,MAAK,KAAKA,OAAM,QAAQ,GAAG,MAAM;AACxE,YAAM,SAAS,CAAC,QAAQpB,UAASD,OAAM,YAAY,eAAe;AAChE,cAAM,gBAAgB,sBAAsB,MAAM;AAClD,cAAM,UAAU;AAAA,UACd,QAAQ;AAAA,UACR,YAAY,0BAA0B,MAAM;AAAA,UAC5C,WAAW,oBAAoB,MAAM;AAAA,QACvC;AACA,eAAO,YAAY,OAAO,MAAM;AAC9B,gBAAMc,SAAQ,OAAOd,OAAMC,UAAS,YAAY,YAAY,mBAAmB,MAAM,GAAG,OAAO;AAC/F,gBAAMa,QAAO,eAAe,OAAO;AACnC,gBAAM,OAAO,SAASA,MAAK;AAC3B,iBAAO,cAAc,IAAI;AACzB,iBAAO,UAAU;AAAA,QACnB,CAAC;AACD,eAAO,WAAW,QAAQ,MAAM,GAAG,4BAA4B,EAAE,IAAI,CAAAA,WAAS;AAC5E,cAAI,oBAAoB,MAAM,GAAG;AAC/B,+BAAmBA,MAAK;AAAA,UAC1B,WAAW,wBAAwB,MAAM,GAAG;AAC1C,8BAAkBA,MAAK;AAAA,UACzB,WAAW,yBAAyB,MAAM,KAAK,aAAa,cAAc,KAAK,GAAG;AAChF,iCAAqBA,MAAK;AAAA,UAC5B;AACA,0BAAgBA,MAAK;AACrB,mBAASA,QAAO,aAAa;AAC7B,qBAAW,QAAQA,MAAK;AACxB,iCAAuB,QAAQA,MAAK;AACpC,iBAAOA,OAAM;AAAA,QACf,CAAC,EAAE,UAAU;AAAA,MACf;AACA,YAAM,cAAc,CAAC,QAAQd,OAAMC,UAAS,UAAU,CAAC,MAAM;AAC3D,cAAM,aAAa,SAAO,SAAS,GAAG,KAAK,MAAM;AACjD,YAAI,WAAWD,KAAI,KAAK,WAAWC,QAAO,GAAG;AAC3C,gBAAM,aAAa,QAAQ,cAAc;AACzC,gBAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,iBAAO,OAAO,QAAQA,UAASD,OAAM,eAAe,UAAU;AAAA,QAChE,OAAO;AACL,kBAAQ,MAAM,6FAA6F;AAC3G,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAE/D,YAAM,gBAAgB;AACtB,YAAM,eAAe,gBAAgB;AACrC,YAAM,kBAAkB,gBAAgB;AACxC,YAAM,UAAU,WAAS;AACvB,cAAM,oBAAoB,OAAO,kBAAkB,KAAK;AACxD,eAAO,MAAM,CAAC,iBAAiB,CAAC;AAAA,MAClC;AACA,YAAM,UAAU,CAAAZ,UAAQ;AACtB,YAAI;AACJ,cAAM,SAAS,KAAK,OAAO,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AACrE,eAAO,QAAQ,OAAO,UAAQ,SAAS,KAAK,KAAK,QAAQA,KAAI,CAAC,CAAC;AAAA,MACjE;AACA,YAAM,YAAY,CAAAA,UAAQ;AACxB,YAAI,QAAQA,KAAI,EAAE,OAAO,GAAG;AAC1B,iBAAO,MAAM;AAAA,QACf;AAAA,MACF;AACA,YAAM,UAAU,aAAW;AACzB,gBAAQ,KAAK,WAAW,CAAAY,UAAQ,QAAQ,EAAE,CAAC,eAAeA,MAAK,CAAC,CAAC;AAAA,MACnE;AACA,YAAM,UAAU,MAAM,QAAQ,YAAY;AAC1C,YAAM,YAAY,MAAM,UAAU,YAAY;AAC9C,YAAM,aAAa,gBAAc;AAC/B,mBAAW,KAAK,cAAc,CAAAC,aAAW,QAAQ,EAAE,CAAC,kBAAkBA,SAAQ,CAAC,CAAC;AAAA,MAClF;AACA,YAAM,aAAa,MAAM,QAAQ,eAAe;AAChD,YAAM,eAAe,MAAM,UAAU,eAAe;AAEpD,YAAM,iCAAiC,YAAU,0BAA0B,kBAAkB,MAAM,GAAG,UAAU,MAAM,CAAC,EAAE,OAAO,qBAAqB;AACrJ,YAAM,wBAAwB,YAAU,iBAAiB,kBAAkB,MAAM,GAAG,UAAU,MAAM,CAAC,EAAE,OAAO,qBAAqB;AACnI,YAAM,mBAAmB,CAAC,QAAQ,YAAY;AAC5C,cAAM,SAAS,UAAU,MAAM;AAC/B,cAAM,aAAa,MAAM,+BAA+B,MAAM,EAAE,KAAK,mBAAiB;AACpF,gBAAM,eAAe,MAAM,EAAE,OAAO,IAAI,MAAM,CAAC,EAAE,KAAK,CAAAa,WAAS;AAC7D,kBAAM,SAAS,aAAa,SAAS,EAAE;AACvC,oBAAQA,QAAO,MAAM;AACrB,qBAASA,MAAK;AACd,gBAAI,OAAO,IAAI,QAAQ,OAAO,QAAQ,CAAC,GAAG;AACxC,qBAAO,WAAW,EAAE;AACpB,qBAAO,UAAU,kBAAkB;AAAA,YACrC,OAAO;AACL,oBAAM,MAAM,OAAO,IAAI,UAAU;AACjC,kBAAI,SAAS,OAAO,KAAK,CAAC;AAC1B,kBAAI,OAAO,OAAO,KAAK,CAAC;AACxB,qBAAO,UAAU,OAAO,GAAG;AAC3B,qBAAO,YAAY;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,cAAM,gBAAgB,YAAU,+BAA+B,MAAM,EAAE,KAAK,mBAAiB;AAC3F,gBAAM,iBAAiB,wBAAwB,MAAM,KAAK,oBAAoB,MAAM,KAAK,yBAAyB,MAAM;AACxH,cAAI,CAAC,gBAAgB;AACnB,kBAAM,eAAe,MAAM,EAAE,KAAK,CAAAA,WAAS;AACzC,kBAAI,WAAW,cAAc,CAAC,gBAAgBA,MAAK,GAAG;AACpD,qCAAqBA,MAAK;AAAA,cAC5B,WAAW,WAAW,WAAW,CAAC,cAAcA,MAAK,GAAG;AACtD,mCAAmBA,MAAK;AAAA,cAC1B,WAAW,WAAW,gBAAgB,CAAC,aAAaA,MAAK,GAAG;AAC1D,kCAAkBA,MAAK;AAAA,cACzB;AACA,8BAAgBA,MAAK;AACrB,gCAAkB,QAAQA,OAAM,KAAK,iBAAiB;AAAA,YACxD,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,cAAM,mBAAmB,CAAAF,UAAQ,MAAMA,OAAM,MAAM;AACnD,cAAM,2BAA2B,YAAU,sBAAsB,MAAM,EAAE,KAAK,CAAAA,UAAQ,iBAAiBA,KAAI,EAAE,IAAI,CAAAE,WAAS,OAAOA,QAAOF,KAAI,CAAC,CAAC;AAC9I,cAAM,mBAAmB,CAAC,KAAK,UAAU;AACvC,mCAAyB,CAAAE,WAAS;AAChC,mBAAO,UAAU,OAAO,cAAc,EAAE,OAAO,MAAM,GAAGA,OAAM,GAAG;AACjE,8BAAkB,QAAQA,OAAM,KAAK,aAAa;AAAA,UACpD,CAAC;AAAA,QACH;AACA,cAAM,uBAAuB,CAAC,KAAK,UAAU;AAC3C,mCAAyB,CAAAA,WAAS;AAChC,kBAAM,gBAAgB,sBAAsB,MAAM;AAClD,kBAAM,eAAe,OAAO,eAAe,CAAAF,UAAQ,OAAO,UAAU,MAAM,kBAAkB,EAAE,OAAO,MAAM,GAAGA,MAAK,GAAG,CAAC;AACvH,kBAAM,kBAAkB,eAAe,OAAO,UAAU,SAAS,OAAO,UAAU;AAClF,mBAAO,eAAe,CAAAA,UAAQ,gBAAgB,kBAAkB,EAAE,OAAO,MAAM,GAAGA,MAAK,GAAG,CAAC;AAC3F,8BAAkB,QAAQE,OAAM,KAAK,aAAa;AAAA,UACpD,CAAC;AAAA,QACH;AACA,cAAM,gBAAgB,MAAM;AAC1B,yCAA+B,MAAM,EAAE,KAAK,mBAAiB;AAC3D,kBAAM,eAAe,MAAM,EAAE,KAAK,CAAAA,WAAS;AACzC,oBAAMA,QAAO,SAAS,EAAE,KAAK,MAAM;AACjC,sBAAM,UAAU,aAAa,QAAQ,SAAS;AAC9C,yBAAS,SAAS,aAAa,SAAS,SAAS,CAAC;AAClD,yBAASA,QAAO,SAAS,CAAC;AAC1B,uBAAO,UAAU,kBAAkB,QAAQ,KAAK,CAAC;AAAA,cACnD,GAAG,aAAW;AACZ,oBAAI,MAAM,SAAS,EAAE,aAAa,GAAG;AACnC,sBAAI,MAAMA,MAAK,EAAE,KAAK,QAAM,OAAO,UAAU,kBAAkB,GAAG,KAAK,CAAC,CAAC;AAAA,gBAC3E;AACA,yBAAS,OAAO;AAAA,cAClB,CAAC;AACD,gCAAkB,QAAQA,OAAM,KAAK,iBAAiB;AAAA,YACxD,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,cAAM,cAAc,WAAS;AAC3B,iBAAO,MAAM;AAAA,QACf;AACA,cAAM,iBAAiB,CAAC,SAAS,WAAW,UAAU,yBAAyB,CAACA,QAAO,cAAc;AACnG,gBAAM,UAAU,QAAQ,sBAAsB,MAAM,GAAGA,QAAO,SAAS;AACvE,kBAAQA,QAAO,SAAS,QAAQ,EAAE,KAAK,WAAW;AAAA,QACpD,CAAC;AACD,cAAM,mBAAmB,MAAM,yBAAyB,CAACA,QAAO,cAAc;AAC5E,gBAAM,UAAU,QAAQ,sBAAsB,MAAM,GAAGA,QAAO,SAAS;AACvE,gBAAM,aAAa,eAAe,MAAM,aAAa,QAAQ,OAAO,OAAO,CAAC,GAAG,SAAS,KAAK,CAAC;AAC9F,iBAAO,SAASA,QAAO,SAAS,UAAU;AAAA,QAC5C,CAAC;AACD,cAAM,mBAAmB,MAAM,yBAAyB,CAACA,QAAO,cAAc;AAC5E,gBAAM,UAAU,QAAQ,sBAAsB,MAAM,GAAGA,QAAO,SAAS;AACvE,iBAAO,SAASA,QAAO,OAAO;AAAA,QAChC,CAAC;AACD,cAAM,mBAAmB,CAAC,SAAS6D,aAAYA,SAAQ,EAAE,KAAK,CAAA3E,UAAQ;AACpE,gBAAM,aAAa,MAAMA,OAAM,CAAAE,SAAO,KAAKA,IAAG,CAAC;AAC/C,mCAAyB,CAACY,QAAO,cAAc;AAC7C,kBAAM,aAAa,QAAQ,aAAa,QAAQ,OAAO,OAAO,CAAC,CAAC;AAChE,kBAAM,UAAU,UAAU,sBAAsB,MAAM,GAAG,WAAW,YAAY,UAAU;AAC1F,oBAAQA,QAAO,OAAO,EAAE,KAAK,WAAW;AAAA,UAC1C,CAAC;AAAA,QACH,CAAC;AACD,cAAM,YAAY,eAAa,CAAC,KAAK,SAAS,MAAM,MAAM,MAAM,EAAE,KAAK,CAAA1B,UAAQ;AAC7E,yBAAe,UAAUA,KAAI,GAAG,KAAK,SAAS;AAAA,QAChD,CAAC;AACD,eAAO;AAAA,UACL,oBAAoB,MAAM,eAAe,QAAQ,YAAY;AAAA,UAC7D,oBAAoB,MAAM,eAAe,QAAQ,UAAU;AAAA,UAC3D,yBAAyB,MAAM,eAAe,QAAQ,gBAAgB;AAAA,UACtE,wBAAwB,MAAM,eAAe,QAAQ,eAAe;AAAA,UACpE,yBAAyB,MAAM,eAAe,QAAQ,mBAAmB;AAAA,UACzE,wBAAwB,MAAM,eAAe,QAAQ,kBAAkB;AAAA,UACvE,mBAAmB,MAAM,eAAe,QAAQ,YAAY;AAAA,UAC5D,mBAAmB,MAAM,eAAe,QAAQ,SAAS;AAAA,UACzD,gBAAgB,MAAM,iBAAiB,EAAE,KAAK,CAAAwF,eAAa;AACzD,uBAAWA,UAAS;AACpB,2BAAe,QAAQ,YAAY;AAAA,UACrC,CAAC;AAAA,UACD,gBAAgB,MAAM,iBAAiB,EAAE,KAAK,CAAAA,eAAa;AACzD,oBAAQA,UAAS;AACjB,2BAAe,QAAQ,SAAS;AAAA,UAClC,CAAC;AAAA,UACD,iBAAiB,MAAM,iBAAiB,EAAE,KAAK,CAAAA,eAAa,WAAWA,UAAS,CAAC;AAAA,UACjF,iBAAiB,MAAM,iBAAiB,EAAE,KAAK,CAAAA,eAAa,QAAQA,UAAS,CAAC;AAAA,UAC9E,wBAAwB,MAAM,iBAAiB,QAAQ,iBAAiB,UAAU;AAAA,UAClF,uBAAuB,MAAM,iBAAiB,QAAQ,gBAAgB,UAAU;AAAA,UAChF,wBAAwB,MAAM,iBAAiB,QAAQ,iBAAiB,OAAO;AAAA,UAC/E,uBAAuB,MAAM,iBAAiB,QAAQ,gBAAgB,OAAO;AAAA,UAC7E,gBAAgB;AAAA,UAChB,yBAAyB;AAAA,UACzB,qBAAqB;AAAA,UACrB,uBAAuB;AAAA,UACvB,oBAAoB,CAAC,KAAK,WAAW,cAAc,MAAM;AAAA,UACzD,kBAAkB,UAAU,CAAAxF,UAAQA,UAAS,OAAO,QAAQ,kBAAkB,QAAQ,iBAAiB;AAAA,UACvG,iBAAiB,UAAU,CAAAA,UAAQA,UAAS,OAAO,QAAQ,oBAAoB,QAAQ,mBAAmB;AAAA,UAC1G,iBAAiB,UAAU,CAAAA,UAAQ;AACjC,oBAAQA,OAAM;AAAA,cACd,KAAK;AACH,uBAAO,QAAQ;AAAA,cACjB,KAAK;AACH,uBAAO,QAAQ;AAAA,cACjB;AACE,uBAAO,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC;AAAA,QACH,GAAG,CAAC,MAAMM,UAAS,OAAO,WAAWA,OAAM,IAAI,CAAC;AAChD,eAAO,WAAW,kBAAkB,CAAC,KAAK,SAAS;AACjD,sBAAY,QAAQ,KAAK,MAAM,KAAK,SAAS,KAAK,OAAO;AAAA,QAC3D,CAAC;AACD,eAAO,WAAW,0BAA0B,CAAC,KAAK,SAAS;AACzD,gBAAM,gBAAgB,WAAS,cAAc,MAAM,YAAY,EAAE,QAAQ,KAAK,EAAE;AAChF,cAAI,CAAC,SAAS,IAAI,GAAG;AACnB;AAAA,UACF;AACA,gBAAMS,SAAQ,SAAS,sBAAsB,MAAM,GAAG,qBAAqB;AAC3E,cAAIA,OAAM,WAAW,GAAG;AACtB;AAAA,UACF;AACA,gBAAM,YAAY,SAAS,MAAM,CAACd,QAAO,UAAU,OAAO,UAAU,IAAI,cAAc,KAAK,CAAC,KAAK,SAASA,MAAK,CAAC;AAChH,cAAI,QAAQ,SAAS,GAAG;AACtB;AAAA,UACF;AACA,iBAAO,WAAW,CAACA,QAAO,UAAU;AAClC,kBAAM,aAAa,cAAc,KAAK;AACtC,mBAAOc,QAAO,CAAAS,UAAQ;AACpB,kBAAIvB,WAAU,IAAI;AAChB,uBAAO,UAAU,OAAO,YAAY,EAAE,OAAO,KAAK,GAAGuB,MAAK,KAAK,IAAI;AAAA,cACrE,OAAO;AACL,uBAAO,UAAU,MAAM,YAAY,EAAE,OAAAvB,OAAM,GAAGuB,MAAK,GAAG;AAAA,cACxD;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD,2BAAiBT,OAAM,EAAE,EAAE,KAAK,CAAAW,WAAS,kBAAkB,QAAQA,OAAM,KAAK,aAAa,CAAC;AAAA,QAC9F,CAAC;AAAA,MACH;AAEA,YAAM,wBAAwB,CAAC,QAAQ,YAAY;AACjD,cAAM,SAAS,UAAU,MAAM;AAC/B,cAAM,oBAAoB,YAAU,iBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK,CAAAF,UAAQ,MAAMA,OAAM,MAAM,EAAE,IAAI,CAAAE,WAAS;AAC5H,gBAAM,UAAU,QAAQ,sBAAsB,MAAM,GAAGA,QAAOF,KAAI;AAClE,iBAAO,OAAOE,QAAO,OAAO;AAAA,QAC9B,CAAC,CAAC,EAAE,MAAM,EAAE;AACZ,eAAO;AAAA,UACL,iBAAiB,MAAM,kBAAkB,QAAQ,eAAe;AAAA,UAChE,kBAAkB,MAAM,kBAAkB,QAAQ,gBAAgB;AAAA,UAClE,iBAAiB,MAAM,kBAAkB,QAAQ,eAAe;AAAA,QAClE,GAAG,CAAC,MAAMpB,UAAS,OAAO,qBAAqBA,OAAM,IAAI,CAAC;AAAA,MAC5D;AAEA,YAAM,QAAQ,IAAI,SAAS;AAAA,QACzB,EAAE,QAAQ,CAAC,SAAS,EAAE;AAAA,QACtB;AAAA,UACE,IAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,EAAE,OAAO,CAAC,SAAS,EAAE;AAAA,MACvB,CAAC;AACD,YAAM,SAAS,CAAC,SAAS,UAAU,MAAM,YAAY,QAAQ,KAAK,UAAU,MAAM,OAAO;AACzF,YAAM,aAAa,UAAQ,KAAK,KAAK,UAAU,UAAU,QAAQ;AACjE,YAAM,WAAW,MAAM;AACvB,YAAM,KAAK,MAAM;AACjB,YAAM,UAAU,MAAM;AACtB,YAAM,OAAO;AAAA,QACX,QAAQ;AAAA,QACR;AAAA,QACA,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ;AAEA,YAAM,WAAW,CAACkF,YAAW,UAAU;AAAA,QACrC,WAAAA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,EAAE,QAAQ,SAAS;AAEpC,YAAM,aAAa,CAAC,KAAK,YAAY;AACnC,cAAM,MAAM,IAAI,SAAS,YAAY;AACrC,YAAI,WAAW,QAAQ,GAAG;AAC1B,eAAO;AAAA,MACT;AACA,YAAM,qBAAqB,CAAC,KAAK,YAAY;AAC3C,cAAM,MAAM,IAAI,SAAS,YAAY;AACrC,gCAAwB,KAAK,OAAO;AACpC,eAAO;AAAA,MACT;AACA,YAAM,0BAA0B,CAAC,KAAK,YAAY,IAAI,mBAAmB,QAAQ,GAAG;AACpF,YAAM,WAAW,CAAC,KAAK,SAAS;AAC9B,aAAK,KAAK,OAAK;AACb,cAAI,eAAe,EAAE,GAAG;AAAA,QAC1B,GAAG,CAAC,GAAG,MAAM;AACX,cAAI,SAAS,EAAE,KAAK,CAAC;AAAA,QACvB,GAAG,OAAK;AACN,cAAI,cAAc,EAAE,GAAG;AAAA,QACzB,CAAC;AAAA,MACH;AACA,YAAM,YAAY,CAAC,KAAK,SAAS;AAC/B,aAAK,KAAK,OAAK;AACb,cAAI,aAAa,EAAE,GAAG;AAAA,QACxB,GAAG,CAAC,GAAG,MAAM;AACX,cAAI,OAAO,EAAE,KAAK,CAAC;AAAA,QACrB,GAAG,OAAK;AACN,cAAI,YAAY,EAAE,GAAG;AAAA,QACvB,CAAC;AAAA,MACH;AACA,YAAM,mBAAmB,CAAC,KAAK,WAAW,eAAe;AACvD,cAAMJ,SAAQ,IAAI,SAAS,YAAY;AACvC,iBAASA,QAAO,SAAS;AACzB,kBAAUA,QAAO,UAAU;AAC3B,eAAOA;AAAA,MACT;AACA,YAAM,gBAAgB,CAAC,KAAK,OAAO,SAAS,QAAQ,YAAY;AAC9D,cAAM,MAAM,IAAI,SAAS,YAAY;AACrC,YAAI,SAAS,MAAM,KAAK,OAAO;AAC/B,YAAI,OAAO,OAAO,KAAK,OAAO;AAC9B,eAAO;AAAA,MACT;AACA,YAAM,SAAS,WAAS;AAAA,QACtB,MAAM,KAAK;AAAA,QACX,KAAK,KAAK;AAAA,QACV,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,MACf;AACA,YAAM,iBAAiB,SAAO;AAC5B,cAAM,QAAQ,IAAI,eAAe;AACjC,cAAM,OAAO,MAAM,SAAS,IAAI,MAAM,KAAK,IAAI,sBAAsB;AACrE,eAAO,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,EAAE,IAAI,MAAM,IAAI,SAAS,KAAK;AAAA,MAC7F;AAEA,YAAM,QAAQ,IAAI,SAAS;AAAA,QACzB;AAAA,UACE,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,YAAY,CAAC,KAAKpF,OAAMoF,WAAUpF,MAAK,aAAa,QAAQoF,OAAM,cAAc,GAAGA,OAAM,aAAa,aAAa,QAAQA,OAAM,YAAY,GAAGA,OAAM,SAAS;AACrK,YAAM,YAAY,CAAC,KAAKI,eAAcA,WAAU,MAAM;AAAA,QACpD,UAAU,SAAO;AACf,iBAAO;AAAA,YACL,KAAK,SAAS,GAAG;AAAA,YACjB,KAAK,SAAS;AAAA,UAChB;AAAA,QACF;AAAA,QACA,UAAU,CAAC,WAAW,eAAe;AACnC,iBAAO;AAAA,YACL,KAAK,OAAO,MAAM,iBAAiB,KAAK,WAAW,UAAU,CAAC;AAAA,YAC9D,KAAK,OAAO,MAAM,SAAS,KAAK,iBAAiB,KAAK,YAAY,SAAS,CAAC,CAAC;AAAA,UAC/E;AAAA,QACF;AAAA,QACA,OAAO,CAAC,OAAO,SAAS,QAAQ,YAAY;AAC1C,iBAAO;AAAA,YACL,KAAK,OAAO,MAAM,cAAc,KAAK,OAAO,SAAS,QAAQ,OAAO,CAAC;AAAA,YACrE,KAAK,OAAO,MAAM,SAAS,KAAK,cAAc,KAAK,QAAQ,SAAS,OAAO,OAAO,CAAC,CAAC;AAAA,UACtF;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,aAAa,CAAC,KAAK,WAAW;AAClC,cAAM,MAAM,OAAO,IAAI;AACvB,YAAI,IAAI,WAAW;AACjB,gBAAM,WAAW,OAAO,IAAI,EAAE,OAAO,SAAO,IAAI,cAAc,KAAK;AACnE,iBAAO,SAAS,IAAI,SAAO,MAAM,IAAI,aAAa,QAAQ,IAAI,YAAY,GAAG,IAAI,WAAW,aAAa,QAAQ,IAAI,cAAc,GAAG,IAAI,WAAW,CAAC,EAAE,WAAW,MAAM,UAAU,KAAK,MAAM,KAAK,GAAG,CAAC;AAAA,QACzM,OAAO;AACL,iBAAO,UAAU,KAAK,MAAM,KAAK,GAAG;AAAA,QACtC;AAAA,MACF;AACA,YAAM,WAAW,CAAC,KAAKA,eAAc;AACnC,cAAM,SAAS,UAAU,KAAKA,UAAS;AACvC,eAAO,WAAW,KAAK,MAAM;AAAA,MAC/B;AACA,YAAM,aAAa,CAAC,KAAKA,eAAc;AACrC,cAAM,YAAY,SAAS,KAAKA,UAAS;AACzC,eAAO,UAAU,MAAM;AAAA,UACrB,KAAK,CAAC,OAAO,SAAS,QAAQ,YAAY;AACxC,kBAAM,MAAM,IAAI,SAAS,YAAY;AACrC,gBAAI,SAAS,MAAM,KAAK,OAAO;AAC/B,gBAAI,OAAO,OAAO,KAAK,OAAO;AAC9B,mBAAO;AAAA,UACT;AAAA,UACA,KAAK,CAAC,OAAO,SAAS,QAAQ,YAAY;AACxC,kBAAM,MAAM,IAAI,SAAS,YAAY;AACrC,gBAAI,SAAS,OAAO,KAAK,OAAO;AAChC,gBAAI,OAAO,MAAM,KAAK,OAAO;AAC7B,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM;AACN,YAAM;AAEN,YAAM,WAAW,CAAC,OAAO,SAAS,QAAQ,aAAa;AAAA,QACrD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,EAAE,QAAQ,SAAS;AAEpC,YAAM,WAAW,CAAC,OAAO,SAAS,QAAQ,YAAY;AACpD,eAAO;AAAA,UACL,OAAO,KAAK,GAAG,OAAO,OAAO;AAAA,UAC7B,QAAQ,KAAK,GAAG,QAAQ,OAAO;AAAA,QACjC;AAAA,MACF;AACA,YAAM,QAAQ,EAAE,QAAQ,SAAS;AAEjC,YAAM,iBAAiB,CAAC,KAAKA,eAAc;AACzC,cAAM,MAAM,WAAW,KAAKA,UAAS;AACrC,eAAO,SAAS,OAAO,aAAa,QAAQ,IAAI,cAAc,GAAG,IAAI,aAAa,aAAa,QAAQ,IAAI,YAAY,GAAG,IAAI,SAAS;AAAA,MACzI;AACA,YAAM,YAAY,MAAM;AAExB,YAAM,OAAO,CAAC,WAAW,QAAQ,OAAO,SAAS,QAAQ,SAAS,gBAAgB;AAChF,YAAI,EAAE,KAAK,OAAO,MAAM,KAAK,YAAY,UAAU;AACjD,iBAAO,UAAU,OAAO,SAAS,MAAM,EAAE,KAAK,OAAK;AACjD,mBAAO,UAAU,QAAQ,SAAS,MAAM,EAAE,KAAK,OAAK;AAClD,qBAAO,OAAO,WAAW,QAAQ,GAAG,GAAG,WAAW;AAAA,YACpD,CAAC;AAAA,UACH,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,SAAS,CAAC,WAAW,QAAQ,OAAO,QAAQ,gBAAgB;AAChE,YAAI,CAAC,KAAK,OAAO,MAAM,GAAG;AACxB,iBAAO,SAAS,OAAO,QAAQ,MAAM,EAAE,KAAK,aAAW;AACrD,kBAAM,QAAQ,QAAQ,MAAM,MAAM,CAAC,CAAC;AACpC,gBAAI,MAAM,SAAS,GAAG;AACpB,0BAAY,WAAW,OAAO,QAAQ,OAAO,QAAQ,MAAM;AAC3D,qBAAO,SAAS,KAAK,SAAS,OAAO,SAAS,KAAK,UAAU,OAAO,GAAG,OAAO,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;AAAA,YACtG,OAAO;AACL,qBAAO,SAAS,KAAK;AAAA,YACvB;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,SAAS,CAAC5E,OAAMC,UAAS,WAAW,UAAU,gBAAgB;AAClE,cAAM,kBAAkB,aAAW;AACjC,sBAAY,kBAAkB,SAAS;AACvC,sBAAY,YAAY,WAAW,QAAQ,OAAO,QAAQ,OAAO,QAAQ,MAAM;AAC/E,iBAAO,QAAQ;AAAA,QACjB;AACA,eAAO,eAAe,UAAUD,OAAMC,UAAS,YAAY,uBAAuB,YAAY,oBAAoB,EAAE,IAAI,eAAe;AAAA,MACzI;AAEA,YAAM,WAAW,CAAC,MAAM,UAAU;AAAA,QAChC;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,CAAC6C,WAAU,MAAM,YAAY,aAAa,aAAa;AACvE,eAAOA,UAAS,SAAS,EAAE,OAAO,IAAI,EAAE,IAAI,OAAK;AAC/C,iBAAO,SAAS,GAAG,UAAU;AAAA,QAC/B,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAACA,WAAU,MAAM,WAAW,aAAa,YAAY;AACpE,eAAO,UAAU,QAAQA,WAAU,IAAI,EAAE,IAAI,OAAK;AAChD,iBAAO,SAAS,GAAG,UAAU;AAAA,QAC/B,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAACA,WAAU,MAAM,WAAW,aAAa,YAAY;AACnE,cAAMhD,YAAWgD,UAAS,SAAS,EAAE,SAAS,IAAI;AAClD,cAAM,SAAS,UAAU,MAAMhD,SAAQ;AACvC,eAAO,OAAO,IAAI,CAAAN,OAAK;AACrB,iBAAO,SAASA,IAAG,UAAU;AAAA,QAC/B,CAAC;AAAA,MACH;AACA,YAAM,aAAa;AAAA,QACjB;AAAA,UACE,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU,SAAS,KAAK;AAAA,QAC1B;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU,SAAS,KAAK,SAAS;AAAA,QACnC;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU,SAAS,KAAK,QAAQ;AAAA,QAClC;AAAA,MACF;AACA,YAAM,KAAK,CAACsD,WAAU,MAAM,MAAM,WAAW,QAAQ,eAAe;AAClE,cAAM,UAAU,OAAO,OAAO,UAAQ;AACpC,iBAAO,KAAK,YAAY;AAAA,QAC1B,CAAC;AACD,eAAO,QAAQ,KAAK,UAAQ;AAC1B,iBAAO,KAAK,QAAQA,WAAU,MAAM,WAAW,KAAK,IAAI,EAAE,QAAQ,MAAM;AACtE,mBAAO,KAAK,SAAS,KAAK,QAAM;AAC9B,qBAAO,GAAGA,WAAU,MAAM,IAAI,SAAS;AAAA,YACzC,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,YAAM,SAAS,MAAM;AACnB,cAAM,UAAU,CAACA,WAAU,SAAS;AAClC,iBAAOA,UAAS,MAAM,EAAE,YAAY,IAAI;AAAA,QAC1C;AACA,cAAMH,SAAQ,CAAA7C,cAAY;AACxB,iBAAOA,UAAS,SAAS,IAAI,SAAS,KAAKA,UAASA,UAAS,SAAS,EAAE,IAAI,SAAS,KAAK;AAAA,QAC5F;AACA,eAAO;AAAA,UACL;AAAA,UACA,OAAA6C;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU,MAAM;AACpB,cAAM,UAAU,CAACG,WAAU,SAAS;AAClC,iBAAOA,UAAS,MAAM,EAAE,YAAY,IAAI;AAAA,QAC1C;AACA,cAAMH,SAAQ,CAAA7C,cAAY;AACxB,iBAAOA,UAAS,SAAS,IAAI,SAAS,KAAKA,UAAS,EAAE,IAAI,SAAS,KAAK;AAAA,QAC1E;AACA,eAAO;AAAA,UACL;AAAA,UACA,OAAA6C;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU;AAAA,QACd,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAEA,YAAM,OAAO,CAACG,WAAU,MAAM,WAAW,MAAM,WAAW,WAAW;AACnE,cAAM,OAAO,GAAGA,WAAU,MAAM,MAAM,SAAS;AAC/C,eAAO,KAAK,KAAK,OAAK;AACpB,cAAI,OAAO,EAAE,IAAI,GAAG;AAClB,mBAAO,SAAS,KAAK;AAAA,UACvB,OAAO;AACL,mBAAO,UAAU,EAAE,IAAI,IAAI,SAAS,KAAK,EAAE,IAAI,IAAI,KAAKA,WAAU,EAAE,MAAM,WAAW,EAAE,MAAM,WAAW,MAAM;AAAA,UAChH;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,OAAO,CAACA,WAAU,MAAM,WAAW,WAAW;AAClD,eAAO,KAAKA,WAAU,MAAM,WAAW,UAAU,QAAQ,KAAK,GAAG,MAAM;AAAA,MACzE;AACA,YAAM,QAAQ,CAACA,WAAU,MAAM,WAAW,WAAW;AACnD,eAAO,KAAKA,WAAU,MAAM,WAAW,UAAU,QAAQ,MAAM,GAAG,MAAM;AAAA,MAC1E;AAEA,YAAM,SAAS,CAAAA,cAAY,aAAWA,UAAS,SAAS,EAAE,SAAS,OAAO,EAAE,WAAW;AACvF,YAAM,WAAW,CAACA,WAAU,MAAM,WAAW;AAC3C,eAAO,WAAWA,WAAU,MAAM,OAAOA,SAAQ,GAAG,MAAM;AAAA,MAC5D;AACA,YAAM,UAAU,CAACA,WAAU,MAAM,WAAW;AAC1C,eAAO,YAAYA,WAAU,MAAM,OAAOA,SAAQ,GAAG,MAAM;AAAA,MAC7D;AACA,YAAM,aAAa;AACnB,YAAM,cAAc;AAEpB,YAAM,WAAW,YAAY;AAC7B,YAAM,SAAS,CAAC,SAAS,WAAW;AAClC,eAAO,SAAS,UAAU,SAAS,MAAM;AAAA,MAC3C;AACA,YAAM,UAAU,CAAC,SAAS,WAAW;AACnC,eAAO,QAAQ,UAAU,SAAS,MAAM;AAAA,MAC1C;AACA,YAAM,WAAW,CAAC,SAAS,WAAW,WAAW;AAC/C,eAAO,WAAW,UAAU,SAAS,WAAW,MAAM;AAAA,MACxD;AACA,YAAM,YAAY,CAAC,SAAS,WAAW,WAAW;AAChD,eAAO,YAAY,UAAU,SAAS,WAAW,MAAM;AAAA,MACzD;AAEA,YAAM,WAAW,CAAC,OAAO,WAAW,WAAW,WAAW,OAAO,WAAW,MAAM,EAAE,OAAO;AAE3F,YAAM,QAAQ,IAAI,SAAS;AAAA,QACzB,EAAE,MAAM,CAAC,SAAS,EAAE;AAAA,QACpB,EAAE,SAAS,CAAC,EAAE;AAAA,QACd,EAAE,UAAU,CAAC,MAAM,EAAE;AAAA,QACrB,EAAE,YAAY,CAAC,MAAM,EAAE;AAAA,MACzB,CAAC;AACD,YAAM,gBAAgB,CAAC,QAAQxB,SAAQC,WAAU;AAC/C,cAAM,eAAe,OAAO,QAAQD,OAAM;AAC1C,cAAM,cAAc,OAAO,QAAQC,MAAK;AACxC,eAAO,YAAY,QAAQ,aAAa,QAAQ,YAAY,OAAO,aAAa;AAAA,MAClF;AACA,YAAM,QAAQ,UAAQ;AACpB,eAAO,UAAU,MAAM,IAAI;AAAA,MAC7B;AACA,YAAM,SAAS,CAAC,QAAQD,SAAQ,cAAcC,QAAO,aAAa,SAAS,WAAW;AACpF,eAAO,UAAUA,QAAO,SAAS,MAAM,EAAE,KAAK,eAAa;AACzD,iBAAO,UAAUD,SAAQ,SAAS,MAAM,EAAE,IAAI,gBAAc;AAC1D,gBAAI,CAAC,KAAK,WAAW,UAAU,GAAG;AAChC,qBAAO,UAAU,OAAO;AAAA,gBACtB;AAAA,gBACA;AAAA,cACF,CAAC,EAAE,KAAK,MAAM;AACZ,uBAAO,cAAc,QAAQ,YAAY,SAAS,IAAI,MAAM,QAAQ,IAAI,QAAQ,UAAU;AAAA,cAC5F,GAAG,gBAAc;AACf,uBAAO,QAAQ,UAAU;AAAA,cAC3B,CAAC;AAAA,YACH,OAAO;AACL,qBAAO,KAAKC,QAAO,SAAS,KAAK,OAAO,SAAS,MAAM,cAAc,QAAQ,UAAU,IAAI,MAAM,KAAK,cAAc;AAAA,YACtH;AAAA,UACF,CAAC;AAAA,QACH,CAAC,EAAE,MAAM,MAAM,KAAK,SAAS,CAAC;AAAA,MAChC;AACA,YAAM,OAAO,CAAC,SAAS,QAAQ,WAAW,YAAY,iBAAiB;AACrE,eAAO,QAAQ,KAAK,QAAQ,WAAW,YAAY,YAAY;AAAA,MACjE;AACA,YAAM,cAAc;AAAA,QAClB,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW,CAAC1B,SAAQC,WAAU,SAAS,WAAW;AAAA,QACtD,QAAAD;AAAA,QACA,UAAAC;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,gBAAgB,aAAW,OAAO,OAAO,EAAE,KAAK,CAAAD,YAAU;AAC9D,cAAMC,YAAW,WAAWD,OAAM;AAClC,eAAO,QAAQC,WAAU,OAAO,EAAE,IAAI,WAAS,SAASD,SAAQC,WAAU,SAAS,KAAK,CAAC;AAAA,MAC3F,CAAC;AACD,YAAM,UAAU,CAAC,UAAU,YAAY,UAAU,UAAU,MAAM,MAAM,OAAO,CAAC;AAE/E,YAAM,OAAO,MAAM,IAAI;AACvB,YAAM,WAAW,CAAC,MAAM,QAAQ,WAAW;AACzC,eAAO,OAAO,MAAM,MAAM,EAAE,KAAK,YAAU;AACzC,iBAAO,OAAO,MAAM,KAAK,MAAM,MAAM,EAAE,KAAK,EAAE,WAAW,IAAI,SAAS,QAAQ,QAAQ,MAAM,IAAI,SAAS,KAAK,MAAM;AAAA,QACtH,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,QAAQ,SAAS,cAAc;AAC/C,eAAO,UAAU,SAAS,OAAO,EAAE,QAAQ,MAAM;AAC/C,iBAAO,SAAS,SAAS,UAAU,QAAQ,MAAM;AAAA,QACnD,CAAC,EAAE,IAAI,UAAU,QAAQ;AAAA,MAC3B;AACA,YAAM,SAAS,CAAC,SAAS,WAAW;AAClC,eAAO,QAAQ,SAAS,MAAM,EAAE,OAAO,IAAI,EAAE,QAAQ,MAAM;AACzD,iBAAO,QAAQ,SAAS,SAAS,CAAC,EAAE,OAAO,IAAI;AAAA,QACjD,CAAC;AAAA,MACH;AACA,YAAM,eAAe,CAAC,QAAQ,SAAS,QAAQ,cAAc;AAC3D,eAAO,OAAO,SAAS,MAAM,EAAE,KAAK,QAAM;AACxC,iBAAO,UAAU,SAAS,EAAE,EAAE,KAAK,MAAM;AACvC,mBAAO,SAAS,IAAI,UAAU,QAAQ,MAAM,EAAE,IAAI,UAAU,QAAQ;AAAA,UACtE,GAAG,cAAY;AACb,mBAAO,cAAc,QAAQ,EAAE,IAAI,UAAQ;AACzC,qBAAO,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;AAAA,YACxC,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,CAAC,QAAQ,SAAS,QAAQ,cAAc;AACpD,cAAM,SAAS,KAAK,OAAO,IAAI,SAAS,QAAQ,SAAS,SAAS,IAAI,aAAa,QAAQ,SAAS,QAAQ,SAAS;AACrH,eAAO,OAAO,IAAI,SAAO;AACvB,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,UAAU,cAAY;AAC1B,eAAO,YAAY,KAAK,UAAU,cAAY;AAC5C,iBAAO,SAAS,KAAK;AAAA,QACvB,GAAG,MAAM;AACP,iBAAO,SAAS,KAAK;AAAA,QACvB,GAAG,CAAAc,UAAQ;AACT,iBAAO,SAAS,KAAK,MAAMA,OAAM,CAAC,CAAC;AAAA,QACrC,GAAG,CAAAA,UAAQ;AACT,iBAAO,SAAS,KAAK,MAAMA,OAAM,OAAOA,KAAI,CAAC,CAAC;AAAA,QAChD,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,OAAO,WAAW;AAClC,eAAO;AAAA,UACL,MAAM,MAAM;AAAA,UACZ,KAAK,MAAM,MAAM;AAAA,UACjB,OAAO,MAAM;AAAA,UACb,QAAQ,MAAM,SAAS;AAAA,QACzB;AAAA,MACF;AACA,YAAM,SAAS,CAAC,OAAO,WAAW;AAChC,eAAO;AAAA,UACL,MAAM,MAAM;AAAA,UACZ,KAAK,MAAM,MAAM;AAAA,UACjB,OAAO,MAAM;AAAA,UACb,QAAQ,MAAM,SAAS;AAAA,QACzB;AAAA,MACF;AACA,YAAM,YAAY,CAAC,OAAO,QAAQ,WAAW;AAC3C,eAAO;AAAA,UACL,MAAM,MAAM,OAAO;AAAA,UACnB,KAAK,MAAM,MAAM;AAAA,UACjB,OAAO,MAAM,QAAQ;AAAA,UACrB,QAAQ,MAAM,SAAS;AAAA,QACzB;AAAA,MACF;AACA,YAAM,SAAS,WAAS;AACtB,eAAO,MAAM;AAAA,MACf;AACA,YAAM,YAAY,WAAS;AACzB,eAAO,MAAM;AAAA,MACf;AAEA,YAAM,gBAAgB,CAAC,QAAQ,SAAS,WAAW;AACjD,YAAI,UAAU,KAAK,SAAS,OAAO,OAAO,GAAG;AAC3C,iBAAO,OAAO,cAAc,SAAS,QAAQ,SAAS,SAAS,CAAC;AAAA,QAClE,WAAW,SAAS,GAAG;AACrB,iBAAO,OAAO,cAAc,SAAS,SAAS,GAAG,SAAS,MAAM;AAAA,QAClE;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,UAAU,WAAS;AAAA,QACvB,MAAM,KAAK;AAAA,QACX,KAAK,KAAK;AAAA,QACV,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,MACf;AACA,YAAM,aAAa,CAAC,QAAQ,YAAY;AACtC,eAAO,SAAS,KAAK,OAAO,QAAQ,OAAO,CAAC;AAAA,MAC9C;AACA,YAAM,WAAW,CAAC,QAAQ,SAAS,WAAW;AAC5C,YAAI,UAAU,OAAO,GAAG;AACtB,iBAAO,WAAW,QAAQ,OAAO,EAAE,IAAI,OAAO;AAAA,QAChD,WAAW,OAAO,OAAO,GAAG;AAC1B,iBAAO,cAAc,QAAQ,SAAS,MAAM,EAAE,IAAI,OAAO;AAAA,QAC3D,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,eAAe,CAAC,QAAQ,YAAY;AACxC,YAAI,UAAU,OAAO,GAAG;AACtB,iBAAO,WAAW,QAAQ,OAAO,EAAE,IAAI,OAAO;AAAA,QAChD,WAAW,OAAO,OAAO,GAAG;AAC1B,iBAAO,OAAO,cAAc,SAAS,GAAG,SAAS,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO;AAAA,QAC/E,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AAEA,YAAM,YAAY;AAClB,YAAM,cAAc;AACpB,YAAM,QAAQ,IAAI,SAAS;AAAA,QACzB,EAAE,MAAM,CAAC,EAAE;AAAA,QACX,EAAE,OAAO,CAAC,OAAO,EAAE;AAAA,MACrB,CAAC;AACD,YAAM,YAAY,CAAC,OAAO,QAAQ;AAChC,eAAO,MAAM,OAAO,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,MAAM,IAAI,IAAI,KAAK,MAAM,OAAO,IAAI;AAAA,MAC3F;AACA,YAAM,iBAAiB,CAAC,QAAQ,SAAS,UAAU;AACjD,eAAO,UAAU,SAAS,OAAO,EAAE,KAAK,OAAO,CAAAA,UAAQ;AACrD,iBAAO,aAAa,QAAQA,KAAI,EAAE,OAAO,SAAO;AAC9C,mBAAO,UAAU,OAAO,GAAG;AAAA,UAC7B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,QAAQ,SAAS,UAAU,UAAU,UAAU;AACjE,cAAM,aAAa,SAAS,OAAO,SAAS;AAC5C,YAAI,KAAK,IAAI,SAAS,SAAS,SAAS,MAAM,IAAI,GAAG;AACnD,iBAAO,MAAM,MAAM,UAAU;AAAA,QAC/B,WAAW,SAAS,MAAM,MAAM,QAAQ;AACtC,iBAAO,MAAM,MAAM,UAAU;AAAA,QAC/B,WAAW,SAAS,QAAQ,MAAM,QAAQ;AACxC,iBAAO,MAAM,MAAM,SAAS,OAAO,CAAC,CAAC;AAAA,QACvC,OAAO;AACL,iBAAO,eAAe,QAAQ,SAAS,KAAK,IAAI,MAAM,MAAM,UAAU,YAAY,WAAW,CAAC,CAAC,IAAI,MAAM,KAAK;AAAA,QAChH;AAAA,MACF;AACA,YAAM,WAAW,CAAC,QAAQ,SAAS,UAAU,UAAU,UAAU;AAC/D,cAAM,cAAc,OAAO,OAAO,SAAS;AAC3C,YAAI,KAAK,IAAI,SAAS,MAAM,SAAS,GAAG,IAAI,GAAG;AAC7C,iBAAO,MAAM,MAAM,WAAW;AAAA,QAChC,WAAW,SAAS,SAAS,MAAM,KAAK;AACtC,iBAAO,MAAM,MAAM,WAAW;AAAA,QAChC,WAAW,SAAS,WAAW,MAAM,KAAK;AACxC,iBAAO,MAAM,MAAM,OAAO,OAAO,CAAC,CAAC;AAAA,QACrC,OAAO;AACL,iBAAO,eAAe,QAAQ,SAAS,KAAK,IAAI,MAAM,MAAM,UAAU,aAAa,WAAW,CAAC,CAAC,IAAI,MAAM,KAAK;AAAA,QACjH;AAAA,MACF;AACA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,MACV;AACA,YAAM,eAAe;AAAA,QACnB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,MACV;AACA,YAAM,YAAY,CAAC,QAAQ,GAAG,MAAM;AAClC,eAAO,OAAO,iBAAiB,GAAG,CAAC,EAAE,OAAO,SAAO;AACjD,iBAAO,KAAK,GAAG,MAAM;AAAA,QACvB,CAAC,EAAE,OAAO;AAAA,MACZ;AACA,YAAM,iBAAiB,CAAC,QAAQ,UAAU,UAAU,OAAO,eAAe;AACxE,eAAO,UAAU,QAAQ,UAAU,UAAU,SAAS,KAAK,OAAO,SAAS,GAAG,UAAU;AAAA,MAC1F;AACA,YAAM,YAAY,CAAC,QAAQ,UAAU,UAAU,OAAO,eAAe;AACnE,YAAI,eAAe,GAAG;AACpB,iBAAO,SAAS,KAAK,KAAK;AAAA,QAC5B;AACA,YAAI,UAAU,QAAQ,MAAM,MAAM,SAAS,MAAM,KAAK,CAAC,GAAG;AACxD,iBAAO,eAAe,QAAQ,UAAU,UAAU,OAAO,aAAa,CAAC;AAAA,QACzE;AACA,eAAO,OAAO,eAAe,MAAM,MAAM,SAAS,MAAM,KAAK,CAAC,EAAE,KAAK,WAAS;AAC5E,iBAAO,MAAM,MAAM,KAAK,SAAS,MAAM,aAAW;AAChD,mBAAO,aAAa,QAAQ,OAAO,EAAE,KAAK,cAAY;AACpD,qBAAO,SAAS,SAAS,QAAQ,SAAS,UAAU,UAAU,KAAK,EAAE,KAAK,SAAS,MAAM,cAAY;AACnG,uBAAO,UAAU,QAAQ,UAAU,UAAU,UAAU,aAAa,CAAC;AAAA,cACvE,CAAC;AAAA,YACH,CAAC,EAAE,QAAQ,MAAM;AACf,qBAAO,SAAS,KAAK,KAAK;AAAA,YAC5B,CAAC;AAAA,UACH,GAAG,SAAS,IAAI;AAAA,QAClB,CAAC;AAAA,MACH;AACA,YAAM,cAAc,CAAC,UAAU,UAAU,WAAW;AAClD,YAAI,SAAS,MAAM,QAAQ,IAAI,OAAO,eAAe,GAAG;AACtD,iBAAO,SAAS,KAAK,SAAS,MAAM,QAAQ,IAAI,OAAO,eAAe,CAAC;AAAA,QACzE,WAAW,SAAS,MAAM,QAAQ,IAAI,GAAG;AACvC,iBAAO,SAAS,KAAK,CAAC,SAAS,MAAM,QAAQ,CAAC;AAAA,QAChD,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,UAAU,QAAQ,UAAU;AACzC,cAAM,QAAQ,SAAS,KAAK,OAAO,SAAS;AAC5C,cAAM,WAAW,UAAU,QAAQ,UAAU,OAAO,OAAO,WAAW,EAAE,MAAM,KAAK;AACnF,eAAO,YAAY,UAAU,UAAU,MAAM,EAAE,KAAK,MAAM;AACxD,iBAAO,OAAO,eAAe,SAAS,MAAM,SAAS,MAAM,QAAQ,CAAC;AAAA,QACtE,GAAG,WAAS;AACV,iBAAO,SAAS,GAAG,KAAK;AACxB,iBAAO,OAAO,eAAe,SAAS,MAAM,SAAS,MAAM,QAAQ,IAAI,KAAK;AAAA,QAC9E,CAAC;AAAA,MACH;AACA,YAAM,UAAU;AAAA,QACd,OAAO,MAAM,OAAO,UAAU;AAAA,QAC9B,SAAS,MAAM,OAAO,YAAY;AAAA,QAClC,aAAa,SAAS,SAAS;AAAA,MACjC;AAEA,YAAM,cAAc;AACpB,YAAM,WAAW,CAAC,QAAQ,QAAQ,cAAc;AAC9C,eAAO,OAAO,aAAa,EAAE,KAAK,SAAO;AACvC,iBAAO,MAAM,QAAQ,IAAI,QAAQ,IAAI,SAAS,SAAS,EAAE,KAAK,MAAM;AAClE,mBAAO,SAAS,KAAK,MAAM,IAAI,QAAQ,IAAI,OAAO,CAAC;AAAA,UACrD,GAAG,iBAAe;AAChB,kBAAM4D,SAAQ,OAAO,UAAU,WAAW;AAC1C,kBAAM,WAAW,YAAY,OAAO,QAAQ,IAAI,QAAQ,IAAI,SAASA,OAAM,QAAQA,OAAM,SAAS,UAAU,SAAS,MAAM;AAC3H,mBAAO,QAAQ,QAAQ;AAAA,UACzB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,OAAO,CAAC,QAAQ,QAAQ,SAAS,QAAQ,WAAW,eAAe;AACvE,YAAI,eAAe,GAAG;AACpB,iBAAO,SAAS,KAAK;AAAA,QACvB;AACA,eAAO,UAAU,QAAQ,QAAQ,SAAS,QAAQ,SAAS,EAAE,KAAK,WAAS;AACzE,gBAAMA,SAAQ,OAAO,UAAU,KAAK;AACpC,gBAAM,WAAW,YAAY,OAAO,QAAQ,SAAS,QAAQA,OAAM,QAAQA,OAAM,SAAS,UAAU,SAAS,MAAM;AACnH,iBAAO,YAAY,KAAK,UAAU,MAAM;AACtC,mBAAO,SAAS,KAAK;AAAA,UACvB,GAAG,MAAM;AACP,mBAAO,SAAS,KAAK,KAAK;AAAA,UAC5B,GAAG,CAAA5D,UAAQ;AACT,gBAAI,KAAK,SAASA,KAAI,KAAK,WAAW,GAAG;AACvC,qBAAO,SAAS,QAAQ,SAAS,QAAQ,QAAQ,SAAS;AAAA,YAC5D,OAAO;AACL,qBAAO,KAAK,QAAQ,QAAQA,OAAM,GAAG,WAAW,aAAa,CAAC;AAAA,YAChE;AAAA,UACF,GAAG,CAAAA,UAAQ;AACT,gBAAI,KAAK,SAASA,KAAI,KAAK,WAAW,OAAOA,KAAI,GAAG;AAClD,qBAAO,SAAS,QAAQ,SAAS,QAAQ,UAAU,SAAS;AAAA,YAC9D,OAAO;AACL,qBAAO,KAAK,QAAQ,QAAQA,OAAM,OAAOA,KAAI,GAAG,WAAW,aAAa,CAAC;AAAA,YAC3E;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,QAAQ,SAAS,QAAQ,MAAM,cAAc;AAC7D,eAAO,SAAS,QAAQ,SAAS,MAAM,EAAE,KAAK,SAAO;AACnD,iBAAO,MAAM,QAAQ,WAAW,KAAK,KAAK,QAAQ,YAAY,CAAC,CAAC;AAAA,QAClE,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,CAAC,QAAQ,WAAW,QAAQ;AACxC,cAAM,UAAU,SAAS,EAAE;AAC3B,YAAI,QAAQ,WAAW,KAAK,QAAQ,SAAS,KAAK,QAAQ,UAAU,GAAG;AACrE,iBAAO,UAAU,MAAM,QAAQ,GAAG;AAAA,QACpC,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,YAAY,CAAC,QAAQ,QAAQ,SAAS,QAAQ,cAAc;AAChE,eAAO,SAAS,QAAQ,SAAS,MAAM,EAAE,KAAK,SAAO;AACnD,iBAAO,MAAM,QAAQ,WAAW,GAAG;AAAA,QACrC,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,QAAQ,QAAQ,cAAc;AAC9C,eAAO,SAAS,QAAQ,QAAQ,SAAS,EAAE,KAAK,UAAQ;AACtD,iBAAO,KAAK,QAAQ,QAAQ,KAAK,SAAS,KAAK,QAAQ,WAAW,WAAW,EAAE,IAAI,OAAO,SAAS;AAAA,QACrG,CAAC;AAAA,MACH;AAEA,YAAM,cAAc,CAAC,MAAME,WAAU;AACnC,eAAO,SAAS,MAAM,OAAK;AACzB,iBAAO,OAAO,CAAC,EAAE,OAAO,OAAK;AAC3B,mBAAO,KAAK,GAAGA,MAAK;AAAA,UACtB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,QAAQ,QAAQ,WAAW,SAAS,WAAW;AAC/D,eAAO,UAAU,SAAS,SAAS,MAAM,EAAE,KAAK,WAAS;AACvD,iBAAO,UAAU,OAAO,SAAS,MAAM,EAAE,KAAK,CAAAA,WAAS;AACrD,gBAAI,CAAC,YAAY,QAAQA,MAAK,GAAG;AAC/B,qBAAO,SAAS,KAAK;AAAA,YACvB;AACA,mBAAO,SAAS,QAAQ,QAAQ,SAAS,EAAE,KAAK,CAAA0D,WAAS;AACvD,qBAAO,UAAUA,OAAM,QAAQ,SAAS,MAAM,EAAE,IAAI,YAAU;AAC5D,uBAAO;AAAA,kBACL;AAAA,kBACA;AAAA,kBACA,OAAAA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,QAAQ,QAAQ,WAAW,SAAS,QAAQ,aAAa;AACzE,eAAO,SAAS,SAAS,MAAM,EAAE,QAAQ,MAAM;AAC7C,iBAAO,SAAS,QAAQ,QAAQ,WAAW,SAAS,MAAM,EAAE,IAAI,UAAQ;AACtE,kBAAMA,SAAQ,KAAK;AACnB,mBAAO,SAAS,OAAO,SAAS,KAAK,UAAUA,OAAM,OAAOA,OAAM,SAASA,OAAM,QAAQA,OAAM,OAAO,CAAC,GAAG,IAAI;AAAA,UAChH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,eAAe,CAAC,SAAS,WAAW;AACxC,eAAO,UAAU,SAAS,MAAM,MAAM,EAAE,KAAK,cAAY;AACvD,iBAAO,UAAU,UAAU,SAAS,MAAM,EAAE,KAAK,CAAA1D,WAAS;AACxD,kBAAMd,QAAO,YAAYc,QAAO,IAAI;AACpC,gBAAI,KAAK,UAAUd,MAAK,EAAE,GAAG;AAC3B,qBAAO,SAASc,QAAO,aAAW;AAChC,uBAAO,OAAO,OAAO,EAAE,OAAO;AAAA,cAChC,GAAG,MAAM,EAAE,IAAI,CAAAyB,UAAQ;AACrB,sBAAM,aAAa,OAAOA,KAAI;AAC9B,uBAAO,SAAS,OAAO,SAAS,KAAK,UAAUA,OAAM,YAAYA,OAAM,UAAU,CAAC,GAAG,IAAI;AAAA,cAC3F,CAAC;AAAA,YACH,OAAO;AACL,qBAAO,SAAS,KAAK;AAAA,YACvB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,CAAC,SAAS,WAAW;AACzC,eAAO,UAAU,SAAS,MAAM,MAAM,EAAE,KAAK,cAAY;AACvD,iBAAO,UAAU,UAAU,SAAS,MAAM,EAAE,KAAK,CAAAzB,WAAS;AACxD,kBAAMd,QAAO,YAAYc,QAAO,IAAI;AACpC,gBAAI,KAAK,UAAUd,MAAKA,MAAK,SAAS,EAAE,GAAG;AACzC,qBAAO,UAAUc,QAAO,aAAW;AACjC,uBAAO,MAAM,OAAO,EAAE,OAAO;AAAA,cAC/B,GAAG,MAAM,EAAE,IAAI,CAAA6B,WAAS;AACtB,uBAAO,SAAS,OAAO,SAAS,KAAK,UAAUA,QAAO,GAAGA,QAAO,CAAC,CAAC,GAAG,IAAI;AAAA,cAC3E,CAAC;AAAA,YACH,OAAO;AACL,qBAAO,SAAS,KAAK;AAAA,YACvB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAAC,QAAQ,WAAW,QAAQ,WAAW,SAAS,QAAQ,gBAAgB;AACrF,eAAO,SAAS,QAAQ,QAAQ,WAAW,SAAS,MAAM,EAAE,KAAK,UAAQ;AACvE,iBAAO,OAAO,WAAW,QAAQ,KAAK,OAAO,KAAK,QAAQ,WAAW;AAAA,QACvE,CAAC;AAAA,MACH;AAEA,YAAM,OAAO,aAAW;AACtB,YAAItD,SAAQ;AACZ,cAAM8B,OAAM,MAAM;AAChB,iBAAO9B;AAAA,QACT;AACA,cAAM6B,OAAM,OAAK;AACf,UAAA7B,SAAQ;AAAA,QACV;AACA,eAAO;AAAA,UACL,KAAA8B;AAAA,UACA,KAAAD;AAAA,QACF;AAAA,MACF;AAEA,YAAM,YAAY,cAAY;AAC5B,cAAM,UAAU,KAAK,SAAS,KAAK,CAAC;AACpC,cAAM,SAAS,MAAM,QAAQ,IAAI,EAAE,KAAK,QAAQ;AAChD,cAAM2D,SAAQ,MAAM;AAClB,iBAAO;AACP,kBAAQ,IAAI,SAAS,KAAK,CAAC;AAAA,QAC7B;AACA,cAAM,QAAQ,MAAM,QAAQ,IAAI,EAAE,OAAO;AACzC,cAAM1D,OAAM,MAAM,QAAQ,IAAI;AAC9B,cAAMD,OAAM,OAAK;AACf,iBAAO;AACP,kBAAQ,IAAI,SAAS,KAAK,CAAC,CAAC;AAAA,QAC9B;AACA,eAAO;AAAA,UACL,OAAA2D;AAAA,UACA;AAAA,UACA,KAAA1D;AAAA,UACA,KAAAD;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ,MAAM;AAClB,cAAM,UAAU,UAAU,IAAI;AAC9B,cAAM4D,MAAK,OAAK,QAAQ,IAAI,EAAE,KAAK,CAAC;AACpC,eAAO;AAAA,UACL,GAAG;AAAA,UACH,IAAAA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,WAAW,CAAC,QAAQ,WAAW,UAAU,QAAQ,SAAS,MAAM;AACtE,YAAM,sBAAsB,CAAAlE,UAAQ,cAAcA,KAAI,EAAE,OAAO,YAAY;AAC3E,YAAM,iBAAiB,CAAC,QAAQ,WAAW,QAAQ,gBAAgB;AACjE,cAAM,SAAS,MAAM;AACrB,cAAM,aAAa,OAAO;AAC1B,cAAM,iBAAiB,WAAS;AAC9B,iBAAO,GAAG,WAAS;AACjB,wBAAY,kBAAkB,SAAS;AACvC,qBAAS,MAAM,QAAQ,MAAM,EAAE,KAAK,YAAU;AAC5C,uBAAS,OAAO,QAAQ,MAAM,EAAE,KAAK,aAAW;AAC9C,sBAAM,QAAQ,QAAQ,MAAM,MAAM,CAAC,CAAC;AACpC,oBAAI,MAAM,WAAW,GAAG;AACtB,wBAAM,aAAa,MAAM;AACzB,wBAAM,oBAAoB,OAAO,UAAU,MAAM;AACjD,wBAAM,+BAA+B,GAAG,QAAQ,MAAM,MAAM,GAAG,YAAY,IAAI;AAC/E,sBAAI,qBAAqB,8BAA8B;AACrD,gCAAY,YAAY,WAAW,OAAO,YAAY,UAAU;AAChE,2BAAO,eAAe,UAAU;AAAA,kBAClC;AAAA,gBACF,WAAW,MAAM,SAAS,GAAG;AAC3B,8BAAY,YAAY,WAAW,OAAO,QAAQ,OAAO,QAAQ,MAAM;AACvE,yBAAO,eAAe,MAAM;AAAA,gBAC9B;AAAA,cACF,CAAC;AAAA,YACH,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,cAAM,YAAY,WAAS;AACzB,sBAAY,MAAM,SAAS;AAC3B,mBAAS,MAAM,QAAQ,MAAM,EAAE,OAAO,mBAAmB,EAAE,KAAK,OAAO,GAAG;AAAA,QAC5E;AACA,cAAM,YAAY,WAAS;AACzB,yBAAe,KAAK;AAAA,QACtB;AACA,cAAM,UAAU,WAAS;AACvB,yBAAe,KAAK;AACpB,qBAAW;AAAA,QACb;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU,KAAK;AAAA,QACf,OAAO,QAAQ;AAAA,QACf,SAAS,YAAY;AAAA,MACvB;AACA,YAAM,KAAK;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU,KAAK;AAAA,QACf,OAAO,QAAQ;AAAA,QACf,SAAS,YAAY;AAAA,MACvB;AAEA,YAAM,QAAQ,CAAAjB,SAAO;AACnB,eAAO,aAAW;AAChB,iBAAO,YAAYA;AAAA,QACrB;AAAA,MACF;AACA,YAAM,OAAO,MAAM,EAAE;AACrB,YAAM,SAAS,MAAM,EAAE;AACvB,YAAM,eAAe,aAAW;AAC9B,eAAO,WAAW,MAAM,WAAW;AAAA,MACrC;AACA,YAAM,MAAM;AAAA,QACV,YAAY,MAAM,EAAE;AAAA,QACpB,WAAW,MAAM,EAAE;AAAA,MACrB;AACA,YAAM,MAAM;AAAA,QACV,YAAY,MAAM,EAAE;AAAA,QACpB,WAAW,MAAM,EAAE;AAAA,MACrB;AAEA,YAAM,QAAQ,UAAQ;AACpB,cAAM,MAAM,SAAS,SAAY,KAAK,MAAM;AAC5C,cAAM,IAAI,IAAI,KAAK,cAAc,IAAI,gBAAgB;AACrD,cAAM,IAAI,IAAI,KAAK,aAAa,IAAI,gBAAgB;AACpD,eAAO,cAAc,GAAG,CAAC;AAAA,MAC3B;AACA,YAAM,KAAK,CAAC,GAAG,GAAG,SAAS;AACzB,cAAM,MAAM,SAAS,SAAY,KAAK,MAAM;AAC5C,cAAM,MAAM,IAAI;AAChB,YAAI,KAAK;AACP,cAAI,SAAS,GAAG,CAAC;AAAA,QACnB;AAAA,MACF;AAEA,YAAM,MAAM,IAAI,SAAS;AAAA,QACvB,EAAE,UAAU,CAAC,KAAK,EAAE;AAAA,QACpB;AAAA,UACE,UAAU;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,iBAAiB,cAAY,IAAI,MAAM,SAAS,OAAO,SAAS,SAAS,SAAS,QAAQ,SAAS,OAAO;AAChH,YAAM,WAAW,CAAAiF,eAAaA,WAAU,MAAM;AAAA,QAC5C,UAAU,SAAO,aAAa,QAAQ,IAAI,cAAc;AAAA,QACxD,UAAU,CAAC,WAAW,gBAAgB,KAAK,SAAS,SAAS;AAAA,QAC7D,OAAO,CAAC,OAAO,UAAU,SAAS,aAAa;AAAA,MACjD,CAAC;AACD,YAAM,WAAW,IAAI;AACrB,YAAM,WAAW,IAAI;AACrB,YAAM,QAAQ,IAAI;AAClB,YAAM,SAAS,CAAAA,eAAa;AAC1B,cAAM,QAAQ,SAASA,UAAS;AAChC,eAAO,YAAY,KAAK;AAAA,MAC1B;AACA,YAAM,QAAQ,SAAS;AACvB,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,yBAAyB,CAAC,KAAK,GAAG,MAAM;AAC5C,YAAI,IAAI;AACR,eAAO,SAAS,MAAM,MAAM,KAAK,IAAI,KAAK,4BAA4B,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,SAAO;AACpI,cAAI,IAAI,eAAe,MAAM;AAC3B,mBAAO,SAAS,KAAK;AAAA,UACvB;AACA,gBAAMpF,KAAI,IAAI,IAAI,YAAY;AAC9B,UAAAA,GAAE,SAAS,IAAI,YAAY,IAAI,MAAM;AACrC,UAAAA,GAAE,SAAS;AACX,iBAAO,SAAS,KAAKA,EAAC;AAAA,QACxB,CAAC;AAAA,MACH;AACA,YAAM,sBAAsB,CAAC,KAAK,GAAG,MAAM;AACzC,YAAI,IAAI;AACR,eAAO,SAAS,MAAM,MAAM,KAAK,IAAI,KAAK,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAAA,MACvH;AACA,YAAM,mBAAmB,MAAM;AAC7B,YAAI,SAAS,wBAAwB;AACnC,iBAAO;AAAA,QACT,WAAW,SAAS,qBAAqB;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,SAAS;AAAA,QAClB;AAAA,MACF,GAAG;AACH,YAAM,YAAY,CAAC,KAAK,GAAG,MAAM;AAC/B,cAAM,MAAM,aAAa,QAAQ,IAAI,QAAQ;AAC7C,eAAO,gBAAgB,KAAK,GAAG,CAAC,EAAE,IAAI,SAAO,SAAS,OAAO,aAAa,QAAQ,IAAI,cAAc,GAAG,IAAI,aAAa,aAAa,QAAQ,IAAI,YAAY,GAAG,IAAI,SAAS,CAAC;AAAA,MAChL;AAEA,YAAM,gBAAgB,CAAC,SAAS,WAAW;AACzC,cAAM,SAAS,KAAK,OAAO;AAC3B,YAAI,YAAY,QAAQ;AACtB,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B,WAAW,CAAC,WAAW;AAAA,UACnB;AAAA,UACA;AAAA,QACF,GAAG,MAAM,GAAG;AACZ,iBAAO,KAAK,GAAG,SAAS,MAAM;AAAA,QAChC,OAAO;AACL,iBAAO,WAAW,IAAI,KAAK,OAAO,OAAO,IAAI,KAAK,MAAM,OAAO;AAAA,QACjE;AAAA,MACF;AACA,YAAM,qBAAqB,CAAC,WAAW,eAAe;AACpD,cAAM,QAAQ,UAAU,KAAK,KAAK,QAAQ,eAAe,KAAK,KAAK;AACnE,cAAM,SAAS,WAAW,KAAK,KAAK,QAAQ,eAAe,KAAK,KAAK;AACrE,eAAO,aAAa,SAAS,OAAO,MAAM;AAAA,MAC5C;AACA,YAAM,kBAAkB,CAAC,OAAO,SAAS,QAAQ,YAAY;AAC3D,cAAM,YAAY,cAAc,OAAO,OAAO;AAC9C,cAAM,aAAa,cAAc,QAAQ,OAAO;AAChD,eAAO,aAAa,SAAS,WAAW,UAAU;AAAA,MACpD;AAEA,YAAM,YAAY,CAAC,OAAO,SAAS,QAAQ,YAAY;AACrD,cAAM,MAAM,MAAM,KAAK;AACvB,cAAM,MAAM,IAAI,IAAI,YAAY;AAChC,YAAI,SAAS,MAAM,KAAK,OAAO;AAC/B,YAAI,OAAO,OAAO,KAAK,OAAO;AAC9B,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAC,OAAO,SAAS,QAAQ,YAAY;AACjD,cAAMA,KAAI,UAAU,OAAO,SAAS,QAAQ,OAAO;AACnD,cAAM,OAAO,KAAK,OAAO,MAAM,KAAK,YAAY;AAChD,eAAOA,GAAE,aAAa,CAAC;AAAA,MACzB;AAEA,YAAM,qBAAqB,SAAO,SAAS,KAAK,IAAI,aAAa,CAAC;AAClE,YAAM,mBAAmB,CAAC,KAAK,QAAQ;AACrC,2BAAmB,GAAG,EAAE,KAAK,CAAAoF,eAAa;AACxC,UAAAA,WAAU,gBAAgB;AAC1B,UAAAA,WAAU,SAAS,GAAG;AAAA,QACxB,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,KAAK,OAAO,SAAS,QAAQ,YAAY;AAC3D,cAAM,MAAM,cAAc,KAAK,OAAO,SAAS,QAAQ,OAAO;AAC9D,yBAAiB,KAAK,GAAG;AAAA,MAC3B;AACA,YAAM,oBAAoB,CAAC,KAAKA,YAAW,OAAO,SAAS,QAAQ,YAAY;AAC7E,QAAAA,WAAU,SAAS,MAAM,KAAK,OAAO;AACrC,QAAAA,WAAU,OAAO,OAAO,KAAK,OAAO;AAAA,MACtC;AACA,YAAM,uBAAuB,CAAC,KAAKG,cAAa,SAAS,KAAKA,SAAQ,EAAE,MAAM;AAAA,QAC5E,KAAK,CAAC,OAAO,SAAS,QAAQ,YAAY;AACxC,qBAAW,KAAK,OAAO,SAAS,QAAQ,OAAO;AAAA,QACjD;AAAA,QACA,KAAK,CAAC,OAAO,SAAS,QAAQ,YAAY;AACxC,6BAAmB,GAAG,EAAE,KAAK,CAAAH,eAAa;AACxC,gBAAIA,WAAU,kBAAkB;AAC9B,cAAAA,WAAU,iBAAiB,MAAM,KAAK,SAAS,OAAO,KAAK,OAAO;AAAA,YACpE,WAAWA,WAAU,QAAQ;AAC3B,kBAAI;AACF,kCAAkB,KAAKA,YAAW,OAAO,SAAS,QAAQ,OAAO;AAAA,cACnE,SAAS,GAAP;AACA,2BAAW,KAAK,QAAQ,SAAS,OAAO,OAAO;AAAA,cACjD;AAAA,YACF,OAAO;AACL,yBAAW,KAAK,QAAQ,SAAS,OAAO,OAAO;AAAA,YACjD;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,YAAM,WAAW,CAAC,KAAK,OAAO,SAAS,QAAQ,YAAY;AACzD,cAAMG,YAAW,gBAAgB,OAAO,SAAS,QAAQ,OAAO;AAChE,6BAAqB,KAAKA,SAAQ;AAAA,MACpC;AACA,YAAM,cAAc,CAAC,KAAK,WAAW,eAAe;AAClD,cAAMA,YAAW,mBAAmB,WAAW,UAAU;AACzD,6BAAqB,KAAKA,SAAQ;AAAA,MACpC;AACA,YAAM,YAAY,CAAAH,eAAa;AAC7B,YAAIA,WAAU,aAAa,GAAG;AAC5B,gBAAM,WAAWA,WAAU,WAAW,CAAC;AACvC,gBAAM,UAAUA,WAAU,WAAWA,WAAU,aAAa,CAAC;AAC7D,iBAAO,SAAS,KAAK,SAAS,OAAO,aAAa,QAAQ,SAAS,cAAc,GAAG,SAAS,aAAa,aAAa,QAAQ,QAAQ,YAAY,GAAG,QAAQ,SAAS,CAAC;AAAA,QAC1K,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,aAAa,CAAAA,eAAa;AAC9B,YAAIA,WAAU,eAAe,QAAQA,WAAU,cAAc,MAAM;AACjE,iBAAO,UAAUA,UAAS;AAAA,QAC5B,OAAO;AACL,gBAAM,SAAS,aAAa,QAAQA,WAAU,UAAU;AACxD,gBAAM,QAAQ,aAAa,QAAQA,WAAU,SAAS;AACtD,iBAAO,MAAM,QAAQA,WAAU,cAAc,OAAOA,WAAU,WAAW,IAAI,SAAS,KAAK,SAAS,OAAO,QAAQA,WAAU,cAAc,OAAOA,WAAU,WAAW,CAAC,IAAI,UAAUA,UAAS;AAAA,QACjM;AAAA,MACF;AACA,YAAM,eAAe,CAAC,KAAK,SAAS,uBAAuB,SAAS;AAClE,cAAM,YAAY,uBAAuB,qBAAqB;AAC9D,cAAM,MAAM,UAAU,KAAK,OAAO;AAClC,yBAAiB,KAAK,GAAG;AAAA,MAC3B;AACA,YAAM,WAAW,SAAO,mBAAmB,GAAG,EAAE,OAAO,SAAO,IAAI,aAAa,CAAC,EAAE,KAAK,UAAU;AACjG,YAAM,QAAQ,SAAO,SAAS,GAAG,EAAE,IAAI,CAAAJ,WAAS,aAAa,MAAMA,OAAM,OAAOA,OAAM,SAASA,OAAM,QAAQA,OAAM,OAAO,CAAC;AAC3H,YAAM,eAAe,CAAC,KAAKI,eAAc;AACvC,cAAM,MAAM,WAAW,KAAKA,UAAS;AACrC,eAAO,eAAe,GAAG;AAAA,MAC3B;AACA,YAAM,aAAa,CAAC,KAAK,GAAG,MAAM,UAAU,KAAK,GAAG,CAAC;AACrD,YAAM,QAAQ,SAAO;AACnB,2BAAmB,GAAG,EAAE,KAAK,CAAAA,eAAaA,WAAU,gBAAgB,CAAC;AAAA,MACvE;AAEA,YAAM,eAAe,SAAO;AAC1B,cAAM,mBAAmB,CAAC,GAAG,MAAM;AACjC,iBAAO,aAAa,UAAU,aAAa,QAAQ,IAAI,QAAQ,GAAG,GAAG,CAAC;AAAA,QACxE;AACA,cAAM,UAAU,aAAW;AACzB,iBAAO,QAAQ,IAAI,sBAAsB;AAAA,QAC3C;AACA,cAAM,gBAAgB,CAAC,OAAO,SAAS,QAAQ,YAAY;AACzD,gBAAM,MAAM,aAAa,MAAM,OAAO,SAAS,QAAQ,OAAO;AAC9D,iBAAO,aAAa,KAAK,GAAG;AAAA,QAC9B;AACA,cAAM,eAAe,MAAM;AACzB,iBAAO,MAAM,GAAG,EAAE,IAAI,cAAY;AAChC,mBAAO,eAAe,KAAK,QAAQ;AAAA,UACrC,CAAC;AAAA,QACH;AACA,cAAM,YAAY,WAAS;AACzB,gBAAMG,YAAW,aAAa,SAAS,MAAM,OAAO,MAAM,MAAM;AAChE,iBAAO,eAAe,KAAKA,SAAQ;AAAA,QACrC;AACA,cAAM,iBAAiB,CAAC,GAAG,MAAM;AAC/B,iBAAO,WAAW,KAAK,GAAG,CAAC,EAAE,IAAI,CAAAC,WAAS;AACxC,mBAAO,MAAM,OAAOA,OAAM,OAAOA,OAAM,SAASA,OAAM,QAAQA,OAAM,OAAO;AAAA,UAC7E,CAAC;AAAA,QACH;AACA,cAAM,iBAAiB,MAAM;AAC3B,gBAAM,GAAG;AAAA,QACX;AACA,cAAM,oBAAoB,CAAC,UAAU,UAAU;AAC7C,gBAAM,GAAG,EAAE,KAAK,SAAO,IAAI,KAAK,SAAO,IAAI,SAAS,OAAO,GAAG,CAAC,WAAW,eAAe;AACvF,kBAAM,OAAO,UAAU,YAAY;AACnC,wBAAY,KAAK,MAAM,IAAI;AAAA,UAC7B,GAAG,CAAC,OAAO,SAAS,QAAQ,YAAY;AACtC,kBAAM,OAAO,UAAU,QAAQ;AAC/B,kBAAM,SAAS,UAAU,UAAU;AACnC,qBAAS,KAAK,MAAM,QAAQ,MAAM,MAAM;AAAA,UAC1C,CAAC,CAAC;AAAA,QACJ;AACA,cAAMC,cAAa,aAAW;AAC5B,uBAAa,KAAK,SAAS,KAAK;AAAA,QAClC;AACA,cAAM,iBAAiB,aAAW;AAChC,uBAAa,KAAK,OAAO;AAAA,QAC3B;AACA,cAAM,eAAe,SAAO;AAC1B,mBAAS,KAAK,IAAI,OAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO;AAAA,QAC/D;AACA,cAAM,uBAAuB,CAAC,OAAO,WAAW;AAC9C,sBAAY,KAAK,OAAO,MAAM;AAAA,QAChC;AACA,cAAM,iBAAiB,MAAM;AAC3B,iBAAO,IAAI;AAAA,QACb;AACA,cAAM,aAAa,MAAM;AACvB,gBAAM,MAAM,MAAM,aAAa,QAAQ,IAAI,QAAQ,CAAC;AACpD,iBAAO,IAAI;AAAA,QACb;AACA,cAAM,WAAW,CAAC,GAAG,MAAM;AACzB,aAAG,GAAG,GAAG,aAAa,QAAQ,IAAI,QAAQ,CAAC;AAAA,QAC7C;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,KAAK,CAACjF,OAAM,UAAU;AAAA,QAC1B,MAAAA;AAAA,QACA;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,KAAK,WAAW,QAAQ,gBAAgB;AACrD,cAAM,SAAS,aAAa,GAAG;AAC/B,cAAM,WAAW,eAAe,QAAQ,WAAW,QAAQ,WAAW;AACtE,eAAO;AAAA,UACL,YAAY,SAAS;AAAA,UACrB,WAAW,SAAS;AAAA,UACpB,WAAW,SAAS;AAAA,UACpB,SAAS,SAAS;AAAA,QACpB;AAAA,MACF;AACA,YAAM,iBAAiB,UAAQ,UAAU,MAAM,aAAa,EAAE,OAAO,YAAY;AACjF,YAAM,sBAAsB,CAAC,OAAO,WAAW,eAAe,KAAK,KAAK,eAAe,MAAM;AAC7F,YAAM,WAAW,CAAC,KAAK,WAAW,QAAQ,gBAAgB;AACxD,cAAM,SAAS,aAAa,GAAG;AAC/B,cAAM,kBAAkB,MAAM;AAC5B,sBAAY,MAAM,SAAS;AAC3B,iBAAO,SAAS,KAAK;AAAA,QACvB;AACA,cAAM,UAAU,CAAC,OAAO,OAAO,SAAS,QAAQ,SAAS,cAAc;AACrE,gBAAM,YAAY,MAAM;AACxB,gBAAM,UAAU,UAAU;AAC1B,gBAAM,WAAW,UAAU,aAAa;AACxC,gBAAM,UAAU,WAAW,WAAW,YAAY,gBAAgB,EAAE,KAAK,MAAM;AAC7E,gBAAI,aAAa,OAAO,KAAK,CAAC,UAAU;AACtC,0BAAY,kBAAkB,SAAS;AAAA,YACzC;AACA,gBAAI,aAAa,OAAO,KAAK,YAAY,CAAC,oBAAoB,OAAO,MAAM,GAAG;AAC5E,qBAAO,SAAS;AAAA,YAClB,WAAW,OAAO,OAAO,KAAK,UAAU;AACtC,qBAAO,MAAM,QAAQ,QAAQ,WAAW,QAAQ,MAAM,QAAQ,OAAO,YAAY,WAAW;AAAA,YAC9F,WAAW,KAAK,OAAO,KAAK,UAAU;AACpC,qBAAO,MAAM,QAAQ,QAAQ,WAAW,QAAQ,IAAI,QAAQ,OAAO,YAAY,WAAW;AAAA,YAC5F,WAAW,OAAO,OAAO,GAAG;AAC1B,qBAAO,MAAM,UAAU,QAAQ,QAAQ,MAAM,QAAQ,OAAO,aAAa;AAAA,YAC3E,WAAW,KAAK,OAAO,GAAG;AACxB,qBAAO,MAAM,UAAU,QAAQ,QAAQ,IAAI,QAAQ,OAAO,YAAY;AAAA,YACxE,OAAO;AACL,qBAAO,SAAS;AAAA,YAClB;AAAA,UACF,GAAG,cAAY;AACb,kBAAM,WAAW,cAAY;AAC3B,qBAAO,MAAM;AACX,sBAAM,aAAa,QAAQ,UAAU,WAAS;AAC5C,yBAAO,OAAO,MAAM,MAAM,MAAM,MAAM,WAAW,UAAU,WAAW;AAAA,gBACxE,CAAC;AACD,uBAAO,WAAW,KAAK,MAAM;AAC3B,yBAAO,SAAS,WAAW,YAAY,uBAAuB,YAAY,oBAAoB,EAAE,IAAI,WAAS;AAC3G,0BAAM+E,YAAW,OAAO,OAAO,KAAK,UAAU,UAAU,OAAO,IAAI,KAAK,QAAQ,KAAK;AACrF,2BAAO,qBAAqB,KAAK,GAAG,MAAM,OAAO,CAAC,GAAGA,UAAS,MAAM,KAAK,CAAC;AAC1E,gCAAY,MAAM,SAAS;AAC3B,2BAAO,SAAS,OAAO,SAAS,KAAK,GAAG,IAAI;AAAA,kBAC9C,CAAC;AAAA,gBACH,GAAG,OAAK;AACN,yBAAO,SAAS,KAAK,SAAS,OAAO,SAAS,KAAK,GAAG,IAAI,CAAC;AAAA,gBAC7D,CAAC;AAAA,cACH;AAAA,YACF;AACA,gBAAI,aAAa,OAAO,KAAK,YAAY,CAAC,oBAAoB,OAAO,MAAM,GAAG;AAC5E,qBAAO,SAAS;AAAA,YAClB,WAAW,OAAO,OAAO,KAAK,UAAU;AACtC,qBAAO,SAAS,CAAC,GAAG,GAAI,CAAC,CAAC,CAAC;AAAA,YAC7B,WAAW,KAAK,OAAO,KAAK,UAAU;AACpC,qBAAO,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,YAC7B,WAAW,UAAU,WAAW,OAAO,KAAK,UAAU;AACpD,qBAAO,SAAS;AAAA,gBACd,GAAG,GAAG,EAAE;AAAA,gBACR,GAAG,IAAI,CAAC;AAAA,cACV,CAAC;AAAA,YACH,WAAW,UAAU,UAAU,OAAO,KAAK,UAAU;AACnD,qBAAO,SAAS;AAAA,gBACd,GAAG,GAAG,CAAE;AAAA,gBACR,GAAG,GAAI,CAAC;AAAA,cACV,CAAC;AAAA,YACH,WAAW,aAAa,OAAO,KAAK,CAAC,UAAU;AAC7C,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO,SAAS;AAAA,YAClB;AAAA,UACF,CAAC;AACD,iBAAO,QAAQ;AAAA,QACjB;AACA,cAAM,QAAQ,CAAC,OAAO,OAAO,SAAS,QAAQ,YAAY;AACxD,iBAAO,WAAW,WAAW,YAAY,gBAAgB,EAAE,KAAK,MAAM;AACpE,kBAAM,YAAY,MAAM;AACxB,kBAAM,UAAU,UAAU;AAC1B,kBAAM,WAAW,UAAU,aAAa;AACxC,gBAAI,CAAC,UAAU;AACb,qBAAO,SAAS,KAAK;AAAA,YACvB;AACA,gBAAI,aAAa,OAAO,KAAK,oBAAoB,OAAO,MAAM,GAAG;AAC/D,qBAAO,KAAK,WAAW,QAAQ,OAAO,SAAS,QAAQ,SAAS,YAAY,WAAW;AAAA,YACzF,OAAO;AACL,qBAAO,SAAS,KAAK;AAAA,YACvB;AAAA,UACF,GAAG,SAAS,IAAI;AAAA,QAClB;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW,CAAC,KAAK,WAAW,QAAQ,gBAAgB;AACxD,cAAM,SAAS,aAAa,GAAG;AAC/B,eAAO,CAAC,OAAO,WAAW;AACxB,sBAAY,kBAAkB,SAAS;AACvC,mBAAS,OAAO,QAAQ,MAAM,EAAE,KAAK,aAAW;AAC9C,kBAAM,QAAQ,QAAQ,MAAM,MAAM,CAAC,CAAC;AACpC,wBAAY,YAAY,WAAW,OAAO,QAAQ,OAAO,QAAQ,MAAM;AACvE,mBAAO,eAAe,MAAM;AAC5B,mBAAO,kBAAkB;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,OAAO,CAAC,SAAS,SAAS;AAC9B,cAAM1F,SAAQ,MAAM,SAAS,IAAI;AACjC,eAAOA,WAAU,UAAaA,WAAU,KAAK,CAAC,IAAIA,OAAM,MAAM,GAAG;AAAA,MACnE;AACA,YAAM,QAAQ,CAAC,SAAS,MAAM,OAAO;AACnC,cAAM,MAAM,KAAK,SAAS,IAAI;AAC9B,cAAMU,MAAK,IAAI,OAAO,CAAC,EAAE,CAAC;AAC1B,cAAM,SAAS,MAAMA,IAAG,KAAK,GAAG,CAAC;AACjC,eAAO;AAAA,MACT;AACA,YAAM,WAAW,CAAC,SAAS,MAAM,OAAO;AACtC,cAAMA,MAAK,SAAS,KAAK,SAAS,IAAI,GAAG,OAAK,MAAM,EAAE;AACtD,YAAIA,IAAG,SAAS,GAAG;AACjB,gBAAM,SAAS,MAAMA,IAAG,KAAK,GAAG,CAAC;AAAA,QACnC,OAAO;AACL,mBAAS,SAAS,IAAI;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AAEA,YAAM,WAAW,aAAW,QAAQ,IAAI,cAAc;AACtD,YAAM,QAAQ,aAAW,KAAK,SAAS,OAAO;AAC9C,YAAM,QAAQ,CAAC,SAAS,UAAU,MAAM,SAAS,SAAS,KAAK;AAC/D,YAAM,WAAW,CAAC,SAAS,UAAU,SAAS,SAAS,SAAS,KAAK;AAErE,YAAM,MAAM,CAAC,SAAS,UAAU;AAC9B,YAAI,SAAS,OAAO,GAAG;AACrB,kBAAQ,IAAI,UAAU,IAAI,KAAK;AAAA,QACjC,OAAO;AACL,gBAAM,SAAS,KAAK;AAAA,QACtB;AAAA,MACF;AACA,YAAM,aAAa,aAAW;AAC5B,cAAM,YAAY,SAAS,OAAO,IAAI,QAAQ,IAAI,YAAY,MAAM,OAAO;AAC3E,YAAI,UAAU,WAAW,GAAG;AAC1B,mBAAS,SAAS,OAAO;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,WAAW,CAAC,SAAS,UAAU;AACnC,YAAI,SAAS,OAAO,GAAG;AACrB,gBAAM,YAAY,QAAQ,IAAI;AAC9B,oBAAU,OAAO,KAAK;AAAA,QACxB,OAAO;AACL,mBAAS,SAAS,KAAK;AAAA,QACzB;AACA,mBAAW,OAAO;AAAA,MACpB;AACA,YAAM,MAAM,CAAC,SAAS,UAAU,SAAS,OAAO,KAAK,QAAQ,IAAI,UAAU,SAAS,KAAK;AAEzF,YAAM,WAAW,CAAC,SAAS,YAAY;AACrC,eAAO,SAAS,OAAK;AACnB,mBAAS,SAAS,CAAC;AAAA,QACrB,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,WAAS,aAAW;AACnC,YAAI,SAAS,KAAK;AAAA,MACpB;AACA,YAAM,gBAAgB,aAAW,aAAW;AAC1C,iBAAS,SAAS,OAAO;AAAA,MAC3B;AAEA,YAAM,UAAU,CAAAoD,cAAY;AAC1B,cAAM,oBAAoB,SAASA,UAAS,QAAQ;AACpD,cAAM,yBAAyB,cAAc;AAAA,UAC3CA,UAAS;AAAA,UACTA,UAAS;AAAA,UACTA,UAAS;AAAA,QACX,CAAC;AACD,cAAM0B,SAAQ,eAAa;AACzB,gBAAM,OAAO,YAAY,WAAW1B,UAAS,gBAAgB;AAC7D,iBAAO,MAAM,sBAAsB;AAAA,QACrC;AACA,cAAM,cAAc,CAAC,WAAWhD,QAAO,OAAO,WAAW;AACvD,UAAA0E,OAAM,SAAS;AACf,iBAAO1E,QAAO,iBAAiB;AAC/B,cAAI,OAAOgD,UAAS,aAAa;AACjC,cAAI,QAAQA,UAAS,YAAY;AAAA,QACnC;AACA,eAAO;AAAA,UACL,mBAAmB0B;AAAA,UACnB,OAAAA;AAAA,UACA;AAAA,UACA,kBAAkB1B,UAAS;AAAA,UAC3B,uBAAuBA,UAAS;AAAA,UAChC,sBAAsBA,UAAS;AAAA,QACjC;AAAA,MACF;AACA,YAAM,SAAS,CAACA,WAAU,aAAa,YAAY;AACjD,cAAM,4BAA4B,aAAW;AAC3C,mBAAS,SAASA,UAAS,QAAQ;AACnC,mBAAS,SAASA,UAAS,aAAa;AACxC,mBAAS,SAASA,UAAS,YAAY;AAAA,QACzC;AACA,cAAM,wBAAwB,aAAW;AACvC,gBAAM,SAASA,UAAS,UAAU,GAAG;AAAA,QACvC;AACA,cAAM0B,SAAQ,eAAa;AACzB,4BAAkB,SAAS;AAC3B,kBAAQ;AAAA,QACV;AACA,cAAM,oBAAoB,eAAa;AACrC,gBAAM,OAAO,YAAY,WAAW,GAAI1B,UAAS,oBAAsBA,UAAS,yBAA2BA,UAAS,sBAAuB;AAC3I,iBAAO,MAAM,yBAAyB;AAAA,QACxC;AACA,cAAM,cAAc,CAAC,WAAWhD,QAAO,OAAO,WAAW;AACvD,UAAA0E,OAAM,SAAS;AACf,iBAAO1E,QAAO,qBAAqB;AACnC,gBAAM,OAAOgD,UAAS,eAAe,GAAG;AACxC,gBAAM,QAAQA,UAAS,cAAc,GAAG;AACxC,sBAAYhD,QAAO,OAAO,MAAM;AAAA,QAClC;AACA,eAAO;AAAA,UACL;AAAA,UACA,OAAA0E;AAAA,UACA;AAAA,UACA,kBAAkB1B,UAAS;AAAA,UAC3B,uBAAuBA,UAAS;AAAA,UAChC,sBAAsBA,UAAS;AAAA,QACjC;AAAA,MACF;AACA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,QACA;AAAA,MACF;AAEA,YAAM,OAAO,CAAC,SAAS,QAAQ,YAAY,aAAa;AACtD,gBAAQ,QAAQ,KAAK;AAAA,UACrB,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB,KAAK;AACH,mBAAO,SAAS,QAAQ,OAAO;AAAA,UACjC,KAAK;AACH,mBAAO,WAAW,QAAQ,QAAQ;AAAA,QACpC;AAAA,MACF;AACA,YAAM,OAAO,OAAO,EAAE,KAAK,OAAO;AAClC,YAAM,WAAW,eAAa;AAAA,QAC5B,KAAK;AAAA,QACL;AAAA,MACF;AACA,YAAM,SAAS,cAAY;AAAA,QACzB,KAAK;AAAA,QACL;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,UAAU+B,WAAU,qBAAqB;AAC3D,cAAM/D,OAAM,MAAM,SAAS,SAAS,GAAG,gBAAgB,EAAE,KAAK,MAAM+D,UAAS,EAAE,KAAK,MAAM,MAAM,GAAG,QAAQ;AAC3G,eAAO,EAAE,KAAA/D,KAAI;AAAA,MACf;AAEA,YAAM,mBAAmB,CAACK,OAAM,kBAAkB;AAChD,cAAM,SAASA,MAAK,MAAM,GAAG,cAAc,cAAc,SAAS,GAAG,MAAM,CAAC;AAC5E,cAAM,YAAY,aAAa,MAAM;AACrC,eAAO,OAAO,WAAW,CAAAE,YAAU;AACjC,gBAAM,cAAcA,QAAO,MAAM,MAAM,GAAG,cAAc,cAAc,SAAS,GAAG,SAAS,CAAC;AAC5F,iBAAO,MAAM,aAAa,CAAAd,UAAQA,MAAK,OAAO;AAAA,QAChD,CAAC;AAAA,MACH;AACA,YAAM,sBAAsB,CAACY,OAAM,kBAAkB;AACnD,cAAM,WAAWA,MAAK,MAAM,cAAc,GAAG,MAAM,cAAc,GAAG,UAAU,GAAGA,MAAK,MAAM;AAC5F,cAAM,cAAc,aAAa,QAAQ;AACzC,eAAO,OAAO,aAAa,CAAAE,YAAU;AACnC,gBAAM,cAAcA,QAAO,MAAM,MAAM,cAAc,GAAG,SAAS,cAAc,GAAG,UAAU,GAAGA,QAAO,MAAM,MAAM;AAClH,iBAAO,MAAM,aAAa,CAAAd,UAAQA,MAAK,OAAO;AAAA,QAChD,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,CAACE,QAAO,QAAQ,eAAe;AACnD,cAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,cAAM,UAAU,QAAQ,WAAW,MAAM;AACzC,eAAO,QAAQ,IAAI,mBAAiB;AAClC,gBAAMU,QAAO,OAAO,WAAW,YAAY,KAAK;AAChD,gBAAM,EAAC,MAAAxB,MAAI,IAAI,mBAAmBwB,KAAI;AACtC,gBAAM,gBAAgB,iBAAiBxB,OAAM,aAAa;AAC1D,gBAAM,mBAAmB,oBAAoBA,OAAM,aAAa;AAChE,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,UAAU,CAAC,QAAQ,GAAG,GAAG,MAAM,SAAS,MAAM,SAAS;AAAA,QAC3D;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,iBAAiB,cAAY;AACjC,cAAM,SAAS,aAAa,QAAQ,uBAAuB,QAAQ,EAAE,MAAM,SAAS,MAAM,CAAC;AAC3F,cAAM,OAAO,MAAM,SAAS,gBAAgB;AAC5C,cAAM,UAAU,MAAM,SAAS,eAAe;AAC9C,cAAM,OAAO,QAAQ,SAAS,IAAI;AAClC,eAAO,QAAQ,QAAQ,SAAS,SAAS,SAAS,SAAS,MAAM,SAAS,MAAM,QAAQ;AAAA,MAC1F;AACA,YAAM,SAAS,CAAC+B,SAAQ,YAAY,cAAY;AAC9C,YAAIA,QAAO,QAAQ,GAAG;AACpB,kBAAQ,eAAe,QAAQ,CAAC;AAAA,QAClC;AAAA,MACF;AACA,YAAM,SAAS,CAAC,SAAS,OAAOA,SAAQ,SAAS,eAAe;AAC9D,cAAM,UAAU,OAAOA,SAAQ,OAAO;AACtC,gBAAQ,IAAI,iBAAiB,OAAO,SAAS,UAAU;AACvD,eAAO,EAAE,QAAQ,MAAM,QAAQ,SAAS,OAAO,SAAS,UAAU,EAAE;AAAA,MACtE;AACA,YAAM,SAAS,CAAC,SAAS,OAAOA,SAAQ,YAAY,OAAO,SAAS,OAAOA,SAAQ,SAAS,KAAK;AACjG,YAAM,SAAS,CAAC,SAAS,OAAO,SAAS,eAAe;AACtD,gBAAQ,IAAI,oBAAoB,OAAO,SAAS,UAAU;AAAA,MAC5D;AAEA,YAAM,SAAS;AACf,YAAM,OAAO,CAAC,SAAS,OAAO,YAAY,OAAO,SAAS,OAAO,QAAQ,OAAO;AAChF,YAAM,eAAe;AAErB,YAAM,oBAAoB,OAAK,CAAC,IAAI,aAAa,QAAQ,EAAE,MAAM,GAAG,2BAA2B;AAC/F,YAAM,4BAA4B,CAAC,QAAQ,kBAAkB;AAC3D,cAAM,gBAAgB,WAAW,MAAM,aAAa,QAAQ,OAAO,QAAQ,CAAC,GAAG,MAAM,iBAAiB,kBAAkB,MAAM,GAAG,UAAU,MAAM,CAAC,GAAG,SAAS,gBAAgB;AAC9K,cAAM,cAAc,CAAC5B,QAAO,OAAO,WAAW;AAC5C,gBAAM,WAAW,MAAM,KAAK;AAC5B,mBAAS,KAAK,CAAAW,WAAS;AACrB,kBAAMwD,gBAAe,sBAAsB,MAAM;AACjD,kBAAM,aAAa,eAAe,MAAM,aAAa,QAAQ,OAAO,OAAO,CAAC,GAAGA,aAAY;AAC3F,kBAAM,gBAAgB,sBAAsB,MAAM;AAClD,kBAAM,aAAa,cAAcxD,QAAO,EAAE,WAAW,cAAc,GAAG,UAAU;AAChF,qCAAyB,QAAQX,QAAO,OAAO,QAAQ,UAAU;AAAA,UACnE,CAAC;AAAA,QACH;AACA,cAAM,UAAU,MAAM,wBAAwB,MAAM;AACpD,cAAM,cAAc,oBAAoB,OAAO,UAAU,aAAa,OAAO;AAC7E,eAAO,GAAG,QAAQ,QAAM;AACtB,gBAAM,MAAM,OAAO,OAAO;AAC1B,gBAAM0B,QAAO,QAAQ,MAAM;AAC3B,gBAAM,SAAS,UAAU,MAAM;AAC/B,gBAAM,gBAAgB,MAAM;AAC1B,kBAAM,MAAM,OAAO;AACnB,kBAAM,QAAQ,aAAa,QAAQ,IAAI,SAAS,CAAC;AACjD,kBAAM,MAAM,aAAa,QAAQ,IAAI,OAAO,CAAC;AAC7C,kBAAM,SAAS,UAAU,OAAO;AAAA,cAC9B;AAAA,cACA;AAAA,YACF,CAAC;AACD,mBAAO,KAAK,MAAM,YAAY,MAAMA,KAAI,GAAG,IAAI;AAAA,UACjD;AACA,gBAAM,gBAAgB,MAAM,KAAKA,OAAM,QAAQ,WAAW;AAC1D,gBAAM,cAAc,SAAS,KAAKA,OAAM,QAAQ,WAAW;AAC3D,gBAAM,aAAa,SAAS,KAAKA,OAAM,QAAQ,WAAW;AAC1D,gBAAM,cAAc,WAAS,MAAM,IAAI,aAAa;AACpD,iBAAO,GAAG,uBAAuB,OAAK,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC;AACnE,gBAAM,iBAAiB,CAAC,OAAO,aAAa;AAC1C,gBAAI,CAAC,YAAY,KAAK,GAAG;AACvB;AAAA,YACF;AACA,gBAAI,SAAS,MAAM;AACjB,oBAAM,KAAK;AAAA,YACb;AACA,qBAAS,UAAU,KAAK,QAAM;AAC5B,oBAAMkD,YAAW,aAAa,SAAS,GAAG,OAAO,GAAG,MAAM;AAC1D,oBAAM,MAAM,WAAW,KAAKA,SAAQ;AACpC,qBAAO,UAAU,OAAO,GAAG;AAAA,YAC7B,CAAC;AAAA,UACH;AACA,gBAAM,QAAQ,WAAS;AACrB,kBAAM,eAAe,aAAa,KAAK;AACvC,gBAAI,aAAa,IAAI,YAAY,aAAa,aAAa,IAAI,KAAK,GAAG;AACrE,oBAAM,MAAM,OAAO,UAAU,OAAO;AACpC,oBAAM,QAAQ,aAAa,QAAQ,IAAI,cAAc;AACrD,oBAAM,MAAM,aAAa,QAAQ,IAAI,YAAY;AACjD,0BAAY,MAAM,cAAc,OAAO,IAAI,aAAa,KAAK,IAAI,SAAS,EAAE,KAAK,cAAY;AAC3F,+BAAe,cAAc,QAAQ;AAAA,cACvC,CAAC;AAAA,YACH;AAAA,UACF;AACA,gBAAM,UAAU,WAAS;AACvB,kBAAM,eAAe,aAAa,KAAK;AACvC,0BAAc,KAAK;AACnB,kBAAM,MAAM,OAAO,UAAU,OAAO;AACpC,kBAAM,QAAQ,aAAa,QAAQ,IAAI,cAAc;AACrD,kBAAM,MAAM,aAAa,QAAQ,IAAI,YAAY;AACjD,kBAAM,YAAY,YAAY,KAAK,GAAG,EAAE,aAAa,QAAQ,OAAO,UAAU,SAAS,CAAC,CAAC;AACzF,wBAAY,QAAQ,cAAc,OAAO,IAAI,aAAa,KAAK,IAAI,WAAW,SAAS,EAAE,KAAK,cAAY;AACxG,6BAAe,cAAc,QAAQ;AAAA,YACvC,CAAC;AACD,0BAAc,KAAK;AAAA,UACrB;AACA,gBAAM,cAAc,SAAO,IAAI,WAAW;AAC1C,gBAAM,sBAAsB,SAAO;AACjC,gBAAI,IAAI,YAAY,QAAW;AAC7B,qBAAO;AAAA,YACT;AACA,oBAAQ,IAAI,UAAU,OAAO;AAAA,UAC/B;AACA,gBAAM,YAAY,CAAAI,QAAM;AACtB,0BAAc,WAAW;AAAA,UAC3B;AACA,gBAAM,YAAY,OAAK;AACrB,gBAAI,YAAY,CAAC,KAAK,kBAAkB,CAAC,GAAG;AAC1C,4BAAc,UAAU,aAAa,CAAC,CAAC;AAAA,YACzC;AAAA,UACF;AACA,gBAAM,YAAY,OAAK;AACrB,gBAAI,oBAAoB,CAAC,KAAK,kBAAkB,CAAC,GAAG;AAClD,4BAAc,UAAU,aAAa,CAAC,CAAC;AAAA,YACzC;AAAA,UACF;AACA,gBAAM,UAAU,OAAK;AACnB,gBAAI,YAAY,CAAC,KAAK,kBAAkB,CAAC,GAAG;AAC1C,4BAAc,QAAQ,aAAa,CAAC,CAAC;AAAA,YACvC;AAAA,UACF;AACA,gBAAM,eAAe,MAAM;AACzB,kBAAM,aAAa,KAAK,aAAa,QAAQtD,KAAI,CAAC;AAClD,kBAAM,gBAAgB,KAAK,CAAC;AAC5B,kBAAM,WAAW,OAAK;AACpB,oBAAM,SAAS,aAAa,QAAQ,EAAE,MAAM;AAC5C,kBAAI,MAAM,IAAI,EAAE,MAAM,KAAK,MAAM,IAAI,EAAE,MAAM,GAAG;AAC9C,sBAAM,KAAK,WAAW,IAAI;AAC1B,sBAAM,MAAM,cAAc,IAAI;AAC9B,oBAAI,KAAK,IAAI,MAAM,KAAK,EAAE,YAAY,MAAM,KAAK;AAC/C,oBAAE,eAAe;AACjB,6BAAW,QAAQ,MAAM;AAAA,gBAC3B;AAAA,cACF;AACA,yBAAW,IAAI,MAAM;AACrB,4BAAc,IAAI,EAAE,SAAS;AAAA,YAC/B;AACA,mBAAO,EAAE,SAAS;AAAA,UACpB;AACA,gBAAM,YAAY,aAAa;AAC/B,iBAAO,GAAG,aAAa,SAAS;AAChC,iBAAO,GAAG,aAAa,SAAS;AAChC,iBAAO,GAAG,aAAa,SAAS;AAChC,iBAAO,GAAG,WAAW,OAAO;AAC5B,iBAAO,GAAG,YAAY,UAAU,QAAQ;AACxC,iBAAO,GAAG,SAAS,KAAK;AACxB,iBAAO,GAAG,WAAW,OAAO;AAC5B,iBAAO,GAAG,cAAc,aAAa;AAAA,QACvC,CAAC;AACD,eAAO,GAAG,WAAW,MAAM;AACzB,iBAAO,WAAW,YAAY,SAAS,aAAa;AACpD,iBAAO,WAAW,YAAY,SAAS,YAAY;AAAA,QACrD,CAAC;AACD,cAAM,qBAAqB,eAAa,YAAY,MAAM,aAAa,QAAQ,SAAS,CAAC;AACzF,cAAM,mBAAmB,MAAM,KAAK,cAAc,IAAI,GAAG,SAAS,CAAC,CAAC,GAAG,CAAA1B,WAAS;AAC9E,iBAAO,MAAMA,QAAO,CAAAS,UAAQA,MAAK,GAAG;AAAA,QACtC,GAAG,CAAAA,UAAQ,CAACA,MAAK,GAAG,CAAC;AACrB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,QAAQ,YAAU;AACtB,YAAI,WAAW,CAAC;AAChB,cAAMwE,QAAO,aAAW;AACtB,cAAI,YAAY,QAAW;AACzB,kBAAM,IAAI,MAAM,qCAAqC;AAAA,UACvD;AACA,mBAAS,KAAK,OAAO;AAAA,QACvB;AACA,cAAMC,UAAS,aAAW;AACxB,qBAAW,SAAS,UAAU,OAAK;AACjC,mBAAO,MAAM;AAAA,UACf,CAAC;AAAA,QACH;AACA,cAAM,UAAU,IAAI,SAAS;AAC3B,gBAAM,QAAQ,CAAC;AACf,iBAAO,QAAQ,CAAC3F,OAAM,MAAM;AAC1B,kBAAMA,SAAQ,KAAK;AAAA,UACrB,CAAC;AACD,iBAAO,UAAU,aAAW;AAC1B,oBAAQ,KAAK;AAAA,UACf,CAAC;AAAA,QACH;AACA,eAAO;AAAA,UACL,MAAA0F;AAAA,UACA,QAAAC;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,WAAW,cAAY;AAC3B,cAAM,WAAW,IAAI,UAAU,WAAS;AACtC,iBAAO;AAAA,YACL,MAAM,MAAM;AAAA,YACZ,QAAQ,MAAM;AAAA,UAChB;AAAA,QACF,CAAC;AACD,cAAM,UAAU,IAAI,UAAU,WAAS;AACrC,iBAAO,MAAM;AAAA,QACf,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO,CAAC,IAAI,SAAS;AACzB,YAAI,QAAQ;AACZ,cAAM,SAAS,MAAM;AACnB,cAAI,CAAC,OAAO,KAAK,GAAG;AAClB,yBAAa,KAAK;AAClB,oBAAQ;AAAA,UACV;AAAA,QACF;AACA,cAAM,WAAW,IAAI,SAAS;AAC5B,iBAAO;AACP,kBAAQ,WAAW,MAAM;AACvB,oBAAQ;AACR,eAAG,MAAM,MAAM,IAAI;AAAA,UACrB,GAAG,IAAI;AAAA,QACT;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO,SAAO;AAClB,eAAO,IAAI,MAAM,CAAC,EAAE,KAAK;AAAA,MAC3B;AACA,YAAM,aAAa,CAAC,UAAUC,UAAS;AACrC,cAAM,IAAI,MAAM,wBAAwB,KAAK,QAAQ,EAAE,KAAK,IAAI,IAAI,gDAAgD,KAAKA,KAAI,EAAE,KAAK,IAAI,IAAI,GAAG;AAAA,MACjJ;AACA,YAAM,gBAAgB,iBAAe;AACnC,cAAM,IAAI,MAAM,kCAAkC,KAAK,WAAW,EAAE,KAAK,IAAI,CAAC;AAAA,MAChF;AACA,YAAM,iBAAiB,CAAC,OAAO,UAAU;AACvC,YAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,gBAAM,IAAI,MAAM,SAAS,QAAQ,oCAAoC,QAAQ,GAAG;AAAA,QAClF;AACA,eAAO,OAAO,OAAK;AACjB,cAAI,CAAC,SAAS,CAAC,GAAG;AAChB,kBAAM,IAAI,MAAM,eAAe,IAAI,aAAa,QAAQ,2BAA2B;AAAA,UACrF;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,qBAAqB,CAAC,WAAWlG,UAAS;AAC9C,cAAM,IAAI,MAAM,oCAAoCA,QAAO,aAAa,KAAK,SAAS,EAAE,KAAK,IAAI,IAAI,aAAa;AAAA,MACpH;AACA,YAAM,aAAa,gBAAc;AAC/B,cAAM,SAAS,KAAK,UAAU;AAC9B,cAAM,OAAO,OAAO,QAAQ,CAAC,GAAG,MAAM;AACpC,iBAAO,IAAI,OAAO,SAAS,KAAK,MAAM,OAAO,IAAI;AAAA,QACnD,CAAC;AACD,aAAK,KAAK,OAAK;AACb,gBAAM,IAAI,MAAM,gBAAgB,IAAI,qDAAqD,OAAO,KAAK,IAAI,IAAI,IAAI;AAAA,QACnH,CAAC;AAAA,MACH;AAEA,YAAM,OAAO,CAAC,mBAAmB,aAAa;AAC5C,eAAO,SAAS,mBAAmB,UAAU;AAAA,UAC3C,UAAU;AAAA,UACV,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAAC,mBAAmB,UAAU,SAAS;AACtD,YAAI,SAAS,WAAW,GAAG;AACzB,gBAAM,IAAI,MAAM,+CAA+C;AAAA,QACjE;AACA,uBAAe,YAAY,QAAQ;AACnC,mBAAW,QAAQ;AACnB,eAAO,SAAO;AACZ,gBAAM,SAAS,KAAK,GAAG;AACvB,gBAAM,UAAU,OAAO,UAAU,SAAO;AACtC,mBAAO,WAAW,QAAQ,GAAG;AAAA,UAC/B,CAAC;AACD,cAAI,CAAC,SAAS;AACZ,uBAAW,UAAU,MAAM;AAAA,UAC7B;AACA,4BAAkB,UAAU,MAAM;AAClC,gBAAM,cAAc,SAAS,UAAU,CAAAO,SAAO;AAC5C,mBAAO,CAAC,KAAK,SAAS,IAAIA,OAAMA,IAAG;AAAA,UACrC,CAAC;AACD,cAAI,YAAY,SAAS,GAAG;AAC1B,+BAAmB,aAAa,KAAK,KAAK;AAAA,UAC5C;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,cAAc,CAAC,UAAU2F,UAAS;AACtC,cAAM,cAAc,SAASA,OAAM,CAAA3F,SAAO;AACxC,iBAAO,CAAC,WAAW,UAAUA,IAAG;AAAA,QAClC,CAAC;AACD,YAAI,YAAY,SAAS,GAAG;AAC1B,wBAAc,WAAW;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,UAAU,cAAY,KAAK,aAAa,QAAQ;AAEtD,YAAM,WAAW,QAAQ;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,WAAW,QAAQ;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,UAAU,QAAQ;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,YAAM,SAAS,MAAM;AACnB,YAAI,WAAW,SAAS,KAAK;AAC7B,cAAM,QAAQ,MAAM;AAClB,qBAAW,SAAS,KAAK;AAAA,QAC3B;AACA,cAAM4F,UAAS,CAAC,MAAMxF,QAAO;AAC3B,gBAAM,SAAS,SAAS,IAAI,SAAO;AACjC,mBAAO,KAAK,QAAQ,KAAKA,GAAE;AAAA,UAC7B,CAAC;AACD,qBAAW,SAAS,KAAKA,GAAE;AAC3B,iBAAO;AAAA,QACT;AACA,cAAM,UAAU,CAAC,OAAO,SAAS;AAC/B,gBAAM,aAAa,KAAK,QAAQ,KAAK;AACrC,qBAAW,KAAK,UAAQ;AACtB,kBAAM,SAASwF,QAAO,MAAM,IAAI;AAChC,mBAAO,KAAK,OAAK;AACf,qBAAO,QAAQ,KAAK,CAAC;AAAA,YACvB,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,cAAM,SAAS,SAAS,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;AACjD,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ,OAAO;AAAA,QACjB;AAAA,MACF;AAEA,YAAM,SAAS,MAAM;AACnB,cAAM,SAAS,SAAS,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;AACjD,eAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ,OAAO;AAAA,QACjB;AAAA,MACF;AAEA,YAAM,WAAW,MAAM;AACrB,cAAM,cAAc,OAAO;AAC3B,cAAM,cAAc,OAAO;AAC3B,YAAI,YAAY;AAChB,cAAMT,MAAK,MAAM;AACf,oBAAU,MAAM;AAChB,sBAAY;AAAA,QACd;AACA,cAAM,MAAM,MAAM;AAChB,oBAAU,MAAM;AAChB,sBAAY;AAAA,QACd;AACA,cAAM,UAAU,CAAC,OAAO,SAAS;AAC/B,oBAAU,QAAQ,OAAO,IAAI;AAAA,QAC/B;AACA,cAAM,OAAO,MAAM;AACjB,iBAAO,cAAc;AAAA,QACvB;AACA,eAAO;AAAA,UACL,IAAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAQ,YAAY;AAAA,QACtB;AAAA,MACF;AAEA,YAAM,QAAQ,CAAC,UAAU,MAAM,aAAa;AAC1C,YAAI,SAAS;AACb,cAAM,SAAS,SAAS;AAAA,UACtB,OAAO,MAAM,CAAC,CAAC;AAAA,UACf,MAAM,MAAM,CAAC,CAAC;AAAA,QAChB,CAAC;AACD,cAAM,WAAW,SAAS;AAC1B,cAAM,OAAO,MAAM;AACjB,UAAAU,MAAK,KAAK;AACV,cAAI,SAAS,KAAK,GAAG;AACnB,qBAAS,IAAI;AACb,mBAAO,QAAQ,KAAK;AAAA,UACtB;AAAA,QACF;AACA,cAAM,gBAAgB,KAAK,MAAM,GAAG;AACpC,cAAMC,MAAK,CAAA5F,YAAU;AACnB,UAAA2F,MAAK,MAAM3F,OAAM;AACjB,mBAAS,GAAG;AACZ,iBAAO,QAAQ,MAAM;AAAA,QACvB;AACA,cAAM,YAAY,WAAS;AACzB,wBAAc,OAAO;AACrB,mBAAS,QAAQ,OAAO,IAAI;AAAA,QAC9B;AACA,iBAAS,OAAO,KAAK,KAAK,WAAS;AACjC,eAAK,OAAO,UAAU,MAAM,IAAI;AAAA,QAClC,CAAC;AACD,cAAMiF,MAAK,MAAM;AACf,mBAAS;AAAA,QACX;AACA,cAAM,MAAM,MAAM;AAChB,mBAAS;AAAA,QACX;AACA,cAAM,WAAW,MAAM;AACvB,cAAM,cAAc,OAAK;AACvB,iBAAO,IAAI,SAAS;AAClB,gBAAI,QAAQ;AACV,gBAAE,MAAM,MAAM,IAAI;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AACA,cAAMU,QAAO,KAAK,KAAK,QAAQ;AAAA,UAC7B,WAAW;AAAA,UACX,MAAM,YAAY,IAAI;AAAA,UACtB,MAAM,YAAY,SAAS;AAAA,UAC3B,WAAW,YAAY,cAAc,QAAQ;AAAA,QAC/C,CAAC,GAAG,QAAQ;AACZ,cAAME,WAAU,MAAM;AACpB,UAAAF,MAAK,QAAQ;AAAA,QACf;AACA,eAAO;AAAA,UACL,SAASA,MAAK;AAAA,UACd,IAAAC;AAAA,UACA,IAAAX;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAAY;AAAA,UACA,QAAQ,OAAO;AAAA,QACjB;AAAA,MACF;AAEA,YAAM,MAAM,eAAa;AACvB,cAAM,gBAAgB,UAAU,QAAQ,OAAO,GAAG;AAClD,cAAMC,WAAU,SAAO;AACrB,iBAAO,gBAAgB,MAAM;AAAA,QAC/B;AACA,eAAO,EAAE,SAAAA,SAAQ;AAAA,MACnB;AAEA,YAAM,WAAW,IAAI,gBAAgB;AACrC,YAAM,YAAY,SAAS;AAE3B,YAAM,UAAU,aAAW;AACzB,cAAM,WAAW;AAAA,UACf,YAAY,UAAU,SAAS;AAAA,UAC/B,GAAG;AAAA,QACL;AACA,cAAM,MAAM,aAAa,QAAQ,KAAK;AACtC,cAAM,KAAK,QAAQ,cAAc;AACjC,eAAO,KAAK;AAAA,UACV,UAAU;AAAA,UACV,MAAM;AAAA,UACN,KAAK;AAAA,UACL,OAAO;AAAA,UACP,QAAQ;AAAA,QACV,CAAC;AACD,YAAI,KAAK,UAAU,SAAS,CAAC;AAC7B,YAAI,KAAK,SAAS,UAAU;AAC5B,cAAM,UAAU,SAAS,GAAG;AAC5B,cAAMD,WAAU,MAAM;AACpB,mBAAS,GAAG;AAAA,QACd;AACA,eAAO;AAAA,UACL;AAAA,UACA,SAAAA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,UAAU,CAAC,KAAK3F,QAAO;AAC3B,eAAO,cAAcA,IAAG,OAAO,IAAI,MAAMA,IAAG,MAAM,IAAI,GAAG;AAAA,MAC3D;AACA,YAAM,UAAU,WAAS;AACvB,eAAO,SAAS,KAAK,cAAc,MAAM,GAAG,MAAM,CAAC,CAAC;AAAA,MACtD;AACA,YAAM,SAAS,CAAC,UAAU,SAAS;AACjC,iBAAS,OAAO,KAAK,MAAM,KAAK,GAAG;AAAA,MACrC;AACA,YAAM,OAAO,CAAC,SAAS,aAAa;AAClC,cAAM,UAAU,QAAQ,QAAQ;AAChC,cAAM,QAAQ,KAAK,QAAQ,QAAQ,GAAG,aAAa,QAAQ,SAAS;AACpE,cAAM,MAAM,KAAK,QAAQ,QAAQ,GAAG,WAAW,QAAQ,IAAI;AAC3D,cAAM,QAAQ,KAAK,QAAQ,QAAQ,GAAG,aAAa,QAAQ,IAAI;AAC/D,cAAM,OAAO,KAAK,QAAQ,QAAQ,GAAG,YAAY,QAAQ,SAAS;AAClE,cAAM2F,WAAU,MAAM;AACpB,kBAAQ,QAAQ;AAChB,cAAI,OAAO;AACX,gBAAM,OAAO;AACb,eAAK,OAAO;AACZ,gBAAM,OAAO;AAAA,QACf;AACA,cAAM,QAAQ,CAAA7F,YAAU;AACtB,mBAASA,SAAQ,QAAQ,QAAQ,CAAC;AAAA,QACpC;AACA,cAAM,OAAO,MAAM;AACjB,mBAAS,QAAQ,QAAQ,CAAC;AAAA,QAC5B;AACA,eAAO,SAAS;AAAA,UACd,SAAS,QAAQ;AAAA,UACjB;AAAA,UACA;AAAA,UACA,SAAA6F;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,YAAY,SAAS;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,YAAM,YAAY,CAAC,UAAU,WAAW,CAAC,MAAM;AAC7C,YAAI;AACJ,cAAM,QAAQ,KAAK,SAAS,UAAU,QAAQ,OAAO,SAAS,KAAK;AACnE,eAAO,MAAM,UAAU,MAAM,QAAQ;AAAA,MACvC;AAEA,YAAM,SAAS,IAAI,eAAe;AAClC,YAAM,UAAU,OAAO;AAEvB,YAAM,WAAW,MAAM;AACrB,cAAM,SAAS,SAAS;AAAA,UACtB,MAAM,MAAM;AAAA,YACV;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,cAAMjD,UAAS,CAAC,GAAG,MAAM;AACvB,iBAAO,QAAQ,KAAK,GAAG,CAAC;AAAA,QAC1B;AACA,eAAO;AAAA,UACL,QAAAA;AAAA,UACA,QAAQ,OAAO;AAAA,QACjB;AAAA,MACF;AAEA,YAAM,cAAc,MAAM;AACxB,cAAM,SAAS,SAAS;AAAA,UACtB,MAAM,MAAM;AAAA,YACV;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,YAAI,SAAS,SAAS,KAAK;AAC3B,cAAM,WAAW,SAAS;AAC1B,iBAAS,OAAO,KAAK,KAAK,WAAS;AACjC,iBAAO,KAAK,OAAK;AACf,mBAAO,QAAQ,KAAK,MAAM,QAAQ,MAAM,QAAQ,CAAC;AAAA,UACnD,CAAC;AAAA,QACH,CAAC;AACD,cAAM,SAAS,OAAK;AAClB,mBAAS,SAAS,KAAK,CAAC;AAAA,QAC1B;AACA,cAAMtB,OAAM,MAAM;AAChB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,UACL;AAAA,UACA,KAAAA;AAAA,UACA,QAAQ,SAAS;AAAA,UACjB,QAAQ,OAAO;AAAA,QACjB;AAAA,MACF;AAEA,YAAM,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG,MAAM;AAClC,cAAM,MAAM,aAAa,QAAQ,KAAK;AACtC,eAAO,KAAK;AAAA,UACV,UAAU;AAAA,UACV,MAAM,IAAI,IAAI,IAAI;AAAA,UAClB,KAAK,IAAI;AAAA,UACT,QAAQ,IAAI;AAAA,UACZ,OAAO,IAAI;AAAA,QACb,CAAC;AACD,iBAAS,KAAK;AAAA,UACZ,eAAe;AAAA,UACf,QAAQ;AAAA,QACV,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,MAAM,CAAC3B,IAAG,GAAG,GAAG,GAAG,MAAM;AAC7B,cAAM,MAAM,aAAa,QAAQ,KAAK;AACtC,eAAO,KAAK;AAAA,UACV,UAAU;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,IAAI,IAAI,IAAI;AAAA,UACjB,QAAQ,IAAI;AAAA,UACZ,OAAO,IAAI;AAAA,QACb,CAAC;AACD,iBAAS,KAAK;AAAA,UACZ,YAAYA;AAAA,UACZ,QAAQ;AAAA,QACV,CAAC;AACD,eAAO;AAAA,MACT;AAEA,YAAM,YAAY,QAAQ,aAAa;AACvC,YAAM,eAAe,QAAQ,cAAc;AAC3C,YAAM,eAAe,QAAQ,cAAc;AAC3C,YAAM,gBAAgB;AACtB,YAAM,gBAAgB,CAAC,WAAWoG,iBAAgB,OAAO,UAAU,KAAK,CAAC1F,MAAK,MAAM0F,aAAY1F,KAAI,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvH,YAAM,mBAAmB,CAAC,WAAW0F,iBAAgB;AACnD,cAAM,gBAAgB,CAAC;AACvB,gBAAQ,UAAU,KAAK,SAAS,WAAS;AACvC,gBAAM,YAAY,UAAU,YAAY,WAAW,KAAK,EAAE,IAAI,CAAAjE,SAAOA,KAAI,OAAO;AAChF,cAAI,UAAU,OAAOiE,YAAW,GAAG;AACjC,0BAAc,KAAK,KAAK;AAAA,UAC1B;AAAA,QACF,CAAC;AACD,eAAO,SAAS,eAAe,cAAY;AACzC,gBAAM,cAAc,UAAU,YAAY,WAAW,CAAAhF,UAAQA,MAAK,WAAW,QAAQ;AACrF,iBAAO,OAAO,aAAa,CAAAA,UAAQgF,aAAYhF,MAAK,OAAO,CAAC;AAAA,QAC9D,CAAC;AAAA,MACH;AACA,YAAM,UAAU,UAAQ;AACtB,cAAM,WAAW,YAAY,KAAK,OAAO,GAAG,MAAM,SAAS;AAC3D,eAAO,UAAU,QAAQ;AAAA,MAC3B;AACA,YAAM,UAAU,CAAC,MAAM,WAAWiF,YAAW;AAC3C,cAAM,SAAS,KAAK,OAAO;AAC3B,eAAO,WAAW,cAAY;AAC5B,mBAAS,KAAK,QAAM;AAClB,kBAAM,MAAMA,QAAO,QAAQ,EAAE;AAC7B,gBAAI,KAAK,SAAS;AAClB,qBAAS,KAAK,OAAO,GAAG,GAAG;AAAA,UAC7B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,MAAM,cAAc,UAAU,gBAAgB;AAChE,gBAAQ,MAAM,cAAc,CAAC,QAAQ,OAAO;AAC1C,gBAAM,SAAS,IAAI,GAAG,KAAK,GAAG,IAAI,OAAO,MAAM,SAAS,MAAM,OAAO,KAAK,eAAe,WAAW;AACpG,cAAI,QAAQ,YAAY;AACxB,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,YAAM,aAAa,CAAC,MAAM,cAAc,UAAU,eAAe;AAC/D,gBAAQ,MAAM,cAAc,CAAC,QAAQ,OAAO;AAC1C,gBAAM,SAAS,IAAI,GAAG,KAAK,SAAS,OAAO,OAAO,MAAM,GAAG,IAAI,OAAO,KAAK,YAAY,aAAa;AACpG,cAAI,QAAQ,YAAY;AACxB,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,YAAM,cAAc,CAAC,UAAU,MAAM/E,QAAOd,OAAM,SAAS;AACzD,cAAM,WAAW,SAASc,MAAK;AAC/B,cAAM8E,eAAc,KAAK;AACzB,cAAM,eAAe5F,MAAK,SAAS,IAAI,OAAO,UAAUA,OAAMc,MAAK,IAAI,CAAC;AACxE,cAAM,mBAAmB,aAAa,SAAS,IAAI,cAAc,UAAU8E,YAAW,IAAI,CAAC;AAC3F,cAAM,wBAAwB,SAAS,cAAc,CAAC,MAAM,MAAM,OAAO,kBAAkB,cAAY,MAAM,QAAQ,CAAC;AACtH,mBAAW,MAAM,uBAAuB,UAAU,WAAW9E,MAAK,CAAC;AACnE,cAAM,eAAe,KAAK,SAAS,IAAI,MAAM,UAAU,MAAMA,MAAK,IAAI,CAAC;AACvE,cAAM,mBAAmB,aAAa,SAAS,IAAI,iBAAiB,UAAU8E,YAAW,IAAI,CAAC;AAC9F,cAAM,wBAAwB,SAAS,cAAc,CAAC,MAAM,MAAM,OAAO,kBAAkB,cAAY,MAAM,QAAQ,CAAC;AACtH,mBAAW,MAAM,uBAAuB,UAAU,WAAW9E,MAAK,CAAC;AAAA,MACrE;AACA,YAAM,UAAU,CAAC,MAAMA,WAAU;AAC/B,gBAAQ,IAAI;AACZ,YAAI,KAAK,YAAYA,MAAK,GAAG;AAC3B,gBAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,gBAAMqB,UAAS,KAAK,SAAS;AAC7B,gBAAM,OAAO,QAAQ,SAAS;AAC9B,sBAAY,WAAW,MAAMrB,QAAOqB,SAAQ,IAAI;AAAA,QAClD;AAAA,MACF;AACA,YAAM,OAAO,CAAC,MAAM,MAAM;AACxB,cAAM,OAAO,YAAY,KAAK,OAAO,GAAG,MAAM,SAAS;AACvD,eAAO,MAAM,CAAC;AAAA,MAChB;AACA,YAAM,OAAO,UAAQ;AACnB,aAAK,MAAM,SAAO;AAChB,gBAAM,KAAK,WAAW,MAAM;AAAA,QAC9B,CAAC;AAAA,MACH;AACA,YAAM,OAAO,UAAQ;AACnB,aAAK,MAAM,SAAO;AAChB,gBAAM,KAAK,WAAW,OAAO;AAAA,QAC/B,CAAC;AAAA,MACH;AACA,YAAM,WAAW,aAAW;AAC1B,eAAO,IAAI,SAAS,YAAY;AAAA,MAClC;AACA,YAAM,WAAW,aAAW;AAC1B,eAAO,IAAI,SAAS,YAAY;AAAA,MAClC;AAEA,YAAM,oBAAoB,QAAQ,sBAAsB;AACxD,YAAM,aAAa,UAAQ;AACzB,cAAM,WAAW,YAAY;AAC7B,cAAM,WAAW,UAAU,UAAU,CAAC,CAAC;AACvC,YAAI,aAAa,SAAS,KAAK;AAC/B,cAAM,aAAa,CAAC,SAAS/C,UAAS;AACpC,iBAAO,SAAS,KAAK,MAAM,SAASA,KAAI,CAAC;AAAA,QAC3C;AACA,iBAAS,OAAO,KAAK,KAAK,WAAS;AACjC,qBAAW,MAAM,QAAQ,UAAU,EAAE,KAAK,cAAY;AACpD,kBAAM,aAAa,YAAY,MAAM,QAAQ,KAAK;AAClD,kBAAM,MAAM,QAAQ,OAAO,aAAa,MAAM,SAAS,IAAI;AAAA,UAC7D,CAAC;AACD,qBAAW,MAAM,QAAQ,aAAa,EAAE,KAAK,cAAY;AACvD,kBAAM,aAAa,YAAY,MAAM,QAAQ,MAAM;AACnD,kBAAM,MAAM,QAAQ,QAAQ,aAAa,MAAM,SAAS,IAAI;AAAA,UAC9D,CAAC;AAAA,QACH,CAAC;AACD,cAAM,WAAW,CAAC,QAAQ,QAAQ;AAChC,gBAAM,OAAO,YAAY,QAAQ,GAAG;AACpC,gBAAM,OAAO,aAAa,QAAQ,kBAAkB,KAAK,CAAC;AAC1D,iBAAO,OAAO;AAAA,QAChB;AACA,iBAAS,OAAO,KAAK,KAAK,MAAM;AAC9B,mBAAS,IAAI,EAAE,KAAK,YAAU;AAC5B,uBAAW,KAAK,CAAA0B,WAAS;AACvB,yBAAW,QAAQ,UAAU,EAAE,KAAK,CAAAZ,SAAO;AACzC,sBAAM,QAAQ,SAAS,QAAQ,KAAK;AACpC,yBAAS,QAAQ,kBAAkB;AACnC,uBAAO,QAAQ,aAAaY,QAAO,OAAO,SAASZ,MAAK,EAAE,CAAC;AAAA,cAC7D,CAAC;AACD,yBAAW,QAAQ,aAAa,EAAE,KAAK,YAAU;AAC/C,sBAAM,QAAQ,SAAS,QAAQ,MAAM;AACrC,yBAAS,QAAQ,mBAAmB;AACpC,uBAAO,QAAQ,YAAYY,QAAO,OAAO,SAAS,QAAQ,EAAE,CAAC;AAAA,cAC/D,CAAC;AACD,sBAAQ,MAAMA,MAAK;AAAA,YACrB,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AACD,cAAM,UAAU,CAAC,QAAQ,QAAQ;AAC/B,iBAAO,QAAQ,YAAY;AAC3B,mBAAS,OAAO,MAAM;AACtB,gBAAM,QAAQ,kBAAkB,KAAK,YAAY,QAAQ,GAAG,CAAC;AAC7D,cAAI,QAAQ,iBAAiB;AAC7B,gBAAM,QAAQ,WAAW,KAAK;AAC9B,mBAAS,GAAG,KAAK,OAAO,CAAC;AAAA,QAC3B;AACA,cAAM,YAAY,KAAK,KAAK,OAAO,GAAG,aAAa,WAAS;AAC1D,cAAI,SAAS,MAAM,MAAM,GAAG;AAC1B,oBAAQ,MAAM,QAAQ,KAAK;AAAA,UAC7B;AACA,cAAI,SAAS,MAAM,MAAM,GAAG;AAC1B,oBAAQ,MAAM,QAAQ,MAAM;AAAA,UAC9B;AAAA,QACF,CAAC;AACD,cAAM,SAAS,OAAK;AAClB,iBAAO,KAAK,GAAG,KAAK,KAAK,CAAC;AAAA,QAC5B;AACA,cAAM,2BAA2B,YAAU,UAAU,QAAQ,SAAS,MAAM,EAAE,OAAO,YAAY;AACjG,cAAM,YAAY,KAAK,KAAK,KAAK,GAAG,aAAa,WAAS;AACxD,mCAAyB,MAAM,MAAM,EAAE,KAAK,MAAM;AAChD,gBAAI,OAAO,MAAM,MAAM,GAAG;AACxB,sBAAQ,IAAI;AAAA,YACd;AAAA,UACF,GAAG,CAAAA,WAAS;AACV,gBAAI,SAAS,SAAS,GAAG;AACvB,2BAAa,SAAS,KAAKA,MAAK;AAChC,sBAAQ,MAAMA,MAAK;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,cAAM,YAAY,MAAM;AACtB,oBAAU,OAAO;AACjB,oBAAU,OAAO;AACjB,mBAAS,QAAQ;AACjB,kBAAQ,IAAI;AAAA,QACd;AACA,cAAM,YAAY,SAAO;AACvB,kBAAQ,MAAM,GAAG;AAAA,QACnB;AACA,cAAM,SAAS,SAAS;AAAA,UACtB,cAAc,MAAM;AAAA,YAClB;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,aAAa,MAAM;AAAA,YACjB;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,aAAa,MAAM,CAAC,CAAC;AAAA,QACvB,CAAC;AACD,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,UACT,IAAI,SAAS;AAAA,UACb,KAAK,SAAS;AAAA,UACd,UAAU,MAAM,MAAM,IAAI;AAAA,UAC1B,UAAU,MAAM,MAAM,IAAI;AAAA,UAC1B,QAAQ,OAAO;AAAA,QACjB;AAAA,MACF;AAEA,YAAM,SAAS,CAAC,MAAM,UAAU,eAAe;AAC7C,cAAM,aAAa;AACnB,cAAM,aAAa;AACnB,cAAM,UAAU,WAAW,IAAI;AAC/B,cAAM,SAAS,SAAS;AAAA,UACtB,cAAc,MAAM;AAAA,YAClB;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,aAAa,MAAM;AAAA,YACjB;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD,WAAW,MAAM,CAAC,CAAC;AAAA,QACrB,CAAC;AACD,gBAAQ,OAAO,aAAa,KAAK,WAAS;AACxC,gBAAMA,SAAQ,MAAM;AACpB,iBAAO,QAAQ,aAAaA,QAAO,KAAK;AACxC,gBAAM,QAAQ,WAAW,MAAM,MAAM,OAAOA,MAAK;AACjD,uBAAaA,QAAO,OAAO,MAAM,KAAK,UAAU;AAChD,iBAAO,QAAQ,YAAYA,QAAO,KAAK;AAAA,QACzC,CAAC;AACD,gBAAQ,OAAO,YAAY,KAAK,YAAU;AACxC,iBAAO,QAAQ,UAAU;AAAA,QAC3B,CAAC;AACD,gBAAQ,OAAO,YAAY,KAAK,WAAS;AACvC,gBAAMA,SAAQ,MAAM;AACpB,iBAAO,QAAQ,aAAaA,QAAO,KAAK;AACxC,gBAAM,QAAQ,WAAW,MAAM,MAAM,OAAOA,MAAK;AACjD,gBAAM,YAAY,WAAWA,MAAK;AAClC,sBAAYA,QAAO,OAAO,MAAM,QAAQ,UAAU,SAAS;AAC3D,iBAAO,QAAQ,YAAYA,QAAO,KAAK;AAAA,QACzC,CAAC;AACD,eAAO;AAAA,UACL,IAAI,QAAQ;AAAA,UACZ,KAAK,QAAQ;AAAA,UACb,aAAa,QAAQ;AAAA,UACrB,UAAU,QAAQ;AAAA,UAClB,UAAU,QAAQ;AAAA,UAClB,SAAS,QAAQ;AAAA,UACjB,QAAQ,OAAO;AAAA,QACjB;AAAA,MACF;AACA,YAAM,cAAc,EAAE,OAAO;AAE7B,YAAM,OAAO,CAAC,SAAS8E,iBAAgB;AACrC,cAAM/F,UAAS,WAAW,OAAO,IAAI,gBAAgB,OAAO,IAAI;AAChE,eAAO;AAAA,UACL,QAAQ,SAASA,OAAM;AAAA,UACvB,MAAM,SAAS,OAAO;AAAA,UACtB,QAAQ,SAAS,cAAc,GAAG,CAAC,CAAC;AAAA,UACpC,aAAA+F;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW,CAAC,UAAU,QAAQA,iBAAgB;AAClD,cAAM,SAAS,MAAM,SAAS,MAAM;AACpC,eAAO;AAAA,UACL,QAAQ,SAAS,MAAM;AAAA,UACvB,MAAM,SAAS,QAAQ;AAAA,UACvB;AAAA,UACA,aAAAA;AAAA,QACF;AAAA,MACF;AACA,YAAM,OAAO,CAAC,UAAU,QAAQA,iBAAgB;AAC9C,eAAO;AAAA,UACL,QAAQ,SAAS,MAAM;AAAA,UACvB,MAAM,SAAS,QAAQ;AAAA,UACvB,QAAQ,SAAS,cAAc,GAAG,CAAC,CAAC;AAAA,UACpC,aAAAA;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,kBAAkB,MAAM;AAC5B,cAAM,YAAY,aAAa,QAAQ,KAAK;AAC5C,eAAO,WAAW;AAAA,UAChB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV,CAAC;AACD,iBAAS,OAAO,GAAG,SAAS;AAC5B,eAAO;AAAA,MACT;AACA,YAAM,MAAM,CAAC,QAAQA,iBAAgB;AACnC,eAAO,OAAO,SAAS,WAAW,KAAK,aAAa,QAAQ,OAAO,QAAQ,CAAC,GAAG,gBAAgB,GAAGA,YAAW,IAAI,WAAW,KAAK,aAAa,QAAQ,OAAO,OAAO,CAAC,GAAGA,YAAW;AAAA,MACrL;AACA,YAAM,SAAS,CAAC,QAAQ,SAAS;AAC/B,YAAI,OAAO,QAAQ;AACjB,mBAAS,KAAK,OAAO,CAAC;AAAA,QACxB;AAAA,MACF;AAEA,YAAM,UAAU,UAAQ,cAAc,IAAI,KAAK,KAAK,aAAa;AACjE,YAAM,mBAAmB;AACzB,YAAM,cAAc,SAAO,MAAM,KAAK,iBAAiB,MAAM;AAC7D,YAAM,aAAa,CAAA9E,WAAS;AAC1B,cAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,YAAI,CAAC,UAAU,WAAW,SAAS,GAAG;AACpC,iBAAO,QAAQA,MAAK,GAAG,CAAAF,UAAQ;AAC7B,kBAAM,gBAAgB,MAAMA,OAAM,OAAO;AACzC,kBAAMA,OAAM,SAAS,aAAa;AAClC,qBAASA,OAAM,OAAO;AAAA,UACxB,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,qBAAqB,YAAU;AACnC,cAAM,eAAe,MAAM;AAC3B,cAAM,cAAc,MAAM;AAC1B,cAAM,aAAa,MAAM;AACzB,YAAI;AACJ,YAAI;AACJ,cAAM,aAAa,CAAAE,WAAS,MAAM,QAAQA,MAAK;AAC/C,cAAM,wBAAwB,MAAM,8BAA8B,MAAM,IAAI,cAAc,IAAI,YAAY;AAC1G,cAAM,gBAAgB,CAAAA,WAAS,YAAYA,MAAK,EAAE;AAClD,cAAM,oBAAoB,CAACA,QAAO,QAAQO,WAAU;AAClD,gBAAM,oBAAoB,SAAS,QAAQ,GAAG;AAC9C,cAAI,cAAc,IAAI;AACpB,iCAAqBP,MAAK;AAAA,UAC5B;AACA,cAAIO,WAAU,UAAU,cAAc,IAAI;AACxC,kBAAMP,QAAO,SAAS,SAAS;AAC/B,kBAAM,WAAW,sBAAsB;AACvC,kBAAM,YAAY,WAAWA,MAAK;AAClC,kBAAMa,OAAM,8BAA8B,MAAM,KAAK,oBAAoB,cAAcb,MAAK,IAAI,IAAI;AACpG,wBAAYA,QAAOO,SAAQ,QAAQM,MAAK,UAAU,SAAS;AAAA,UAC7D,WAAW,eAAe,SAAS,GAAG;AACpC,kBAAM,WAAW,WAAW,UAAU,QAAQ,KAAK,EAAE,CAAC;AACtD,kBAAM,iBAAiBN,SAAQ,WAAW;AAC1C,kBAAMP,QAAO,SAAS,iBAAiB,GAAG;AAAA,UAC5C;AACA,cAAI,QAAQ,SAAS,GAAG;AACtB,uBAAWA,MAAK;AAAA,UAClB;AAAA,QACF;AACA,cAAM4E,WAAU,MAAM;AACpB,sBAAY,GAAG,QAAM;AACnB,eAAG,QAAQ;AAAA,UACb,CAAC;AACD,qBAAW,GAAG,OAAK;AACjB,mBAAO,QAAQ,CAAC;AAAA,UAClB,CAAC;AAAA,QACH;AACA,eAAO,GAAG,QAAQ,MAAM;AACtB,gBAAM,UAAU,IAAI,QAAQ,WAAW;AACvC,qBAAW,IAAI,OAAO;AACtB,cAAI,uBAAuB,MAAM,KAAK,mBAAmB,MAAM,GAAG;AAChE,kBAAM,WAAW,sBAAsB;AACvC,kBAAM,KAAK,YAAY,OAAO,SAAS,UAAU,UAAU;AAC3D,eAAG,GAAG;AACN,eAAG,OAAO,UAAU,KAAK,YAAU;AACjC,2BAAa,IAAI,OAAO,UAAU,OAAO,CAAC;AAAA,YAC5C,CAAC;AACD,eAAG,OAAO,aAAa,KAAK,WAAS;AACnC,oBAAM,WAAW,MAAM,MAAM;AAC7B,oCAAsB,QAAQ,UAAU,cAAc,QAAQ,GAAG,eAAe,QAAQ,GAAG,mBAAmB,MAAM,IAAI;AAAA,YAC1H,CAAC;AACD,eAAG,OAAO,YAAY,KAAK,WAAS;AAClC,oBAAM5E,SAAQ,MAAM;AACpB,oBAAM,WAAWA,OAAM;AACvB,8BAAgBA,MAAK;AACrB,2BAAa,GAAG,SAAO;AACrB,uBAAO,UAAU,OAAO,GAAG;AAC3B,uBAAO,MAAM;AAAA,cACf,CAAC;AACD,gCAAkB,QAAQ,UAAU,cAAc,QAAQ,GAAG,eAAe,QAAQ,GAAG,mBAAmB,MAAM,IAAI;AACpH,qBAAO,YAAY,IAAI;AAAA,YACzB,CAAC;AACD,wBAAY,IAAI,EAAE;AAAA,UACpB;AAAA,QACF,CAAC;AACD,eAAO,GAAG,qBAAqB,OAAK;AAClC,gBAAM,YAAY,EAAE;AACpB,cAAI,QAAQ,SAAS,GAAG;AACtB,kBAAMA,SAAQ,aAAa,QAAQ,SAAS;AAC5C,mBAAO,OAAO,IAAI,OAAO,sBAAsB,GAAG,CAAA0B,WAAS;AACzD,qBAAO,IAAI,SAASA,QAAO,SAAS,gCAAgC,MAAM,IAAI,UAAU;AAAA,YAC1F,CAAC;AACD,gBAAI,CAAC,cAAc1B,MAAK,KAAK,oBAAoB,MAAM,GAAG;AACxD,iCAAmBA,MAAK;AAAA,YAC1B,WAAW,CAAC,gBAAgBA,MAAK,KAAK,yBAAyB,MAAM,GAAG;AACtE,mCAAqBA,MAAK;AAAA,YAC5B;AACA,gBAAI,aAAaA,MAAK,KAAK,WAAW,EAAE,QAAQ,gBAAgB,GAAG;AACjE,mCAAqBA,MAAK;AAAA,YAC5B;AACA,qBAAS,EAAE;AACX,wBAAY,wBAAwB,MAAM,IAAI,KAAK,YAAY,QAAQ,SAAS,EAAE,MAAM,EAAE;AAAA,UAC5F;AAAA,QACF,CAAC;AACD,eAAO,GAAG,iBAAiB,OAAK;AAC9B,gBAAM,YAAY,EAAE;AACpB,cAAI,QAAQ,SAAS,GAAG;AACtB,kBAAMA,SAAQ,aAAa,QAAQ,SAAS;AAC5C,kBAAM,SAAS,EAAE;AACjB,gBAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,gCAAkBA,QAAO,QAAQ,EAAE,KAAK;AAAA,YAC1C;AACA,4BAAgBA,MAAK;AACrB,8BAAkB,QAAQA,OAAM,KAAK,aAAa;AAAA,UACpD;AAAA,QACF,CAAC;AACD,eAAO,GAAG,cAAc,MAAM;AAC5B,sBAAY,GAAG,CAAA8C,YAAU;AACvB,gBAAI,OAAO,KAAK,WAAW,GAAG;AAC5B,cAAAA,QAAO,SAAS;AAAA,YAClB,OAAO;AACL,cAAAA,QAAO,SAAS;AAAA,YAClB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,eAAO,GAAG,qBAAqB,OAAK;AAClC,sBAAY,GAAG,CAAAA,YAAU;AACvB,gBAAI,EAAE,SAAS,aAAa;AAC1B,cAAAA,QAAO,SAAS;AAChB,cAAAA,QAAO,IAAI;AAAA,YACb,OAAO;AACL,cAAAA,QAAO,GAAG;AACV,cAAAA,QAAO,SAAS;AAAA,YAClB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,eAAO,GAAG,UAAU,MAAM;AACxB,UAAA8B,SAAQ;AAAA,QACV,CAAC;AACD,cAAMI,WAAU,CAAAhF,WAAS;AACvB,sBAAY,GAAG,CAAA8C,YAAUA,QAAO,YAAY,aAAa,QAAQ9C,MAAK,CAAC,CAAC;AAAA,QAC1E;AACA,cAAMiF,QAAO,MAAM;AACjB,sBAAY,GAAG,CAAAnC,YAAUA,QAAO,SAAS,CAAC;AAAA,QAC5C;AACA,cAAMoC,QAAO,MAAM;AACjB,sBAAY,GAAG,CAAApC,YAAUA,QAAO,SAAS,CAAC;AAAA,QAC5C;AACA,eAAO;AAAA,UACL,SAAAkC;AAAA,UACA,MAAAC;AAAA,UACA,MAAAC;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa,YAAU;AAC3B,iBAAS,MAAM;AACf,cAAM,gBAAgB,mBAAmB,MAAM;AAC/C,cAAM,uBAAuB,0BAA0B,QAAQ,aAAa;AAC5E,cAAM,UAAU,aAAa,QAAQ,eAAe,oBAAoB;AACxE,yBAAiB,QAAQ,OAAO;AAChC,8BAAsB,QAAQ,OAAO;AACrC,uBAAe,QAAQ,OAAO;AAC9B,eAAO;AAAA,UACL,kBAAkB,qBAAqB;AAAA,UACvC,oBAAoB,qBAAqB;AAAA,QAC3C;AAAA,MACF;AAEA,YAAM,WAAW,YAAU;AACzB,cAAMlF,SAAQ,WAAW,MAAM;AAC/B,eAAO,EAAE,OAAAA,OAAM;AAAA,MACjB;AACA,UAAI,QAAQ,MAAM;AAChB,iBAAS,IAAI,OAAO,QAAQ;AAAA,MAC9B;AAEA,YAAM;AAAA,IAEV,GAAG;AAAA;AAAA;;;ACj2PH;", "names": ["type", "value", "all", "binder", "r", "copy", "name", "key", "base", "parent", "children", "nu", "rows", "columns", "row", "cells", "section", "is", "ancestor", "child", "selector", "left", "css", "styles", "cell", "fallback", "table", "mediaMatch", "browsers", "oses", "set", "get", "getOuter", "width", "before", "after", "grid", "cloneRow", "detail", "col", "translate", "body", "total", "filter", "getWidth", "columns$1", "getHeight", "rows$1", "zero", "getOption", "parents", "last", "clone", "mutate", "bounds", "first", "document", "isEmptyTag", "universe", "head", "prune", "path", "getIsRoot", "ephemera", "isTable", "resizeTable", "isHeaderCells", "sync", "colgroup", "extract", "error", "adt", "resize", "height", "add", "find", "unmerge", "merge", "isBr", "mergable", "unmergable", "editor", "cloneFormats", "getTableSectionType", "range", "values", "scan", "getRows", "selection", "clear", "on", "relative", "exact", "selectNode", "getStart", "_e", "bind", "unbind", "keys", "update", "sink", "go", "destroy", "resolve", "isResizable", "create", "refresh", "hide", "show"]}
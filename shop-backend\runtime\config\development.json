{"port": 8360, "workers": 1, "stickyCluster": false, "startServerTimeout": 3000, "reloadSignal": "SIGUSR2", "processKillTimeout": 10000, "jsonpCallbackField": "callback", "jsonContentType": "application/json", "jsonpContentType": "application/javascript", "errnoField": "errno", "errmsgField": "errmsg", "defaultErrno": 1000, "validateDefaultErrno": 1001, "userConfig": {"staticURL": "http://127.0.0.1:8360/"}, "cache": {"type": "file", "file": {"timeout": 86400000, "cachePath": "C:\\Users\\<USER>\\Desktop\\chapter08\\shop-backend\\runtime\\cache", "pathDepth": 1, "gcInterval": 86400000}}, "model": {"type": "mysql", "mysql": {"logConnect": true, "logSql": true, "database": "vueshop", "prefix": "vueshop_", "encoding": "utf8mb4", "host": "127.0.0.1", "port": "3306", "user": "root", "password": "123456", "dateStrings": true, "insecureAuth": true, "supportBigNumbers": true, "bigNumberStrings": true, "connectionLimit": 10, "acquireTimeout": 1000, "timeout": 1000, "reconnect": true}}, "session": {"type": "jwt", "jwt": {"cookie": {"name": "thinkjs"}, "secret": "《Vue.js前端开发实战（第2版）》教材配套项目secret", "tokenType": "header", "tokenName": "jwt", "sign": {}, "verify": {}}}, "view": {"type": "nunjucks", "nunjucks": {"viewPath": "C:\\Users\\<USER>\\Desktop\\chapter08\\shop-backend\\view", "sep": "_", "extname": ".html"}}, "logger": {"type": "console", "console": {}, "file": {"backups": 10, "absolute": true, "maxLogSize": 51200, "filename": "C:\\Users\\<USER>\\Desktop\\chapter08\\shop-backend\\logs\\app.log"}, "dateFile": {"level": "ALL", "absolute": true, "pattern": "-yyyy-MM-dd", "alwaysIncludePattern": true, "filename": "C:\\Users\\<USER>\\Desktop\\chapter08\\shop-backend\\logs\\app.log"}}}
{"version": 3, "sources": ["../../pinia-plugin-persist/dist/pinia-persist.es.js"], "sourcesContent": ["const updateStorage = (strategy, store) => {\n  const storage = strategy.storage || sessionStorage;\n  const storeKey = strategy.key || store.$id;\n  if (strategy.paths) {\n    const partialState = strategy.paths.reduce((finalObj, key) => {\n      finalObj[key] = store.$state[key];\n      return finalObj;\n    }, {});\n    storage.setItem(storeKey, JSON.stringify(partialState));\n  } else {\n    storage.setItem(storeKey, JSON.stringify(store.$state));\n  }\n};\nvar index = ({ options, store }) => {\n  var _a, _b, _c, _d;\n  if ((_a = options.persist) == null ? void 0 : _a.enabled) {\n    const defaultStrat = [{\n      key: store.$id,\n      storage: sessionStorage\n    }];\n    const strategies = ((_c = (_b = options.persist) == null ? void 0 : _b.strategies) == null ? void 0 : _c.length) ? (_d = options.persist) == null ? void 0 : _d.strategies : defaultStrat;\n    strategies.forEach((strategy) => {\n      const storage = strategy.storage || sessionStorage;\n      const storeKey = strategy.key || store.$id;\n      const storageResult = storage.getItem(storeKey);\n      if (storageResult) {\n        store.$patch(JSON.parse(storageResult));\n        updateStorage(strategy, store);\n      }\n    });\n    store.$subscribe(() => {\n      strategies.forEach((strategy) => {\n        updateStorage(strategy, store);\n      });\n    });\n  }\n};\nexport { index as default, updateStorage };\n"], "mappings": ";;;AAAA,IAAM,gBAAgB,CAAC,UAAU,UAAU;AACzC,QAAM,UAAU,SAAS,WAAW;AACpC,QAAM,WAAW,SAAS,OAAO,MAAM;AACvC,MAAI,SAAS,OAAO;AAClB,UAAM,eAAe,SAAS,MAAM,OAAO,CAAC,UAAU,QAAQ;AAC5D,eAAS,OAAO,MAAM,OAAO;AAC7B,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,YAAQ,QAAQ,UAAU,KAAK,UAAU,YAAY,CAAC;AAAA,EACxD,OAAO;AACL,YAAQ,QAAQ,UAAU,KAAK,UAAU,MAAM,MAAM,CAAC;AAAA,EACxD;AACF;AACA,IAAI,QAAQ,CAAC,EAAE,SAAS,MAAM,MAAM;AAClC,MAAI,IAAI,IAAI,IAAI;AAChB,OAAK,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,SAAS;AACxD,UAAM,eAAe,CAAC;AAAA,MACpB,KAAK,MAAM;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,UAAM,eAAe,MAAM,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,eAAe,OAAO,SAAS,GAAG,WAAW,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,aAAa;AAC7K,eAAW,QAAQ,CAAC,aAAa;AAC/B,YAAM,UAAU,SAAS,WAAW;AACpC,YAAM,WAAW,SAAS,OAAO,MAAM;AACvC,YAAM,gBAAgB,QAAQ,QAAQ,QAAQ;AAC9C,UAAI,eAAe;AACjB,cAAM,OAAO,KAAK,MAAM,aAAa,CAAC;AACtC,sBAAc,UAAU,KAAK;AAAA,MAC/B;AAAA,IACF,CAAC;AACD,UAAM,WAAW,MAAM;AACrB,iBAAW,QAAQ,CAAC,aAAa;AAC/B,sBAAc,UAAU,KAAK;AAAA,MAC/B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;", "names": []}